describe('Location Activity and Time Slot Management', () => {
  const eventName = `Test Event ${Date.now()}`;
  const locationName = `Test Arena ${Date.now()}`;
  let eventId: string;
  let locationId: string;

  beforeEach(() => {
    cy.visit('/');
    // Login as organizer (same as working test)
    cy.get('button').contains('Login').click();
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('Fritz0615@@');
    cy.get('[data-testid="signin-submit-button"]').click();
    cy.contains('Upcoming Events').should('be.visible');
  });

  it('should create event, add location, manage activities, and generate time slots', () => {
    // Step 1: Navigate to Organizer Dashboard (same as working test)
    cy.get('button').contains('Sally Organizer').click();
    cy.contains('Organizer Dashboard').click();
    cy.url().should('include', '/admin/organizer-dashboard');

    // Step 2: Create a new event for testing
    cy.get('button').contains('Create New Event').click();
    cy.url().should('include', '/admin/events/create');

    // Fill out event creation form
    cy.get('[data-testid="event-name-input"]').type(eventName);
    cy.get('[data-testid="event-start-date-input"]').type('2025-12-01');
    cy.get('[data-testid="event-end-date-input"]').type('2025-12-02');
    cy.get('[data-testid="create-event-button"]').click();

    // Wait for event creation and navigate to event details
    cy.contains('Event created successfully').should('be.visible');
    cy.contains(eventName).should('be.visible');
    
    // Click on the event to go to details
    cy.contains(eventName).click();
    cy.url().should('include', '/admin/events/');
    
    // Wait for the LocationManagement component to load
    cy.contains('Location Management').should('be.visible');

    // Step 3: Add a location to the event
    cy.get('[data-testid="add-location-button"]').click();
    cy.get('[data-testid="location-name-input"]').type(locationName);
    cy.contains('Select activity').click();
    cy.contains('Dressage').click();
    cy.get('[data-testid="description-input"]').type('Primary dressage competition ring');
    cy.get('[data-testid="create-location-button"]').click();
    
    // Wait for location creation
    cy.contains('Location created successfully').should('be.visible');
    cy.contains(locationName).should('be.visible');
    
    // Get location ID - find the location card and extract the ID
    cy.contains(locationName).closest('[data-testid^="location-card-"]').invoke('attr', 'data-testid').then((testId) => {
      if (testId) {
        locationId = testId.replace('location-card-', '');
      }
    });

    // Step 4: Expand location and click "Manage Activities/Schedule"
    cy.get(`[data-testid="location-card-${locationId}"]`).within(() => {
      // Click the expand button using the test ID
      cy.get(`[data-testid="expand-location-button-${locationId}"]`).click();
      
      // Wait for expansion and click Activities tab
      cy.get('[data-testid="activities-tab"]').click();
    });

    // Step 5: Add first activity
    cy.get('[data-testid="add-activity-button"]').click();
    cy.get('[data-testid="activity-date-input"]').type('2025-12-01');
    cy.get('[data-testid="start-time-input"]').type('09:00');
    cy.get('[data-testid="end-time-input"]').type('10:00');
    cy.get('[data-testid="slot-duration-input"]').type('30');
    cy.get('[data-testid="description-input"]').type('Morning dressage session');
    cy.get('[data-testid="create-activity-button"]').click();
    
    // Wait for activity creation
    cy.contains('Morning dressage session').should('be.visible');

    // Step 6: Add second activity
    cy.get('[data-testid="add-activity-button"]').click();
    cy.get('[data-testid="activity-date-input"]').type('2025-12-01');
    cy.get('[data-testid="start-time-input"]').type('10:30');
    cy.get('[data-testid="end-time-input"]').type('11:30');
    cy.get('[data-testid="slot-duration-input"]').type('30');
    cy.get('[data-testid="description-input"]').type('Afternoon dressage session');
    cy.get('[data-testid="create-activity-button"]').click();
    
    // Wait for second activity creation
    cy.contains('Afternoon dressage session').should('be.visible');

    // Step 7: View the schedule
    cy.get('[data-testid="schedule-tab"]').click();
    
    // Verify schedule shows both activities
    cy.contains('Morning dressage session').should('be.visible');
    cy.contains('Afternoon dressage session').should('be.visible');

    // Step 8: Generate time slots
    cy.get('[data-testid="timeslots-tab"]').click();
    
    // Click generate time slots button
    cy.get('[data-testid="generate-time-slots-btn"]').click();
    
    // Confirm generation in dialog
    cy.get('[data-testid="confirm-action-btn"]').click();
    
    // Wait for time slots to be generated
    cy.get('[data-testid^="time-slot-"]').should('have.length.at.least', 2);
    
    // Verify time slots are available
    cy.get('[data-testid^="slot-status-"]').each(($status) => {
      cy.wrap($status).should('contain', 'Available');
    });
  });

  afterEach(() => {
    // Clean up: Delete the event (which will cascade delete location, activities, and time slots)
    if (eventName) {
      // Navigate to events list page to find the event
      cy.visit('/admin/events');
      
      // Wait for the event to appear and click the delete button (trash icon)
      cy.get('h3').contains(eventName, { timeout: 20000 }).should('be.visible');
      
      // Find the event card and click the delete button (trash icon)
      cy.get('h3').contains(eventName).closest('.border-green-200').find('button').last().click();
      
      // Confirm deletion in the dialog
      cy.get('[data-testid="delete-event-confirm-button"]')
        .should('be.visible')
        .and('not.be.disabled');
      cy.wait(300);
      cy.get('[data-testid="delete-event-confirm-button"]').click({ force: true });
      
      // Assert the success toast appears
      cy.contains('Event deleted successfully', { timeout: 10000 }).should('be.visible');
      cy.get('[role="dialog"]').should('not.exist');
      cy.get('h3').contains(eventName, { timeout: 20000 }).should('not.exist');
    }
  });
}); 