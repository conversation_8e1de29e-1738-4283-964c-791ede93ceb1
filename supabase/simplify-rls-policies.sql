-- Simplify RLS policies - Remove all existing policies and create minimal working set
-- This will fix the RLS conflicts by starting fresh

-- First, let's see what policies currently exist
SELECT 'Current policies before cleanup:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename IN ('events', 'event_levels', 'activities', 'time_slots', 'locations', 'levels_library')
ORDER BY tablename, policyname;

-- Drop ALL existing policies on these tables
SELECT 'Dropping all existing policies...' as info;

-- Events table
DROP POLICY IF EXISTS "Enable read access for all users" ON events;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON events;
DROP POLICY IF EXISTS "Enable update for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable delete for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable all access for service role" ON events;
DROP POLICY IF EXISTS "Enable read access for authenticated users only" ON events;
DROP POLICY IF EXISTS "Enable insert for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable update for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable delete for users based on organizer_id" ON events;

-- Event levels table
DROP POLICY IF EXISTS "Enable read access for all users" ON event_levels;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON event_levels;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON event_levels;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON event_levels;
DROP POLICY IF EXISTS "Enable all access for service role" ON event_levels;

-- Activities table
DROP POLICY IF EXISTS "Enable read access for all users" ON activities;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON activities;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON activities;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON activities;
DROP POLICY IF EXISTS "Enable all access for service role" ON activities;

-- Time slots table
DROP POLICY IF EXISTS "Enable read access for all users" ON time_slots;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON time_slots;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON time_slots;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON time_slots;
DROP POLICY IF EXISTS "Enable all access for service role" ON time_slots;

-- Locations table
DROP POLICY IF EXISTS "Enable read access for all users" ON locations;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON locations;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON locations;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON locations;
DROP POLICY IF EXISTS "Enable all access for service role" ON locations;

-- Levels library table
DROP POLICY IF EXISTS "Enable read access for all users" ON levels_library;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON levels_library;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON levels_library;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON levels_library;
DROP POLICY IF EXISTS "Enable all access for service role" ON levels_library;

-- Now create SIMPLE, MINIMAL policies that actually work

-- 1. EVENTS TABLE - Simple policies
SELECT 'Creating simple events policies...' as info;
CREATE POLICY "events_select_policy" ON events FOR SELECT USING (true);
CREATE POLICY "events_insert_policy" ON events FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "events_update_policy" ON events FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "events_delete_policy" ON events FOR DELETE USING (auth.role() = 'authenticated');

-- 2. EVENT_LEVELS TABLE - Simple policies
SELECT 'Creating simple event_levels policies...' as info;
CREATE POLICY "event_levels_select_policy" ON event_levels FOR SELECT USING (true);
CREATE POLICY "event_levels_insert_policy" ON event_levels FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "event_levels_update_policy" ON event_levels FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "event_levels_delete_policy" ON event_levels FOR DELETE USING (auth.role() = 'authenticated');

-- 3. ACTIVITIES TABLE - Simple policies
SELECT 'Creating simple activities policies...' as info;
CREATE POLICY "activities_select_policy" ON activities FOR SELECT USING (true);
CREATE POLICY "activities_insert_policy" ON activities FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "activities_update_policy" ON activities FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "activities_delete_policy" ON activities FOR DELETE USING (auth.role() = 'authenticated');

-- 4. TIME_SLOTS TABLE - Simple policies
SELECT 'Creating simple time_slots policies...' as info;
CREATE POLICY "time_slots_select_policy" ON time_slots FOR SELECT USING (true);
CREATE POLICY "time_slots_insert_policy" ON time_slots FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "time_slots_update_policy" ON time_slots FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "time_slots_delete_policy" ON time_slots FOR DELETE USING (auth.role() = 'authenticated');

-- 5. LOCATIONS TABLE - Simple policies
SELECT 'Creating simple locations policies...' as info;
CREATE POLICY "locations_select_policy" ON locations FOR SELECT USING (true);
CREATE POLICY "locations_insert_policy" ON locations FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "locations_update_policy" ON locations FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "locations_delete_policy" ON locations FOR DELETE USING (auth.role() = 'authenticated');

-- 6. LEVELS_LIBRARY TABLE - Simple policies
SELECT 'Creating simple levels_library policies...' as info;
CREATE POLICY "levels_library_select_policy" ON levels_library FOR SELECT USING (true);
CREATE POLICY "levels_library_insert_policy" ON levels_library FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "levels_library_update_policy" ON levels_library FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "levels_library_delete_policy" ON levels_library FOR DELETE USING (auth.role() = 'authenticated');

-- Verify the new simple policies
SELECT 'New simplified policies:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename IN ('events', 'event_levels', 'activities', 'time_slots', 'locations', 'levels_library')
ORDER BY tablename, policyname;

-- Test basic access
SELECT 'Testing events access...' as info;
SELECT COUNT(*) as event_count FROM events;

SELECT 'Testing locations access...' as info;
SELECT COUNT(*) as location_count FROM locations; 