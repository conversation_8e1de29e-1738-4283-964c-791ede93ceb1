-- Fix RLS policies for events table
-- This script will enable proper access to events for admin/organizer users

-- First, let's check current RLS policies on events table
SELECT 'Current RLS policies on events table:' as info;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'events';

-- Check if RLS is enabled on events table
SELECT 'RLS status on events table:' as info;
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename = 'events';

-- Drop existing policies if they exist (to recreate them properly)
DROP POLICY IF EXISTS "Enable read access for all users" ON events;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON events;
DROP POLICY IF EXISTS "Enable update for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable delete for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable all access for service role" ON events;

-- Create new RLS policies for events table

-- 1. Allow public read access to active events
CREATE POLICY "Enable read access for all users" ON events
    FOR SELECT
    USING (is_active = true);

-- 2. Allow authenticated users to create events (admin/organizer)
CREATE POLICY "Enable insert for authenticated users only" ON events
    FOR INSERT
    WITH CHECK (auth.role() = 'authenticated');

-- 3. Allow users to update their own events (based on organizer_id if it exists)
-- If organizer_id column doesn't exist, this will still work for admin users
CREATE POLICY "Enable update for users based on organizer_id" ON events
    FOR UPDATE
    USING (
        auth.role() = 'authenticated' AND 
        (organizer_id IS NULL OR organizer_id = auth.uid())
    );

-- 4. Allow users to delete their own events
CREATE POLICY "Enable delete for users based on organizer_id" ON events
    FOR DELETE
    USING (
        auth.role() = 'authenticated' AND 
        (organizer_id IS NULL OR organizer_id = auth.uid())
    );

-- 5. Allow service role full access (for Edge Functions)
CREATE POLICY "Enable all access for service role" ON events
    FOR ALL
    USING (auth.role() = 'service_role');

-- Verify the policies were created
SELECT 'New RLS policies on events table:' as info;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'events';

-- Test query to verify access
SELECT 'Testing events access:' as info;
SELECT 
    id,
    name,
    event_type,
    is_active,
    created_at
FROM events 
LIMIT 5; 