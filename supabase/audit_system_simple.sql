-- Simple Audit System for Horse Trials Booking Hub
-- Single audit_logs table for all audit records

-- 1. Create single audit table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name TEXT NOT NULL,
    record_id UUID NOT NULL,
    action TEXT NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    user_id UUID NOT NULL,
    user_role TEXT,
    user_email TEXT,
    ip_address INET,
    user_agent TEXT,
    reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    session_id TEXT
);

-- 2. Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_record ON audit_logs(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_name ON audit_logs(table_name);

-- 3. Function to get current user context
CREATE OR REPLACE FUNCTION get_audit_context()
RETURNS TABLE(
    user_id UUID,
    user_role TEXT,
    user_email TEXT,
    ip_address INET,
    user_agent TEXT,
    session_id TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        auth.uid() as user_id,
        ur.role::TEXT as user_role,
        p.email as user_email,
        NULL::INET as ip_address, -- Would need to be passed from application
        NULL::TEXT as user_agent, -- Would need to be passed from application
        NULL::TEXT as session_id  -- Would need to be passed from application
    FROM user_roles ur
    LEFT JOIN profiles p ON p.id = auth.uid()
    WHERE ur.user_id = auth.uid()
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Generic audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
    context_record RECORD;
    old_data JSONB;
    new_data JSONB;
    changed_fields TEXT[];
BEGIN
    -- Get user context
    SELECT * INTO context_record FROM get_audit_context();
    
    -- Prepare old and new data
    IF TG_OP = 'DELETE' THEN
        old_data := to_jsonb(OLD);
        new_data := NULL;
    ELSIF TG_OP = 'INSERT' THEN
        old_data := NULL;
        new_data := to_jsonb(NEW);
    ELSIF TG_OP = 'UPDATE' THEN
        old_data := to_jsonb(OLD);
        new_data := to_jsonb(NEW);
        
        -- Calculate changed fields
        SELECT array_agg(key) INTO changed_fields
        FROM (
            SELECT key FROM jsonb_object_keys(to_jsonb(NEW))
            EXCEPT
            SELECT key FROM jsonb_object_keys(to_jsonb(OLD))
            UNION
            SELECT key FROM (
                SELECT key, value FROM jsonb_each(to_jsonb(NEW))
                EXCEPT
                SELECT key, value FROM jsonb_each(to_jsonb(OLD))
            ) AS changed_values
        ) AS changed_keys;
    END IF;
    
    -- Insert audit record
    INSERT INTO audit_logs (
        table_name, record_id, action, old_values, new_values, 
        changed_fields, user_id, user_role, user_email, 
        ip_address, user_agent, reason, session_id
    ) VALUES (
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        old_data,
        new_data,
        changed_fields,
        context_record.user_id,
        context_record.user_role,
        context_record.user_email,
        context_record.ip_address,
        context_record.user_agent,
        current_setting('app.audit_reason', true),
        context_record.session_id
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create triggers for existing tables
DROP TRIGGER IF EXISTS audit_events_trigger ON events;
CREATE TRIGGER audit_events_trigger
    AFTER INSERT OR UPDATE OR DELETE ON events
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

DROP TRIGGER IF EXISTS audit_time_slots_trigger ON time_slots;
CREATE TRIGGER audit_time_slots_trigger
    AFTER INSERT OR UPDATE OR DELETE ON time_slots
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

DROP TRIGGER IF EXISTS audit_bookings_trigger ON bookings;
CREATE TRIGGER audit_bookings_trigger
    AFTER INSERT OR UPDATE OR DELETE ON bookings
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

DROP TRIGGER IF EXISTS audit_booking_slots_trigger ON booking_slots;
CREATE TRIGGER audit_booking_slots_trigger
    AFTER INSERT OR UPDATE OR DELETE ON booking_slots
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- 6. Function to set audit reason (called from application)
CREATE OR REPLACE FUNCTION set_audit_reason(reason_text TEXT)
RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.audit_reason', reason_text, false);
END;
$$ LANGUAGE plpgsql;

-- 7. Function to get audit trail for a specific record
CREATE OR REPLACE FUNCTION get_audit_trail(
    p_table_name TEXT,
    p_record_id UUID,
    p_limit INTEGER DEFAULT 100
)
RETURNS TABLE(
    id UUID,
    table_name TEXT,
    record_id UUID,
    action TEXT,
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    user_id UUID,
    user_role TEXT,
    user_email TEXT,
    ip_address INET,
    user_agent TEXT,
    reason TEXT,
    created_at TIMESTAMPTZ,
    session_id TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        id, table_name, record_id, action, old_values, new_values,
        changed_fields, user_id, user_role, user_email,
        ip_address, user_agent, reason, created_at, session_id
    FROM audit_logs
    WHERE table_name = p_table_name AND record_id = p_record_id
    ORDER BY created_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Function to get audit trail for a user
CREATE OR REPLACE FUNCTION get_user_audit_trail(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 100
)
RETURNS TABLE(
    id UUID,
    table_name TEXT,
    record_id UUID,
    action TEXT,
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    user_id UUID,
    user_role TEXT,
    user_email TEXT,
    ip_address INET,
    user_agent TEXT,
    reason TEXT,
    created_at TIMESTAMPTZ,
    session_id TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        id, table_name, record_id, action, old_values, new_values,
        changed_fields, user_id, user_role, user_email,
        ip_address, user_agent, reason, created_at, session_id
    FROM audit_logs
    WHERE user_id = p_user_id
    ORDER BY created_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. RLS policies for audit table (only super admins can access)
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Only super admins can read audit data
CREATE POLICY "super_admin_audit_access" ON audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_roles 
            WHERE user_roles.user_id = auth.uid() 
            AND user_roles.role = 'super_admin'
        )
    );

-- 10. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated; 