-- Debug time slot generation issues
-- Check activities, time_slots, and RLS policies

-- 1. Check activities table
SELECT 'Activities table data:' as info;
SELECT 
    id,
    location_id,
    activity_type,
    start_time,
    end_time,
    slot_duration_minutes,
    level,
    created_at
FROM activities 
ORDER BY created_at DESC;

-- 2. Check time_slots table
SELECT 'Time slots table data:' as info;
SELECT 
    id,
    location_id,
    start_time,
    end_time,
    is_booked,
    activity_id,
    created_at
FROM time_slots 
ORDER BY created_at DESC;

-- 3. Check RLS policies on activities table
SELECT 'Activities table RLS policies:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'activities';

-- 4. Check RLS policies on time_slots table
SELECT 'Time slots table RLS policies:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'time_slots';

-- 5. Check if RLS is enabled on these tables
SELECT 'RLS status on tables:' as info;
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename IN ('activities', 'time_slots');

-- 6. Test basic access to activities
SELECT 'Testing activities access:' as info;
SELECT COUNT(*) as activities_count FROM activities;

-- 7. Test basic access to time_slots
SELECT 'Testing time_slots access:' as info;
SELECT COUNT(*) as time_slots_count FROM time_slots;

-- 8. Check locations table (needed for time slot generation)
SELECT 'Locations table data:' as info;
SELECT 
    id,
    name,
    activity_type,
    fixed_level,
    created_at
FROM locations 
ORDER BY created_at DESC; 