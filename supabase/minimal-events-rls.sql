-- Minimal RLS fix for events table only
-- This removes all existing policies and creates just the essential ones

-- First, let's see what policies currently exist on events
SELECT 'Current events policies:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'events';

-- Drop ALL existing policies on events table
SELECT 'Dropping all existing events policies...' as info;
DROP POLICY IF EXISTS "Enable read access for all users" ON events;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON events;
DROP POLICY IF EXISTS "Enable update for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable delete for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable all access for service role" ON events;
DROP POLICY IF EXISTS "Enable read access for authenticated users only" ON events;
DROP POLICY IF EXISTS "Enable insert for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable update for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "Enable delete for users based on organizer_id" ON events;
DROP POLICY IF EXISTS "events_select_policy" ON events;
DROP POLICY IF EXISTS "events_insert_policy" ON events;
DROP POLICY IF EXISTS "events_update_policy" ON events;
DROP POLICY IF EXISTS "events_delete_policy" ON events;

-- Create just ONE simple policy for events - allow all operations for authenticated users
SELECT 'Creating minimal events policy...' as info;
CREATE POLICY "events_all_operations" ON events
    FOR ALL
    USING (auth.role() = 'authenticated');

-- Verify the new policy
SELECT 'New events policy:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'events';

-- Test basic access
SELECT 'Testing events access...' as info;
SELECT COUNT(*) as event_count FROM events; 