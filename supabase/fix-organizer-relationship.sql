-- Fix the organizer relationship for events
-- This script will handle the organizer_id foreign key properly

-- First, let's see what we're working with
SELECT 'Current situation:' as info;
SELECT 
    'Events count' as metric,
    COUNT(*) as value
FROM events
UNION ALL
SELECT 
    'Organizers count' as metric,
    COUNT(*) as value
FROM organizers
UNION ALL
SELECT 
    'Events with valid organizer_id' as metric,
    COUNT(*) as value
FROM events e
JOIN organizers o ON e.organizer_id = o.id
UNION ALL
SELECT 
    'Events with NULL organizer_id' as metric,
    COUNT(*) as value
FROM events 
WHERE organizer_id IS NULL;

-- Option 1: Make organizer_id nullable (simplest solution)
SELECT 'Making organizer_id nullable...' as info;
ALTER TABLE events ALTER COLUMN organizer_id DROP NOT NULL;

-- Option 2: Drop the foreign key constraint (if you want to keep it as a string reference)
-- ALTER TABLE events DROP CONSTRAINT IF EXISTS events_organizer_id_fkey;

-- Option 3: Create a default organizer for existing events (if you want to maintain the relationship)
-- This would create an organizer record for each user who has created events
SELECT 'Creating organizer records for users who created events...' as info;
INSERT INTO organizers (id, name, email, created_at)
SELECT DISTINCT
    e.created_by as id,
    'Organizer ' || e.created_by as name,
    '<EMAIL>' as email,
    NOW() as created_at
FROM events e
WHERE e.created_by IS NOT NULL
AND e.created_by NOT IN (SELECT id FROM organizers)
ON CONFLICT (id) DO NOTHING;

-- Update events to use the created_by as organizer_id
SELECT 'Updating events to use created_by as organizer_id...' as info;
UPDATE events 
SET organizer_id = created_by 
WHERE organizer_id IS NULL AND created_by IS NOT NULL;

-- Verify the changes
SELECT 'Events after fixes:' as info;
SELECT 
    e.id,
    e.name,
    e.organizer_id,
    e.created_by,
    o.name as organizer_name
FROM events e
LEFT JOIN organizers o ON e.organizer_id = o.id
ORDER BY e.created_at DESC;

-- Check foreign key constraints
SELECT 'Foreign key constraints after changes:' as info;
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'events'; 