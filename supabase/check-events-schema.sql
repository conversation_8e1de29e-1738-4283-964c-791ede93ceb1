-- Check the current events table structure
SELECT 'Events table structure:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'events' 
ORDER BY ordinal_position;

-- Check if organizer_id column exists and its properties
SELECT 'Organizer_id column details:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'events' 
AND column_name = 'organizer_id';

-- Check current events data to see if organizer_id is populated
SELECT 'Current events with organizer_id:' as info;
SELECT 
    id,
    name,
    organizer_id,
    created_at
FROM events 
ORDER BY created_at DESC;

-- Check if there are any foreign key constraints on organizer_id
SELECT 'Foreign key constraints on events table:' as info;
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'events'; 