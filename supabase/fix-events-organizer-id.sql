-- Fix organizer_id issue in events table
-- First, let's check the current structure

SELECT 'Current events table structure:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'events' 
ORDER BY ordinal_position;

-- Check if organizer_id exists and its properties
SELECT 'Organizer_id column details:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'events' 
AND column_name = 'organizer_id';

-- Check current events data
SELECT 'Current events with organizer_id:' as info;
SELECT 
    id,
    name,
    organizer_id,
    created_at
FROM events 
ORDER BY created_at DESC;

-- Option 1: Make organizer_id nullable if it's not already
-- ALTER TABLE events ALTER COLUMN organizer_id DROP NOT NULL;

-- Option 2: Set a default value for organizer_id (if it's required)
-- ALTER TABLE events ALTER COLUMN organizer_id SET DEFAULT auth.uid();

-- Option 3: Update existing events to have organizer_id set to created_by
SELECT 'Updating existing events to set organizer_id...' as info;
UPDATE events 
SET organizer_id = created_by 
WHERE organizer_id IS NULL AND created_by IS NOT NULL;

-- Check the result
SELECT 'Events after update:' as info;
SELECT 
    id,
    name,
    organizer_id,
    created_by,
    created_at
FROM events 
ORDER BY created_at DESC; 