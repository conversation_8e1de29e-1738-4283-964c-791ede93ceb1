-- Add missing updated_at column to time_slots table
-- This is needed for the audit triggers to work properly

ALTER TABLE time_slots 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create a trigger to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop the trigger if it exists to avoid conflicts
DROP TRIGGER IF EXISTS update_time_slots_updated_at ON time_slots;

-- Create the trigger
CREATE TRIGGER update_time_slots_updated_at
    BEFORE UPDATE ON time_slots
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column(); 