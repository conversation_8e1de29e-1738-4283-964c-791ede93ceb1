-- Debug script to check time slot existence and foreign key constraints
-- Replace 'YOUR_TIME_SLOT_ID' with the actual time_slot_id from the error

-- Check if the specific time slot exists
SELECT 
    id,
    start_time,
    end_time,
    location_id,
    activity_id,
    event_id,
    created_at,
    updated_at
FROM time_slots 
WHERE id = 'YOUR_TIME_SLOT_ID';

-- Show all time slots for reference
SELECT 
    id,
    start_time,
    end_time,
    location_id,
    activity_id,
    event_id
FROM time_slots 
ORDER BY start_time 
LIMIT 10;

-- Check foreign key constraints on booking_slots table
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'booking_slots';

-- Check the structure of booking_slots table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'booking_slots' 
ORDER BY ordinal_position; 