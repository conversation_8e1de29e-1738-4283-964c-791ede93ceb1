-- Fix RLS policies for event-related tables
-- This covers event_levels, activities, and other tables needed for event creation

-- 1. Fix event_levels table RLS
SELECT 'Fixing event_levels RLS policies:' as info;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for all users" ON event_levels;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON event_levels;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON event_levels;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON event_levels;
DROP POLICY IF EXISTS "Enable all access for service role" ON event_levels;

-- Create new policies for event_levels
CREATE POLICY "Enable read access for all users" ON event_levels
    FOR SELECT
    USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON event_levels
    FOR INSERT
    WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users only" ON event_levels
    FOR UPDATE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable delete for authenticated users only" ON event_levels
    FOR DELETE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all access for service role" ON event_levels
    FOR ALL
    USING (auth.role() = 'service_role');

-- 2. Fix activities table RLS
SELECT 'Fixing activities RLS policies:' as info;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for all users" ON activities;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON activities;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON activities;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON activities;
DROP POLICY IF EXISTS "Enable all access for service role" ON activities;

-- Create new policies for activities
CREATE POLICY "Enable read access for all users" ON activities
    FOR SELECT
    USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON activities
    FOR INSERT
    WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users only" ON activities
    FOR UPDATE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable delete for authenticated users only" ON activities
    FOR DELETE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all access for service role" ON activities
    FOR ALL
    USING (auth.role() = 'service_role');

-- 3. Fix time_slots table RLS
SELECT 'Fixing time_slots RLS policies:' as info;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for all users" ON time_slots;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON time_slots;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON time_slots;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON time_slots;
DROP POLICY IF EXISTS "Enable all access for service role" ON time_slots;

-- Create new policies for time_slots
CREATE POLICY "Enable read access for all users" ON time_slots
    FOR SELECT
    USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON time_slots
    FOR INSERT
    WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users only" ON time_slots
    FOR UPDATE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable delete for authenticated users only" ON time_slots
    FOR DELETE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all access for service role" ON time_slots
    FOR ALL
    USING (auth.role() = 'service_role');

-- 4. Fix locations table RLS
SELECT 'Fixing locations RLS policies:' as info;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for all users" ON locations;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON locations;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON locations;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON locations;
DROP POLICY IF EXISTS "Enable all access for service role" ON locations;

-- Create new policies for locations
CREATE POLICY "Enable read access for all users" ON locations
    FOR SELECT
    USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON locations
    FOR INSERT
    WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users only" ON locations
    FOR UPDATE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable delete for authenticated users only" ON locations
    FOR DELETE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all access for service role" ON locations
    FOR ALL
    USING (auth.role() = 'service_role');

-- 5. Fix levels_library table RLS
SELECT 'Fixing levels_library RLS policies:' as info;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for all users" ON levels_library;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON levels_library;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON levels_library;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON levels_library;
DROP POLICY IF EXISTS "Enable all access for service role" ON levels_library;

-- Create new policies for levels_library
CREATE POLICY "Enable read access for all users" ON levels_library
    FOR SELECT
    USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON levels_library
    FOR INSERT
    WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users only" ON levels_library
    FOR UPDATE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable delete for authenticated users only" ON levels_library
    FOR DELETE
    USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all access for service role" ON levels_library
    FOR ALL
    USING (auth.role() = 'service_role');

-- Verify all policies were created
SELECT 'Verifying all RLS policies:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename IN ('events', 'event_levels', 'activities', 'time_slots', 'locations', 'levels_library')
ORDER BY tablename, policyname; 