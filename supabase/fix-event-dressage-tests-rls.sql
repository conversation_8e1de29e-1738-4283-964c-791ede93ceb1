-- Minimal RLS fix for event_dressage_tests table
-- This removes all existing policies and creates just the essential ones

-- First, let's see what policies currently exist on event_dressage_tests
SELECT 'Current event_dressage_tests policies:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'event_dressage_tests';

-- Drop ALL existing policies on event_dressage_tests table
SELECT 'Dropping all existing event_dressage_tests policies...' as info;
DROP POLICY IF EXISTS "Enable read access for all users" ON event_dressage_tests;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON event_dressage_tests;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON event_dressage_tests;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON event_dressage_tests;
DROP POLICY IF EXISTS "Enable all access for service role" ON event_dressage_tests;
DROP POLICY IF EXISTS "event_dressage_tests_select_policy" ON event_dressage_tests;
DROP POLICY IF EXISTS "event_dressage_tests_insert_policy" ON event_dressage_tests;
DROP POLICY IF EXISTS "event_dressage_tests_update_policy" ON event_dressage_tests;
DROP POLICY IF EXISTS "event_dressage_tests_delete_policy" ON event_dressage_tests;

-- Create just ONE simple policy for event_dressage_tests - allow all operations for authenticated users
SELECT 'Creating minimal event_dressage_tests policy...' as info;
CREATE POLICY "event_dressage_tests_all_operations" ON event_dressage_tests
    FOR ALL
    USING (auth.role() = 'authenticated');

-- Verify the new policy
SELECT 'New event_dressage_tests policy:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'event_dressage_tests';

-- Test basic access
SELECT 'Testing event_dressage_tests access...' as info;
SELECT COUNT(*) as event_dressage_tests_count FROM event_dressage_tests; 