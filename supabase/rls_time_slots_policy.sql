-- Complete RLS Policy Script for Time Slots Table
-- This script fixes the 403 Forbidden error while allowing public read access for booking purposes

-- 1. Enable RLS on time_slots table
ALTER TABLE time_slots ENABLE ROW LEVEL SECURITY;

-- 2. Drop any existing policies to avoid conflicts
DROP POLICY IF EXISTS "Allow public read access to time slots" ON time_slots;
DROP POLICY IF EXISTS "Allow authenticated users to manage time slots" ON time_slots;
DROP POLICY IF EXISTS "Allow super admins to manage all time slots" ON time_slots;
DROP POLICY IF EXISTS "Allow organizers to manage their event time slots" ON time_slots;

-- 3. Create policy for public read access (allows non-authenticated users to view time slots for booking)
CREATE POLICY "Allow public read access to time slots" ON time_slots
    FOR SELECT
    USING (true);

-- 4. Create policy for authenticated users to manage time slots
CREATE POLICY "Allow authenticated users to manage time slots" ON time_slots
    FOR ALL
    USING (
        auth.role() = 'authenticated' AND (
            -- Super admins can manage all time slots
            EXISTS (
                SELECT 1 FROM user_roles 
                WHERE user_roles.user_id = auth.uid() 
                AND user_roles.role = 'super_admin'
            )
            OR
            -- Organizers can manage time slots for events they own
            EXISTS (
                SELECT 1 FROM locations 
                JOIN events ON locations.event_id = events.id
                WHERE locations.id = time_slots.location_id 
                AND events.organizer_id = auth.uid()
            )
        )
    );

-- 5. Create specific policy for super admins (redundant but explicit)
CREATE POLICY "Allow super admins to manage all time slots" ON time_slots
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM user_roles 
            WHERE user_roles.user_id = auth.uid() 
            AND user_roles.role = 'super_admin'
        )
    );

-- 6. Create specific policy for organizers to manage their event time slots
CREATE POLICY "Allow organizers to manage their event time slots" ON time_slots
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM locations 
            JOIN events ON locations.event_id = events.id
            WHERE locations.id = time_slots.location_id 
            AND events.organizer_id = auth.uid()
        )
    );

-- 7. Verify the policies are created
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'time_slots'
ORDER BY policyname; 