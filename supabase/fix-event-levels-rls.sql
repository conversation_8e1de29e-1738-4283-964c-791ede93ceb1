-- Minimal <PERSON>LS fix for event_levels table
-- This removes all existing policies and creates just the essential ones

-- First, let's see what policies currently exist on event_levels
SELECT 'Current event_levels policies:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'event_levels';

-- Drop ALL existing policies on event_levels table
SELECT 'Dropping all existing event_levels policies...' as info;
DROP POLICY IF EXISTS "Enable read access for all users" ON event_levels;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON event_levels;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON event_levels;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON event_levels;
DROP POLICY IF EXISTS "Enable all access for service role" ON event_levels;
DROP POLICY IF EXISTS "event_levels_select_policy" ON event_levels;
DROP POLICY IF EXISTS "event_levels_insert_policy" ON event_levels;
DROP POLICY IF EXISTS "event_levels_update_policy" ON event_levels;
DROP POLICY IF EXISTS "event_levels_delete_policy" ON event_levels;

-- Create just ONE simple policy for event_levels - allow all operations for authenticated users
SELECT 'Creating minimal event_levels policy...' as info;
CREATE POLICY "event_levels_all_operations" ON event_levels
    FOR ALL
    USING (auth.role() = 'authenticated');

-- Verify the new policy
SELECT 'New event_levels policy:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename = 'event_levels';

-- Test basic access
SELECT 'Testing event_levels access...' as info;
SELECT COUNT(*) as event_levels_count FROM event_levels; 