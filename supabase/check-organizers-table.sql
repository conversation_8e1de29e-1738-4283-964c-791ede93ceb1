-- Check the organizers table and fix the organizer_id relationship
-- First, let's see what the organizers table looks like

SELECT 'Organizers table structure:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'organizers' 
ORDER BY ordinal_position;

-- Check if organizers table exists and has data
SELECT 'Organizers table data:' as info;
SELECT 
    id,
    name,
    email,
    created_at
FROM organizers 
ORDER BY created_at DESC;

-- Check the foreign key constraint details
SELECT 'Foreign key constraint details:' as info;
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'events'
    AND kcu.column_name = 'organizer_id';

-- Check current events and their organizer relationships
SELECT 'Current events with organizer relationships:' as info;
SELECT 
    e.id,
    e.name,
    e.organizer_id,
    e.created_by,
    o.name as organizer_name,
    o.email as organizer_email
FROM events e
LEFT JOIN organizers o ON e.organizer_id = o.id
ORDER BY e.created_at DESC;

-- Check if there are any events without valid organizer_id
SELECT 'Events without valid organizer_id:' as info;
SELECT 
    e.id,
    e.name,
    e.organizer_id,
    e.created_by
FROM events e
LEFT JOIN organizers o ON e.organizer_id = o.id
WHERE o.id IS NULL AND e.organizer_id IS NOT NULL;

-- Check if there are any events with NULL organizer_id
SELECT 'Events with NULL organizer_id:' as info;
SELECT 
    id,
    name,
    organizer_id,
    created_by
FROM events 
WHERE organizer_id IS NULL; 