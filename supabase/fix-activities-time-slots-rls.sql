-- Fix RLS for activities and time_slots tables
-- This will allow time slot generation to work properly

-- 1. Fix activities table RLS
SELECT 'Fixing activities table RLS...' as info;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for all users" ON activities;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON activities;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON activities;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON activities;
DROP POLICY IF EXISTS "Enable all access for service role" ON activities;
DROP POLICY IF EXISTS "activities_select_policy" ON activities;
DROP POLICY IF EXISTS "activities_insert_policy" ON activities;
DROP POLICY IF EXISTS "activities_update_policy" ON activities;
DROP POLICY IF EXISTS "activities_delete_policy" ON activities;

-- Create simple policy for activities
CREATE POLICY "activities_all_operations" ON activities
    FOR ALL
    USING (auth.role() = 'authenticated');

-- 2. Fix time_slots table RLS
SELECT 'Fixing time_slots table RLS...' as info;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for all users" ON time_slots;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON time_slots;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON time_slots;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON time_slots;
DROP POLICY IF EXISTS "Enable all access for service role" ON time_slots;
DROP POLICY IF EXISTS "time_slots_select_policy" ON time_slots;
DROP POLICY IF EXISTS "time_slots_insert_policy" ON time_slots;
DROP POLICY IF EXISTS "time_slots_update_policy" ON time_slots;
DROP POLICY IF EXISTS "time_slots_delete_policy" ON time_slots;

-- Create simple policy for time_slots
CREATE POLICY "time_slots_all_operations" ON time_slots
    FOR ALL
    USING (auth.role() = 'authenticated');

-- 3. Verify the policies
SELECT 'New policies created:' as info;
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename IN ('activities', 'time_slots')
ORDER BY tablename, policyname;

-- 4. Test access
SELECT 'Testing access after fixes:' as info;
SELECT 
    'activities' as table_name,
    COUNT(*) as record_count
FROM activities
UNION ALL
SELECT 
    'time_slots' as table_name,
    COUNT(*) as record_count
FROM time_slots; 