-- Fix the foreign key constraint on organizer_id
-- First, let's see what the constraint is

SELECT 'Foreign key constraints on events table:' as info;
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'events';

-- Check if the organizer_id column is nullable
SELECT 'Organizer_id column properties:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'events' 
AND column_name = 'organizer_id';

-- Check what table the foreign key references
SELECT 'Checking auth.users table structure:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'auth'
ORDER BY ordinal_position;

-- Option 1: Drop the foreign key constraint entirely
SELECT 'Dropping foreign key constraint...' as info;
ALTER TABLE events DROP CONSTRAINT IF EXISTS events_organizer_id_fkey;

-- Option 2: Make organizer_id nullable (if it's not already)
SELECT 'Making organizer_id nullable...' as info;
ALTER TABLE events ALTER COLUMN organizer_id DROP NOT NULL;

-- Verify the changes
SELECT 'Events table structure after changes:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'events' 
AND column_name = 'organizer_id';

-- Check foreign key constraints again
SELECT 'Foreign key constraints after changes:' as info;
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'events'; 