-- Complete RLS Policy Script for Activities Table
-- This script fixes the 403 Forbidden error while allowing public read access for booking purposes

-- 1. Enable RLS on activities table
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;

-- 2. Drop any existing policies to avoid conflicts
DROP POLICY IF EXISTS "Allow public read access to activities" ON activities;
DROP POLICY IF EXISTS "Allow authenticated users to manage activities" ON activities;
DROP POLICY IF EXISTS "Allow super admins to manage all activities" ON activities;
DROP POLICY IF EXISTS "Allow organizers to manage their event activities" ON activities;

-- 3. Create policy for public read access (allows non-authenticated users to view activities)
CREATE POLICY "Allow public read access to activities" ON activities
    FOR SELECT
    USING (true);

-- 4. Create policy for authenticated users to manage activities
CREATE POLICY "Allow authenticated users to manage activities" ON activities
    FOR ALL
    USING (
        auth.role() = 'authenticated' AND (
            -- Super admins can manage all activities
            EXISTS (
                SELECT 1 FROM user_roles 
                WHERE user_roles.user_id = auth.uid() 
                AND user_roles.role = 'super_admin'
            )
            OR
            -- Organizers can manage activities for events they own
            EXISTS (
                SELECT 1 FROM locations 
                JOIN events ON locations.event_id = events.id
                WHERE locations.id = activities.location_id 
                AND events.organizer_id = auth.uid()
            )
        )
    );

-- 5. Create specific policy for super admins (redundant but explicit)
CREATE POLICY "Allow super admins to manage all activities" ON activities
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM user_roles 
            WHERE user_roles.user_id = auth.uid() 
            AND user_roles.role = 'super_admin'
        )
    );

-- 6. Create specific policy for organizers to manage their event activities
CREATE POLICY "Allow organizers to manage their event activities" ON activities
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM locations 
            JOIN events ON locations.event_id = events.id
            WHERE locations.id = activities.location_id 
            AND events.organizer_id = auth.uid()
        )
    );

-- 7. Verify the policies are created
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'activities'
ORDER BY policyname; 