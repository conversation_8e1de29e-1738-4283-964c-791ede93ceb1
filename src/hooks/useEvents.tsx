import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Event {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  schooling_start_date: string;
  schooling_end_date: string;
  event_type: 'dressage' | 'horse_trials' | 'hunter_jumper';
  created_by?: string;
  organizer_id?: string;
  created_at?: string;
  is_active?: boolean;
}

export const useCreateEvent = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (eventData: {
      event: Omit<Event, 'id' | 'created_at'>;
      levelIds: string[];
    }) => {
      // Get current user for organizer_id
      const { data: { user } } = await supabase.auth.getUser();
      
      // Prepare event data with organizer_id
      const eventWithOrganizer = {
        ...eventData.event,
        organizer_id: user?.id || null,
        created_by: user?.id || null
      };

      // First create the event
      const { data: event, error: eventError } = await supabase
        .from('events')
        .insert([eventWithOrganizer])
        .select()
        .single();

      if (eventError) {
        throw eventError;
      }

      // Then create the event levels if any are selected
      if (eventData.levelIds && eventData.levelIds.length > 0) {
        const eventLevels = eventData.levelIds.map(levelId => ({
          event_id: event.id,
          level_id: levelId,
          is_enabled: true
        }));

        const { error: levelsError } = await supabase
          .from('event_levels')
          .insert(eventLevels);

        if (levelsError) {
          // If levels creation fails, delete the event
          await supabase
            .from('events')
            .delete()
            .eq('id', event.id);
          throw levelsError;
        }
      }

      return event;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['event-levels'] });
      toast({
        title: "Success",
        description: "Event created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}; 