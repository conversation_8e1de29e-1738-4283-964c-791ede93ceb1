{
  // enable the Vitest Explorer
  "vitestExplorer.enable": true,

  // how to launch Vitest (adjust if you use yarn/pnpm or a custom script)
  "vitestExplorer.vitestCommand": "npm run test",

  // point to your vitest config file(s)
  "vitestExplorer.configFiles": ["vite.config.ts", "vitest.config.ts"],

  // optional: show inline “Run | Debug” codelens above each test
  "vitestExplorer.codeLens.enable": true
}