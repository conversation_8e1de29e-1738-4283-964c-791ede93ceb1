#!/usr/bin/env sh

# Check if commit message follows conventional commits format
commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ Commit message does not follow conventional commits format."
    echo "Expected format: type(scope): description"
    echo "Examples:"
    echo "  feat: add new feature"
    echo "  fix(auth): resolve login issue"
    echo "  test: add unit tests for user component"
    echo "  chore: update dependencies"
    exit 1
fi

echo "✅ Commit message format is valid!" 