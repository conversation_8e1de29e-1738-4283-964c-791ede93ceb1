#!/bin/bash

# Redeploy all Supabase Edge Functions
echo "Redeploying Supabase Edge Functions..."

PROJECT_ID="tuocgtwpjvruwtfwzmiu"

# Deploy create-stripe-checkout function
echo "Deploying create-stripe-checkout function..."
supabase functions deploy create-stripe-checkout --project-ref $PROJECT_ID

# Deploy stripe-webhook function
echo "Deploying stripe-webhook function..."
supabase functions deploy stripe-webhook --project-ref $PROJECT_ID

# Deploy delete-booking function
echo "Deploying delete-booking function..."
supabase functions deploy delete-booking --project-ref $PROJECT_ID

echo "All functions deployed successfully!" 