# Next Rider Up - Schooling Scheduling App

This is a web application for managing and booking schooling sessions for horse trials and other equestrian events. It's built with Vite, React, TypeScript, and uses Shadcn/UI for components and Supabase as the backend.

## Features

-   Event creation and management for organizers.
-   Public-facing list of events.
-   Booking flow for riders to select activities and time slots.
-   User authentication and role management (User, Organizer, Super Admin).
-   Stripe integration for payments.
-   Admin dashboards for managing events, bookings, users, and pricing.

## Tech Stack

-   **Frontend:** React, Vite, TypeScript
-   **UI:** Shadcn/UI, Tailwind CSS
--   **Backend & DB:** [Supabase](https://supabase.com/)
-   **Payments:** [Stripe](https://stripe.com/)
-   **Testing:** Vitest, React Testing Library

## Getting Started

### Prerequisites

-   Node.js (v18 or higher recommended)
-   npm or another package manager
-   Supabase account and project
-   Stripe account

### Local Development

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd horse-trials-booking-hub
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    Create a `.env.local` file in the `horse-trials-booking-hub` directory and add your Supabase and Stripe API keys. You can get these from your Supabase project settings.
    ```
    VITE_SUPABASE_URL=your_supabase_project_url
    VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
    ```

4.  **Run the development server:**
    ```bash
    npm run dev
    ```
    The application will be available at `http://localhost:5173`.

### Testing

-   To run all tests: `npm test`
-   To run tests in watch mode: `npm run test:watch`

### Pre-commit Hooks
This project uses `husky` and `lint-staged` to run checks before committing.
-   The pre-commit hook will run `eslint` on staged files.
-   It is also configured to run the full test suite (`npm run test:run`), though this is currently commented out in `.husky/pre-commit`. Uncomment it to enable pre-commit test runs.

## Database Management

### Backing Up the Schema

It's important to periodically back up the database schema. This can be done using the Supabase CLI.

1.  **Ensure Supabase CLI is installed and you are logged in.** If not, run:
    ```bash
    npm i -g supabase && supabase login
    ```

2.  **Link your local project to your Supabase project** (only needs to be done once):
    ```bash
    supabase link --project-ref <your-project-ref>
    ```
    You can find your `<your-project-ref>` in your Supabase project's URL (`https://app.supabase.com/project/<your-project-ref>`).

3.  **Dump the remote schema to a file:**
    This command will save the structure of your remote database to a file with the current date and time in its name (e.g., `schema_backup_2023-10-27_10-30-00.sql`).
    ```bash
    # For macOS / Linux
    supabase db dump -f schema_backup_$(date +'%Y-%m-%d_%H-%M-%S').sql --schema-only
    ```

You can commit this schema backup file to your repository to keep a version-controlled history of your database structure.

### Backing Up Data

There are two primary ways to back up your project's data.

**1. Via the Supabase CLI**

This method is useful for scripting and automation. It will dump the data from your remote database into a SQL file with a timestamp.

```bash
# For macOS / Linux
supabase db dump -f data_backup_$(date +'%Y-%m-%d_%H-%M-%S').sql --data-only
```
**Important:** Be careful about committing this `data_backup.sql` file to a public repository, as it will contain all your database's data, which may include sensitive user information. It's best to store data backups in a secure, private location.

**2. Via the Supabase Dashboard**

Supabase provides automated daily backups and the ability to create manual backups from the dashboard. This is the recommended approach for most use cases.

1.  Navigate to your project on the [Supabase Dashboard](https://app.supabase.com/).
2.  In the left sidebar, go to **Project Settings** (the gear icon).
3.  Click on **Backups** in the settings menu.
4.  Here you can see a list of scheduled daily backups. You can download any of these backups.
5.  Supabase also offers Point-in-Time Recovery (PITR) on paid plans for more granular data restoration.
