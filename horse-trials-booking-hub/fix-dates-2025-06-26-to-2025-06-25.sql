-- Fix dates: Change everything from 2025-06-26 to 2025-06-25
-- This script updates the event schooling dates and all related time slots and activities

-- 1. Update the event's schooling end date
UPDATE events 
SET schooling_end_date = '2025-06-25'
WHERE id = '76e88ec6-1ff2-4703-94b8-35687e56ff90' 
  AND schooling_end_date = '2025-06-26';

-- 2. Update all time slots from 2025-06-26 to 2025-06-25
UPDATE time_slots 
SET 
  start_time = start_time - INTERVAL '1 day',
  end_time = end_time - INTERVAL '1 day'
WHERE 
  DATE(start_time) = '2025-06-26' 
  OR DATE(end_time) = '2025-06-26';

-- 3. Update all activities from 2025-06-26 to 2025-06-25
UPDATE activities 
SET 
  start_time = start_time - INTERVAL '1 day',
  end_time = end_time - INTERVAL '1 day'
WHERE 
  DATE(start_time) = '2025-06-26' 
  OR DATE(end_time) = '2025-06-26';

-- 4. Show the results
SELECT 'Event updated:' as info, name, schooling_start_date, schooling_end_date 
FROM events 
WHERE id = '76e88ec6-1ff2-4703-94b8-35687e56ff90';

SELECT 'Time slots updated:' as info, COUNT(*) as count 
FROM time_slots 
WHERE DATE(start_time) = '2025-06-25' OR DATE(end_time) = '2025-06-25';

SELECT 'Activities updated:' as info, COUNT(*) as count 
FROM activities 
WHERE DATE(start_time) = '2025-06-25' OR DATE(end_time) = '2025-06-25'; 