# Agent.md

## 🧪 Test Authoring Guidance for the Schooling Scheduling App

This guide outlines best practices and patterns for writing reliable, maintainable tests across the Gray Area Events application.

Tests are written using:
- **Vitest** as the test runner
- **React Testing Library** for DOM interaction
- **Mock service layer** to control Supabase and hook behavior

---

## 🎯 Setting Expectations

**Before starting any test work, always:**

1. **"Let's check the component first before writing tests"**
   - Inspect the actual component implementation
   - Confirm what UI elements and behavior actually exist
   - Do not write tests for functionality that doesn't exist in the component

2. **"Use npx for test execution"**
   - Always run: `npx vitest run path/to/YourComponent.test.tsx`
   - This ensures you're using the correct local version
   - Avoids global version mismatches and ensures consistent behavior

**Following these expectations prevents wasted time testing non-existent UI elements and ensures tests accurately reflect the real user experience.**

---

## 🔧 General Testing Patterns

- Use `userEvent.setup()` in `beforeEach()` and share it via a top-level `user` variable.
- Always wrap tested components with `QueryClientProvider` using `createTestQueryClient()` from `test-utils.ts`.
- Prefer `userEvent` over `fireEvent` for simulating realistic user actions.
- Use `waitFor()` or `findBy*` queries for anything that renders or updates asynchronously.

```tsx
beforeEach(() => {
  user = userEvent.setup();
});

it('submits the form successfully', async () => {
  render(
    <QueryClientProvider client={createTestQueryClient()}>
      <MyComponent />
    </QueryClientProvider>
  );

  await user.click(screen.getByRole('button', { name: /submit/i }));
  expect(await screen.findByText(/success/i)).toBeInTheDocument();
});
```

## 🛡️ Code Editing Principles

**Minimize Unnecessary File Rewrites**

- **Always prefer minimal, targeted changes.**  
  Only update the lines or sections necessary to achieve the goal. Avoid overwriting entire files unless absolutely required (e.g., restoring from a known good commit).
- **Preserve in-progress work.**  
  Before making large changes, check for recent, uncommitted edits and merge them in if possible.
- **Show diffs for significant changes.**  
  When making non-trivial edits, review the diff to ensure only the intended code is changed.
- **Communicate intent.**  
  If a full-file rewrite is needed, clearly state why and confirm with the team before proceeding.

**Why?**  
This practice prevents accidental loss of work, reduces merge conflicts, and makes code review and debugging easier for everyone.

---

## 🧵 Radix UI Strategy

To avoid flaky tests with `@radix-ui/react-*` components:

- **Mock Radix components** (e.g., `Select`, `Dialog`, `Tabs`) using simple HTML equivalents.
- Radix Tabs: check `data-state="active"` to verify tab selection.
- Dialogs and dropdowns may require `.keyboard('{Escape}')` or explicit container scoping.

---

## 🔌 Mocking Strategy

- Centralized mocks are defined in `test-utils.ts` using `vi.mock`.
- Spy functions (`vi.fn()`) are exposed and asserted in tests as needed.
- Use inline `mockReturnValue()` in test files to override base mocks.

```ts
mockUseUpdateBooking.mockReturnValue({
  mutateAsync: vi.fn(),
  isPending: false
});
```

---

## ✅ Preferred Matchers and Interactions

| Purpose              | Matcher / Interaction                              |
|----------------------|-----------------------------------------------------|
| Presence             | `expect(...).toBeInTheDocument()`                  |
| Attribute            | `expect(...).toHaveAttribute()`                    |
| Text matching        | `expect(...).toHaveTextContent()`                  |
| Form input           | `await user.type(..., 'value')`                    |
| Scoped queries       | `within(container).getByRole(...)`                 |

---

## 🏷️ Test IDs

- **Prefer accessible queries** (`getByRole`, `getByLabelText`, `getByText`, etc.) over `getByTestId`.
- Use `data-testid` **only when necessary** (e.g., for elements with no accessible role or label).
- If you must use a test ID, make it descriptive and stable (e.g., `data-testid="delete-user-button"`).

```tsx
// Prefer this:
expect(screen.getByRole('button', { name: /submit/i })).toBeInTheDocument();

// Only use this if no other option:
expect(screen.getByTestId('custom-widget')).toBeInTheDocument();
```

---

## 🔧 Advanced Mocking Patterns

### Universal Supabase Mock
For complex database operations, use inline chainable mocks:

```ts
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: () => {
      const fn = vi.fn().mockReturnThis();
      return {
        select: fn, insert: fn, update: fn, delete: fn,
        eq: fn, in: fn, order: fn, single: fn,
        from: vi.fn().mockReturnThis(),
      };
    },
  },
}));
```

### Testing Mutation Logic
- Test actual mutation functions, not just hook states
- Mock complex business logic (cascade deletions, rollbacks)
- Test error scenarios at each step

### Vitest Hoisting
- `vi.mock` factories are hoisted to the top
- Don't reference top-level variables inside `vi.mock`
- Use inline function definitions instead

---

## ⏱ Handling Async Behavior

Use `findBy*` or `waitFor` when:
- Tabs switch
- Dialogs appear
- Pricing data loads
- Toasts display

```ts
await waitFor(() => {
  expect(screen.getByText(/some async content/i)).toBeInTheDocument();
});

const confirmation = await screen.findByRole('alert');
```

---

## 📋 Representative Test Coverage Areas

These are examples of app areas that benefit most from focused tests. Use this as inspiration, not as a checklist:

### BookingFlow
- Pricing accuracy per activity
- Form validation and required fields
- Booking confirmation behavior

### EventDetails
- Switching between activity-type tabs
- Filtering locations by date or type
- Interactions with Radix dropdowns

### AdminBookings
- Editing and deleting bookings
- Dialog interactions and state updates
- Hook-driven update and mutation flows

### Auth
- Login and role-based redirect logic
- Basic route protection

### Hook-based Logic
- Use `vi.fn()` mocks to simulate hook return values
- Assert calls using `.toHaveBeenCalledWith(...)`

---

## 📁 Suggested Folder Structure

```bash
src/
  hooks/
    useBookings.test.tsx
    useAppSettings.test.tsx

  components/
    BookingFlow.test.tsx
    EventDetails.test.tsx

  pages/
    AdminDashboardPage.test.tsx
    CreateEventPage.test.tsx
```

---

## 🚨 Common Gotchas

- **Radix Selects**: Must be mocked or will fail due to `pointer-events: none`
- **Tabs**: Use `data-state="active"` to determine selection, not just `click`
- **Toasts**: Use `screen.findByRole('alert')` to detect success or error messages
- **screen.debug()**: Use to inspect hidden nodes or debug layout issues

---

## 🧠 Tips

- Prefer `findBy*` over `getBy*` when waiting for async hydration
- Isolate reusable mocks and setup into `test-utils.ts`
- Avoid flaky transitions by mocking animations and disabling pointer checks
- Include this file (`Agent.md`) in your repo to onboard new contributors quickly

## Cypress E2E Testing Guidelines

**General Principles**
- Always review the actual page/component code before writing or updating a test.
- Use only real field names, data-testid values, and UI flows as implemented.
- Do not assume the presence of fields or steps—confirm by reading the code.

**Best Practices**
- Use `data-testid` attributes for selectors whenever possible.
- For custom UI components (e.g., Radix UI Select), use robust interaction patterns (see below).
- Always clean up test data (e.g., delete created events) at the end of each test.

**Radix UI Select Pattern**
```js
// Example for selecting an option in a Radix UI Select
cy.get('[data-testid="activity-type-select"]').click();
cy.contains('Dressage').click();
```

**Test Cleanup**
- Navigate to the events list page for reliable deletion.
- Use unique event names (e.g., with a timestamp) to avoid collisions.

**Updating Tests**
- If the UI changes, update the test to match the new structure and selectors.
- If unsure, summarize the relevant JSX or form structure before writing the test.