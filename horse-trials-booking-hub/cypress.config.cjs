const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:5173', // Vite dev server default port
    setupNodeEvents(on, config) {
      // implement node event listeners here
    },
    viewportWidth: 1280,
    viewportHeight: 720,
    video: false, // Disable video recording for faster runs
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    // Environment-specific configurations
    env: {
      // Add any environment variables here
      // apiUrl: 'http://localhost:54321', // Supabase local
      REACT_APP_ALLOW_EVENT_DELETE_WITH_BOOKINGS: 'true',
    },
    // Test retries for flaky tests
    retries: {
      runMode: 2,
      openMode: 0,
    },
  },
  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
  },
}); 