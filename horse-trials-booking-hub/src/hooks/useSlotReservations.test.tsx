import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { 
  getUserSessionId,
  useSlotReservations, 
  useReserveSlot, 
  useExtendReservation, 
  useReleaseReservation, 
  useCleanupExpiredReservations 
} from './useSlotReservations';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { usePricingSettings } from '@/hooks/usePricingSettings';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          gt: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
        gt: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
    rpc: vi.fn(),
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

// Mock usePricingSettings
vi.mock('@/hooks/usePricingSettings', () => ({
  usePricingSettings: vi.fn(),
  getCartRetentionMinutes: vi.fn(),
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);
const mockUsePricingSettings = vi.mocked(usePricingSettings);

describe('useSlotReservations', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockPricingSettings = {
    cart_retention_minutes: 15,
  };

  const mockReservations = [
    {
      id: 'reservation-1',
      time_slot_id: 'slot-1',
      user_session_id: 'session-1',
      expires_at: '2024-01-15T10:15:00Z',
      created_at: '2024-01-15T10:00:00Z',
      event_id: 'event-1',
      event_name: 'Spring Show',
      location_name: 'Main Arena',
      start_time: '2024-01-15T10:00:00Z',
      end_time: '2024-01-15T11:00:00Z',
      activity_type: 'dressage',
      level: 'Intro',
    },
    {
      id: 'reservation-2',
      time_slot_id: 'slot-2',
      user_session_id: 'session-1',
      expires_at: '2024-01-15T10:20:00Z',
      created_at: '2024-01-15T10:05:00Z',
      event_id: 'event-1',
      event_name: 'Spring Show',
      location_name: 'Jumping Arena',
      start_time: '2024-01-15T11:00:00Z',
      end_time: '2024-01-15T12:00:00Z',
      activity_type: 'show_jumping',
      level: 'Prelim',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
    mockUsePricingSettings.mockReturnValue({
      data: mockPricingSettings,
      isLoading: false,
      error: null,
    } as any);
    localStorageMock.getItem.mockReturnValue(null);
    localStorageMock.setItem.mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getUserSessionId', () => {
    it('creates a new session ID when none exists', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const sessionId = getUserSessionId();
      
      expect(sessionId).toMatch(/^session_\d+_[a-z0-9]{9}$/);
      expect(localStorageMock.setItem).toHaveBeenCalledWith('user_session_id', sessionId);
    });

    it('returns existing session ID from localStorage', () => {
      const existingSessionId = 'session_1234567890_abc123def';
      localStorageMock.getItem.mockReturnValue(existingSessionId);
      
      const sessionId = getUserSessionId();
      
      expect(sessionId).toBe(existingSessionId);
      expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });
  });

  describe('useSlotReservations', () => {
    it('fetches reservations for a specific user session', async () => {
      mockUseQuery.mockReturnValue({
        data: mockReservations,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSlotReservations('session-1'));

      expect(result.current.data).toEqual(mockReservations);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('fetches reservations for a specific event', async () => {
      mockUseQuery.mockReturnValue({
        data: mockReservations.filter(r => r.event_id === 'event-1'),
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSlotReservations('session-1', 'event-1'));

      expect(result.current.data).toHaveLength(2);
      expect(result.current.data?.every(r => r.event_id === 'event-1')).toBe(true);
    });

    it('returns empty array when no userSessionId provided', () => {
      mockUseQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSlotReservations(''));

      expect(result.current.data).toEqual([]);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useSlotReservations('session-1'));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch reservations');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useSlotReservations('session-1'));

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('formats time correctly for display', () => {
      const reservationsWithFormattedTime = mockReservations.map(r => ({
        ...r,
        time: '10:00 AM - 11:00 AM',
      }));
      
      mockUseQuery.mockReturnValue({
        data: reservationsWithFormattedTime,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSlotReservations('session-1'));

      expect(result.current.data?.[0].time).toBe('10:00 AM - 11:00 AM');
    });
  });

  describe('useReserveSlot', () => {
    it('reserves a slot successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'reservation-3',
        time_slot_id: 'slot-3',
        user_session_id: 'session-1',
        expires_at: '2024-01-15T10:30:00Z',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useReserveSlot());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during reservation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useReserveSlot());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during reservation', () => {
      const mockError = new Error('Slot already reserved');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useReserveSlot());

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useExtendReservation', () => {
    it('extends reservation successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'reservation-1',
        expires_at: '2024-01-15T10:30:00Z',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useExtendReservation());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during extension', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useExtendReservation());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during extension', () => {
      const mockError = new Error('Failed to extend reservation');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useExtendReservation());

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useReleaseReservation', () => {
    it('releases reservation successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useReleaseReservation());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during release', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useReleaseReservation());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during release', () => {
      const mockError = new Error('Failed to release reservation');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useReleaseReservation());

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useCleanupExpiredReservations', () => {
    it('cleans up expired reservations successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCleanupExpiredReservations());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during cleanup', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useCleanupExpiredReservations());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during cleanup', () => {
      const mockError = new Error('Failed to cleanup expired reservations');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useCleanupExpiredReservations());

      expect(result.current.error).toBe(mockError);
    });
  });
}); 