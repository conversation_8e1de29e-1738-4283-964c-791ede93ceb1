import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { usePricingSettings, getActivityPrice } from '@/hooks/usePricingSettings';

export interface EventPrice {
  id: string;
  event_id: string;
  activity_type: 'dressage' | 'show_jumping' | 'cross_country';
  level?: string | null;
  price: number;
  currency_symbol: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type EventPricingFormData = Omit<EventPrice, 'id' | 'event_id'>;

export const useEventPricing = (eventId: string) => {
  return useQuery<EventPrice[], Error>({
    queryKey: ['event-pricing', eventId],
    queryFn: async () => {
      console.log('Fetching event pricing for eventId:', eventId);
      if (!eventId) return [];
      
      const { data, error } = await supabase
        .from('event_pricing')
        .select('*')
        .eq('event_id', eventId)
        .eq('is_active', true);

      if (error) {
        console.error('Error fetching event pricing:', error);
        throw new Error(error.message);
      }
      
      console.log('Fetched event pricing data:', data);
      return data || [];
    },
    enabled: !!eventId,
  });
};

// Hook to get pricing for a specific event and activity (with fallback to global pricing)
export const useEventActivityPricing = (eventId: string, activityType?: string, level?: string) => {
  const { data: globalPricing } = usePricingSettings();
  
  return useQuery({
    queryKey: ['event_activity_pricing', eventId, activityType, level],
    queryFn: async () => {
      if (!eventId || !activityType) {
        // Fall back to global pricing
        const globalPrice = getActivityPrice(activityType || 'dressage', globalPricing);
        return {
          price: globalPrice,
          currency_symbol: globalPricing?.currency_symbol || '$',
          source: 'global' as const
        };
      }

      // First try to get event-specific pricing
      const query = supabase
        .from('event_pricing')
        .select('price, currency_symbol')
        .eq('event_id', eventId)
        .eq('activity_type', activityType)
        .eq('is_active', true);

      // If level is specified, try level-specific pricing first
      if (level) {
        const { data: levelPricing, error: levelError } = await query
          .eq('level', level)
          .single();

        if (levelPricing) {
          return {
            price: levelPricing.price,
            currency_symbol: levelPricing.currency_symbol,
            source: 'event_level' as const
          };
        }
      }

      // Try general activity pricing (no level)
      const { data: activityPricing, error: activityError } = await query
        .is('level', null)
        .single();

      if (activityPricing) {
        return {
          price: activityPricing.price,
          currency_symbol: activityPricing.currency_symbol,
          source: 'event_activity' as const
        };
      }

      // Fall back to global pricing
      const globalPrice = getActivityPrice(activityType, globalPricing);
      return {
        price: globalPrice,
        currency_symbol: globalPricing?.currency_symbol || '$',
        source: 'global' as const
      };
    },
    enabled: !!eventId,
  });
};

export const useCreateEventPricing = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ eventId, ...pricingData }: { eventId: string } & EventPricingFormData) => {
      const { error } = await supabase
        .from('event_pricing')
        .insert([{ ...pricingData, event_id: eventId }]);

      if (error) throw error;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['event-pricing', variables.eventId] });
      toast({ title: 'Success', description: 'New price created successfully.' });
    },
    onError: (error) => {
      toast({ title: 'Error', description: `Failed to create price: ${error.message}`, variant: 'destructive' });
    },
  });
};

export const useUpdateEventPricing = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, eventId, ...updates }: { id: string, eventId: string } & Partial<EventPricingFormData>) => {
      console.log('=== UPDATE MUTATION START ===');
      console.log('Updating event pricing:', { id, eventId, updates });
      
      // Check current user authentication
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      console.log('Current user:', user);
      console.log('Auth error:', authError);
      
      // Check user roles
      let isSuperAdmin = false;
      if (user) {
        const { data: userRoles, error: roleError } = await supabase
          .from('user_roles')
          .select('*')
          .eq('user_id', user.id);
        console.log('User roles:', userRoles);
        console.log('Role error:', roleError);
        
        isSuperAdmin = userRoles?.some(role => role.role === 'super_admin') || false;
        console.log('Is Super Admin:', isSuperAdmin);
      }
      
      // Log the exact data being sent
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };
      console.log('Update data being sent:', updateData);
      
      const { data, error } = await supabase
        .from('event_pricing')
        .update(updateData)
        .eq('id', id)
        .select('*');

      if (error) {
        console.error('Supabase update error:', error);
        console.error('Error details:', error.details, error.hint, error.message);
        throw error;
      }
      
      console.log('Update successful, returned data:', data);
      console.log('Number of rows affected:', data?.length);
      console.log('=== UPDATE MUTATION END ===');
      return data;
    },
    onSuccess: (data, variables) => {
      console.log('=== MUTATION SUCCESS ===');
      console.log('Mutation success, invalidating queries for eventId:', variables.eventId);
      console.log('Current query cache keys:', queryClient.getQueryCache().getAll().map(q => q.queryKey));
      
      // Invalidate the specific query
      queryClient.invalidateQueries({ queryKey: ['event-pricing', variables.eventId] });
      
      // Also try to update the cache directly
      queryClient.setQueryData(['event-pricing', variables.eventId], (oldData: EventPrice[] | undefined) => {
        console.log('Updating cache directly, old data:', oldData);
        if (!oldData) return oldData;
        
        const updatedData = oldData.map(price => 
          price.id === variables.id 
            ? { ...price, ...variables }
            : price
        );
        console.log('Updated cache data:', updatedData);
        return updatedData;
      });
      
      toast({ title: 'Success', description: 'Price updated successfully.' });
    },
    onError: (error) => {
      console.error('=== MUTATION ERROR ===');
      console.error('Mutation error:', error);
      toast({ title: 'Error', description: `Failed to update price: ${error.message}`, variant: 'destructive' });
    },
  });
};

export const useDeleteEventPricing = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, eventId }: { id: string, eventId: string }) => {
      const { error } = await supabase.from('event_pricing').delete().eq('id', id);
      if (error) throw error;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['event-pricing', variables.eventId] });
      toast({ title: 'Success', description: 'Price deleted successfully.' });
    },
    onError: (error) => {
      toast({ title: 'Error', description: `Failed to delete price: ${error.message}`, variant: 'destructive' });
    },
  });
}; 