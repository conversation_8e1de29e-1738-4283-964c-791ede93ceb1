import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { getErrorMessage } from '@/types/errors';

export interface DressageTest {
  id: string;
  label: string;
  description?: string;
  level_id?: string;
  organization?: string;
  is_active?: boolean;
  levels?: {
    id: string;
    name: string;
    discipline: string;
  };
}

export interface EventDressageTest {
  id: string;
  event_id: string;
  test_id: string;
  is_enabled?: boolean;
  dressage_test_library?: DressageTest;
}

export const useDressageTests = (levelId?: string) => {
  return useQuery({
    queryKey: ['dressage-tests', levelId],
    queryFn: async () => {
      console.log('Fetching dressage tests for level:', levelId);
      
      let query = supabase
        .from('dressage_test_library')
        .select(`
          *,
          levels_library!dressage_test_library_level_id_fkey (
            id,
            name,
            discipline
          )
        `)
        .eq('is_active', true);
      
      if (levelId) {
        query = query.eq('level_id', levelId);
      }
      
      const { data, error } = await query.order('label', { ascending: true });

      if (error) {
        console.error('Error fetching dressage tests:', error);
        throw error;
      }

      console.log('Dressage tests fetched:', data);
      
      // Transform the data to match our interface
      const transformedData = data?.map(item => ({
        ...item,
        levels: item.levels_library
      })) || [];
      
      return transformedData as DressageTest[];
    },
  });
};

export const useEventDressageTests = (eventId?: string) => {
  return useQuery({
    queryKey: ['event_dressage_tests', eventId],
    queryFn: async () => {
      if (!eventId) {
        console.log('No event ID provided to useEventDressageTests');
        return [];
      }
      
      console.log(`Fetching dressage tests for event: ${eventId}`);
      
      try {
        // Query with proper foreign key relationship using dressage_test_library_id
        const { data, error } = await supabase
          .from('event_dressage_tests')
          .select(`
            id,
            event_id,
            test_id,
            dressage_test_library_id,
            dressage_test_library!event_dressage_tests_dressage_test_library_id_fkey (
              id,
              label,
              description,
              level_id,
              levels_library!dressage_test_library_level_id_fkey (
                id,
                name,
                discipline
              )
            )
          `)
          .eq('event_id', eventId);

        if (error) {
          console.error('Error fetching event dressage tests:', error);
          throw error;
        }

        console.log(`Found ${data?.length || 0} dressage tests for event ${eventId}`);
        
        // Transform the data to match our interface
        const transformedData = data?.map(item => ({
          ...item,
          dressage_test_library: item.dressage_test_library ? {
            ...item.dressage_test_library,
            levels: item.dressage_test_library.levels_library
          } : null
        })) || [];
        
        // Log each test to debug
        if (transformedData && transformedData.length > 0) {
          transformedData.forEach((test, index) => {
            console.log(`Test ${index + 1}:`, {
              id: test.id,
              test_id: test.test_id,
              dressage_test_library_id: test.dressage_test_library_id,
              dressage_test_library: test.dressage_test_library
            });
          });
        } else {
          console.log('No dressage tests found for this event');
        }
        
        return transformedData;
      } catch (error) {
        console.error('Error in useEventDressageTests:', error);
        // Return an empty array instead of throwing to prevent breaking the UI
        return [];
      }
    },
    enabled: !!eventId,
    staleTime: 1000 * 60, // 1 minute
    refetchOnWindowFocus: false, // Disable refetch on window focus to reduce errors
    retry: 1, // Only retry once to avoid excessive error logs
  });
};

export const useCreateEventDressageTest = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (eventTestData: Omit<EventDressageTest, 'id' | 'dressage_test_library'>) => {
      // Set both test_id and dressage_test_library_id to the same value
      const insertData = {
        ...eventTestData,
        dressage_test_library_id: eventTestData.test_id
      };

      const { data, error } = await supabase
        .from('event_dressage_tests')
        .insert([insertData])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      // Invalidate all event_dressage_tests queries to refresh the list
      queryClient.invalidateQueries({ queryKey: ['event_dressage_tests'] });
      toast({
        title: "Success",
        description: "Dressage test added to event successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};

export const useDeleteEventDressageTest = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('event_dressage_tests')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate all event_dressage_tests queries to refresh the list
      queryClient.invalidateQueries({ queryKey: ['event_dressage_tests'] });
      toast({
        title: "Success",
        description: "Dressage test removed from event",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};
