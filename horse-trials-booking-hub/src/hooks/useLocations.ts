import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client'; // Adjust path as needed
import { useToast } from '@/hooks/use-toast'; // Assuming you have this for notifications

// Define the Location type based on your Supabase table structure
export interface Location {
  id: string; // Typically UUID from Supabase
  name: string;
  event_id: string;
  capacity?: number | null;
  activity_type?: string | null; // Keep as activity_type in database
  description?: string | null;
  created_at?: string;
}

const LOCATIONS_QUERY_KEY = 'locations';

// Fetch all locations for a specific event
export const useLocations = (eventId: string | undefined) => {
  return useQuery<Location[], Error>({
    queryKey: [LOCATIONS_QUERY_KEY, eventId],
    queryFn: async () => {
      if (!eventId) return [];
      console.log(`[useLocations hook] Querying Supabase for eventId: ${eventId}`); // Debug log
      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .eq('event_id', eventId)
        .eq('is_active', true) // Only get active locations
        .order('name', { ascending: true });
      console.log('[useLocations hook] Supabase response:', { data, error }); // Debug log
      if (error) throw error;
      return data || [];
    },
    enabled: !!eventId, // Only run query if eventId is available
  });
};

// Create a new location
export const useCreateLocation = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<Location, Error, Omit<Location, 'id' | 'created_at'>>({
    mutationFn: async (newLocation) => {
      const { data, error } = await supabase
        .from('locations')
        .insert([newLocation])
        .select()
        .single(); // Assuming you want the created record back
      if (error) throw error;
      if (!data) throw new Error('Location creation failed, no data returned.');
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [LOCATIONS_QUERY_KEY, data.event_id] });
      toast({ title: 'Success', description: 'Location created successfully.' });
    },
    onError: (error) => {
      toast({ title: 'Error', description: `Failed to create location: ${error.message}`, variant: 'destructive' });
    },
  });
};

// Update an existing location
export const useUpdateLocation = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<Location, Error, Partial<Location> & { id: string }>({
    mutationFn: async (updatedLocation) => {
      const { id, ...updateData } = updatedLocation;
      const { data, error } = await supabase
        .from('locations')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      if (error) throw error;
      if (!data) throw new Error('Location update failed, no data returned.');
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [LOCATIONS_QUERY_KEY, data.event_id] });
      queryClient.invalidateQueries({ queryKey: [LOCATIONS_QUERY_KEY, 'detail', data.id] }); // If you have a detail query
      toast({ title: 'Success', description: 'Location updated successfully.' });
    },
    onError: (error) => {
      toast({ title: 'Error', description: `Failed to update location: ${error.message}`, variant: 'destructive' });
    },
  });
};

// Delete a location
export const useDeleteLocation = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<void, Error, { locationId: string; eventId: string }>({
    mutationFn: async ({ locationId }) => {
      const { error } = await supabase.from('locations').delete().eq('id', locationId);
      if (error) throw error;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [LOCATIONS_QUERY_KEY, variables.eventId] });
      toast({ title: 'Success', description: 'Location deleted successfully.' });
    },
    onError: (error) => {
      toast({ title: 'Error', description: `Failed to delete location: ${error.message}`, variant: 'destructive' });
    },
  });
};