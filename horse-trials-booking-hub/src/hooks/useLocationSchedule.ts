import { useMemo } from 'react';
import { useActivities, Activity } from './useActivities'; // Assuming useActivities will be adapted for locations or a new useLocationActivities will be used

export interface ScheduleItem {
  id: string;
  type: 'activity' | 'break';
  start_time: string; // ISO string
  end_time: string;   // ISO string
  duration_minutes: number;
  activity_type?: string; // From Activity table
  level?: string | null;      // From Activity table
  description?: string | null; // From Activity table
}

export interface ScheduleStats {
  activity_count: number;
  activity_minutes: number;
  break_count: number;
  break_minutes: number;
  start_time: string | null; // Earliest start_time
  end_time: string | null;   // Latest end_time
  total_duration_minutes?: number; // Added for consistency with ArenaScheduleView
}

export const useLocationSchedule = (locationId?: string, selectedDate?: string) => {
  // This will now fetch activities based on locationId.
  // Ensure useActivities hook is updated to filter by location_id if your activities table schema has changed.
  const { data: activities, isLoading, error } = useActivities(locationId); 
  
  const schedule = useMemo(() => {
    if (!activities || activities.length === 0) {
      return [];
    }
    
    // Filter activities by selected date if provided
    let filteredActivities = activities;
    if (selectedDate) {
      filteredActivities = activities.filter(activity => {
        const activityDate = new Date(activity.start_time).toISOString().split('T')[0];
        return activityDate === selectedDate;
      });
    }
    
    if (filteredActivities.length === 0) {
      return [];
    }
    
    // Sort activities by start_time
    const sortedActivities = [...filteredActivities].sort((a, b) => 
      new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
    );
    
    const scheduleItems: ScheduleItem[] = [];
    
    // Process each activity and identify breaks
    for (let i = 0; i < sortedActivities.length; i++) {
      const activity = sortedActivities[i];
      const activityStart = new Date(activity.start_time);
      const activityEnd = new Date(activity.end_time);
      
      // Calculate duration in minutes
      const durationMinutes = Math.round((activityEnd.getTime() - activityStart.getTime()) / (1000 * 60));
      
      // Add the activity to the schedule
      scheduleItems.push({
        id: activity.id,
        type: 'activity',
        start_time: activity.start_time,
        end_time: activity.end_time,
        duration_minutes: durationMinutes,
        activity_type: activity.activity_type,
        level: activity.level || null, // Ensure null if undefined
        description: activity.description || null, // Ensure null if undefined
      });
      
      // Check if there's a break before the next activity
      if (i < sortedActivities.length - 1) {
        const nextActivity = sortedActivities[i + 1];
        const nextActivityStart = new Date(nextActivity.start_time);
        
        // If there's a gap between this activity end and next activity start
        if (activityEnd.getTime() < nextActivityStart.getTime()) {
          const breakDurationMinutes = Math.round(
            (nextActivityStart.getTime() - activityEnd.getTime()) / (1000 * 60)
          );
          
          // Only add breaks that are at least 5 minutes (or adjust as needed)
          if (breakDurationMinutes >= 1) { // ArenaSchedule used >=5, adjust if needed
            scheduleItems.push({
              id: `break-${activity.id}-${nextActivity.id}`,
              type: 'break',
              start_time: activity.end_time,
              end_time: nextActivity.start_time,
              duration_minutes: breakDurationMinutes
            });
          }
        }
      }
    }
    
    return scheduleItems;
  }, [activities, selectedDate]);
  
  // Calculate overall schedule statistics
  const scheduleStats = useMemo(() => {
    if (!schedule || schedule.length === 0) {
      return { start_time: null, end_time: null, total_duration_minutes: 0, activity_minutes: 0, break_minutes: 0, activity_count: 0, break_count: 0 };
    }
    
    const statsInitial: ScheduleStats = { start_time: schedule[0].start_time, end_time: schedule[schedule.length - 1].end_time, activity_minutes: 0, break_minutes: 0, activity_count: 0, break_count: 0, total_duration_minutes: 0 };
    
    return schedule.reduce((acc, item) => {
      if (item.type === 'activity') {
        acc.activity_minutes += item.duration_minutes;
        acc.activity_count++;
      } else {
        acc.break_minutes += item.duration_minutes;
        acc.break_count++;
      }
      acc.total_duration_minutes = acc.activity_minutes + acc.break_minutes;
      return acc;
    }, statsInitial);
  }, [schedule]);
  
  // Format time for display (e.g., "09:30 AM")
  const formatTime = (isoTime: string | null) => {
    if (!isoTime) return '';
    const date = new Date(isoTime);
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };
  
  return {
    schedule,
    scheduleStats,
    formatTime,
    isLoading,
    error
  };
};