import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { 
  useArenas, 
  useCreateArena, 
  useUpdateArena, 
  useDeleteArena,
  type Arena 
} from './useArenas';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockSupabase = vi.mocked(await import('@/integrations/supabase/client')).supabase;
const mockUseToast = vi.mocked(await import('@/hooks/use-toast')).useToast;

// Test data
const mockArena: Arena = {
  id: 'arena-1',
  name: 'Main Arena',
  activity_type: 'dressage',
  slot_duration_minutes: 45,
  fixed_level: 'Training',
  description: 'Main dressage arena',
  event_id: 'event-1',
  created_at: '2024-01-01T00:00:00Z',
  created_by: 'user-1'
};

const mockArenas: Arena[] = [
  mockArena,
  {
    id: 'arena-2',
    name: 'Warm-up Arena',
    activity_type: 'jumping',
    slot_duration_minutes: 30,
    event_id: 'event-1',
    created_at: '2024-01-01T00:00:00Z',
    created_by: 'user-1'
  }
];

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useArenas', () => {
  const mockToast = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseToast.mockReturnValue({ toast: mockToast });
    
    // Mock Supabase chain
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          order: vi.fn().mockResolvedValue({
            data: mockArenas,
            error: null,
          }),
        }),
        order: vi.fn().mockResolvedValue({
          data: mockArenas,
          error: null,
        }),
      }),
      insert: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockArena,
            error: null,
          }),
        }),
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockArena,
              error: null,
            }),
          }),
        }),
      }),
      delete: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
      }),
    } as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('useArenas', () => {
    it('should fetch arenas for specific event successfully', async () => {
      const { result } = renderHook(() => useArenas('event-1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockArenas);
      expect(mockSupabase.from).toHaveBeenCalledWith('arenas');
    });

    it('should handle error when fetching arenas fails', async () => {
      const error = new Error('Database error');
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({
              data: null,
              error,
            }),
          }),
        }),
      } as any);

      const { result } = renderHook(() => useArenas('event-1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBe(error);
    });

    it('should not query when eventId is not provided', () => {
      const { result } = renderHook(() => useArenas(), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.data).toBeUndefined();
      expect(mockSupabase.from).not.toHaveBeenCalled();
    });

    it('should have correct query configuration', () => {
      const { result } = renderHook(() => useArenas('event-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.data).toBeUndefined();
      expect(result.current.isLoading).toBe(true);
    });
  });

  describe('useCreateArena', () => {
    it('should create arena successfully', async () => {
      const newArena = {
        name: 'New Arena',
        activity_type: 'dressage',
        slot_duration_minutes: 45,
        event_id: 'event-1'
      };

      const { result } = renderHook(() => useCreateArena(), {
        wrapper: createWrapper(),
      });

      result.current.mutate(newArena);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('arenas');
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Arena created successfully",
      });
    });

    it('should handle error when creating arena fails', async () => {
      const error = new Error('Creation failed');
      mockSupabase.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error,
            }),
          }),
        }),
      } as any);

      const { result } = renderHook(() => useCreateArena(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        name: 'Test Arena',
        activity_type: 'dressage',
        slot_duration_minutes: 45,
        event_id: 'event-1'
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBe(error);
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Creation failed",
        variant: "destructive",
      });
    });

    it('should invalidate arenas query on success', async () => {
      const newArena = {
        name: 'Test Arena',
        activity_type: 'dressage',
        slot_duration_minutes: 45,
        event_id: 'event-1'
      };

      const { result } = renderHook(() => useCreateArena(), {
        wrapper: createWrapper(),
      });

      result.current.mutate(newArena);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Arena created successfully",
      });
    });
  });

  describe('useUpdateArena', () => {
    it('should update arena successfully', async () => {
      const updateData = {
        id: 'arena-1',
        name: 'Updated Arena Name',
        slot_duration_minutes: 60
      };

      const { result } = renderHook(() => useUpdateArena(), {
        wrapper: createWrapper(),
      });

      result.current.mutate(updateData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('arenas');
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Arena updated successfully",
      });
    });

    it('should handle error when updating arena fails', async () => {
      const error = new Error('Update failed');
      mockSupabase.from.mockReturnValue({
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: null,
                error,
              }),
            }),
          }),
        }),
      } as any);

      const { result } = renderHook(() => useUpdateArena(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        id: 'arena-1',
        name: 'Updated Name'
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBe(error);
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Update failed",
        variant: "destructive",
      });
    });

    it('should invalidate arenas query on success', async () => {
      const updateData = {
        id: 'arena-1',
        name: 'Updated Name'
      };

      const { result } = renderHook(() => useUpdateArena(), {
        wrapper: createWrapper(),
      });

      result.current.mutate(updateData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Arena updated successfully",
      });
    });
  });

  describe('useDeleteArena', () => {
    it('should delete arena successfully', async () => {
      const { result } = renderHook(() => useDeleteArena(), {
        wrapper: createWrapper(),
      });

      result.current.mutate('arena-1');

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('arenas');
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Arena deleted successfully",
      });
    });

    it('should handle error when deleting arena fails', async () => {
      const error = new Error('Delete failed');
      mockSupabase.from.mockReturnValue({
        delete: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({
            data: null,
            error,
          }),
        }),
      } as any);

      const { result } = renderHook(() => useDeleteArena(), {
        wrapper: createWrapper(),
      });

      result.current.mutate('arena-1');

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBe(error);
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Delete failed",
        variant: "destructive",
      });
    });

    it('should invalidate arenas query on success', async () => {
      const { result } = renderHook(() => useDeleteArena(), {
        wrapper: createWrapper(),
      });

      result.current.mutate('arena-1');

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Arena deleted successfully",
      });
    });
  });
}); 