import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface CancelBookingParams {
  bookingId: string;
  timeSlotId: string;
  eventId?: string; // Optional: for more targeted query invalidation
}

const cancelBookingInDb = async ({ bookingId, timeSlotId }: CancelBookingParams) => {
  // Step 1: Get the booking_code from the booking
  const { data: booking, error: bookingError } = await supabase
    .from('bookings')
    .select('booking_code')
    .eq('id', bookingId)
    .single();

  if (bookingError) {
    console.error('Error fetching booking:', bookingError);
    throw new Error(`Failed to fetch booking: ${bookingError.message}`);
  }

  // Step 2: Delete the booking slot
  const { error: slotError } = await supabase
    .from('booking_slots')
    .delete()
    .match({ booking_code: booking.booking_code, time_slot_id: timeSlotId });

  if (slotError) {
    console.error('Error deleting booking slot:', slotError);
    throw new Error(`Failed to delete booking slot: ${slotError.message}`);
  }

  // Step 3: Check if there are any remaining slots for this booking
  const { data: remainingSlots, error: countError } = await supabase
    .from('booking_slots')
    .select('id')
    .eq('booking_code', booking.booking_code);

  if (countError) {
    console.error('Error checking remaining slots:', countError);
    throw new Error(`Failed to check remaining slots: ${countError.message}`);
  }

  // Step 4: If no slots remain, delete the booking
  if (!remainingSlots || remainingSlots.length === 0) {
    const { error: deleteBookingError } = await supabase
      .from('bookings')
      .delete()
      .eq('id', bookingId);

    if (deleteBookingError) {
      console.error('Error deleting booking:', deleteBookingError);
      throw new Error(`Failed to delete booking: ${deleteBookingError.message}`);
    }
  }

  // Step 5: Update the time slot to be available
  const { error: timeSlotError } = await supabase
    .from('time_slots')
    .update({ is_booked: false })
    .eq('id', timeSlotId);

  if (timeSlotError) {
    console.error('Error updating time slot:', timeSlotError);
    throw new Error(`Failed to update time slot: ${timeSlotError.message}`);
  }

  // Step 6: Delete any potential reservation for this slot (cleanup)
  const { error: reservationError } = await supabase
    .from('slot_reservations')
    .delete()
    .eq('time_slot_id', timeSlotId);

  if (reservationError) {
    // This is not critical if no reservation exists, so just log it.
    console.warn('Could not delete slot reservation (it might not have existed):', reservationError.message);
  }

  return { success: true };
};

export const useCancelBooking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: cancelBookingInDb,
    onSuccess: (_data, variables) => {
      toast.success('Booking cancelled successfully!');
      queryClient.invalidateQueries({ queryKey: ['bookings', variables.eventId] });
      queryClient.invalidateQueries({ queryKey: ['time_slots'] }); // Invalidate all time slots as one was freed
      queryClient.invalidateQueries({ queryKey: ['slot_reservations'] });
    },
    onError: (error: Error) => {
      toast.error(`Cancellation failed: ${error.message}`);
    },
  });
};