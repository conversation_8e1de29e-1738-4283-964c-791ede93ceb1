import { useMemo } from 'react';
import { ComprehensiveUser } from '@/hooks/useAllUsers';

export const useUserFiltering = (
  users: ComprehensiveUser[] | undefined,
  searchTerm: string,
  roleFilter: string
) => {
  const filteredUsers = useMemo(() => {
    if (!users) return [];
    
    return users.filter(user => {
      // Add null checks for user properties
      if (!user || !user.full_name || !user.auth_email) {
        return false;
      }
      
      const matchesSearch = user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           user.auth_email.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Use the user_role directly from the comprehensive view
      const matchesRole = roleFilter === 'all' || user.user_role === roleFilter;
      
      return matchesSearch && matchesRole;
    });
  }, [users, searchTerm, roleFilter]);

  return filteredUsers;
}; 