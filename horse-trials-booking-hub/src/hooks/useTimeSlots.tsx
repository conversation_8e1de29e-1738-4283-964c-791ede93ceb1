import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface TimeSlot {
  id: string;
  location_id: string; // Changed from arena_id
  start_time: string;
  end_time: string;
  level?: string;
  is_booked?: boolean;
  created_at?: string;
  activity_id?: string; // Add activity_id to track which activity created this slot
}

// Helper function to ensure consistent timezone handling
const toLocalISOString = (date: Date): string => {
  // Get the local timezone offset in minutes
  const tzOffset = date.getTimezoneOffset() * 60000; // convert to milliseconds
  
  // Create a new date that's adjusted for the local timezone
  const localDate = new Date(date.getTime() - tzOffset);
  
  // Return the ISO string without the 'Z' at the end (which indicates UTC)
  return localDate.toISOString().slice(0, -1);
};

export const useTimeSlots = (locationId?: string) => { // Changed parameter name
  return useQuery({
    queryKey: ['time_slots', locationId],
    queryFn: async () => {
      if (!locationId) return [];
      

      const { data, error } = await supabase
        .from('time_slots')
        .select('*')
        .eq('location_id', locationId) // Changed to location_id
        .order('start_time');

      if (error) {
        console.error('Error fetching time slots:', error);
        throw error;
      }

      
      return data as TimeSlot[];
    },
    enabled: !!locationId,
    staleTime: 1000 * 30, // Reduce to 30 seconds to refresh more frequently
    refetchOnWindowFocus: true, // Refresh when window regains focus
  });
};

export const useGenerateTimeSlots = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ activityId, slotDurationMinutes }: { activityId: string, slotDurationMinutes: number }) => {
      // First, get the activity details
      const { data: activity } = await supabase
        .from('activities')
        .select('*')
        .eq('id', activityId)
        .single();

      if (!activity) {
        throw new Error('Activity not found');
      }

      // Get existing time slots that overlap with this activity's time range
      const { data: existingSlots } = await supabase
        .from('time_slots')
        .select('*')
        .eq('location_id', activity.location_id)
        .gte('start_time', activity.start_time)
        .lte('end_time', activity.end_time)
        .order('start_time');

      // Generate potential new time slots
      const startTime = new Date(activity.start_time);
      const endTime = new Date(activity.end_time);
      const potentialSlots = [];
      
      let currentTime = new Date(startTime);
      while (currentTime < endTime) {
        const slotEnd = new Date(currentTime.getTime() + slotDurationMinutes * 60000);
        if (slotEnd <= endTime) {
          potentialSlots.push({
            start_time: toLocalISOString(currentTime),
            end_time: toLocalISOString(slotEnd)
          });
        }
        currentTime = slotEnd;
      }

      // Filter out slots that would overlap with existing ones
      const slotsToCreate = potentialSlots.filter(newSlot => {
        const newStart = new Date(newSlot.start_time);
        const newEnd = new Date(newSlot.end_time);
        
        // Check if this slot overlaps with any existing slot
        const hasOverlap = existingSlots?.some(existingSlot => {
          const existingStart = new Date(existingSlot.start_time);
          const existingEnd = new Date(existingSlot.end_time);
          
          // Check for any overlap
          return (newStart < existingEnd && newEnd > existingStart);
        });
        
        // Keep slots that don't overlap
        return !hasOverlap;
      });

      // Create the non-overlapping slots
      if (slotsToCreate.length > 0) {
        const slotsWithMetadata = slotsToCreate.map(slot => ({
          ...slot,
          location_id: activity.location_id,
          level: activity.level,
          is_booked: false,
          activity_id: activityId,
          // Don't set audit fields - let the triggers handle them
          // created_by, updated_by, etc. should be set by triggers
        }));

        const { data, error } = await supabase
          .from('time_slots')
          .insert(slotsWithMetadata)
          .select();

        if (error) {
          console.error('Time slot insertion error:', error);
          console.error('Error details:', error.details, error.hint, error.message);
          throw error;
        }
        
        // Return both existing and new slots for the activity
        return {
          newSlots: data,
          existingSlots: existingSlots || [],
          totalSlots: (existingSlots || []).length + data.length
        };
      }

      return {
        newSlots: [],
        existingSlots: existingSlots || [],
        totalSlots: (existingSlots || []).length
      };
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
      toast({
        title: "Success",
        description: `Time slots updated successfully. Created ${result.newSlots.length} new slots (${result.totalSlots} total slots).`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// New function to regenerate all time slots for an arena
export const useRegenerateLocationTimeSlots = () => { // Renamed hook
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ locationId, selectedDate }: { locationId: string, selectedDate?: string }) => { // Changed parameter
      // First check if any slots are already booked
      const { data: bookedSlots, error: bookedError } = await supabase
        .from('time_slots')
        .select('id')
        .eq('location_id', locationId) // Changed to location_id
        .eq('is_booked', true);

      if (bookedError) throw bookedError;
      
      if (bookedSlots && bookedSlots.length > 0) {
        throw new Error('Cannot regenerate time slots: some slots are already booked');
      }
      // Fetch location details if needed for a default slot duration.
      // For now, assuming slot_duration_minutes comes from each activity.
      // If you need a location-level default, you would fetch it here:
      // const { data: locationData, error: locationError } = await supabase
      //   .from('locations') // Query 'locations' table
      //   .select('default_slot_duration_minutes') // Assuming a column for default duration
      //   .eq('id', locationId)
      //   .single();
      // if (locationError) throw locationError;
      // if (!locationData) throw new Error('Location not found');
      // const defaultSlotDurationMinutes = locationData.default_slot_duration_minutes;

      // Get all activities for this location
      let activitiesQuery = supabase
        .from('activities') // Ensure this table name is correct
        .select('*')
        .eq('location_id', locationId); // Filter by location_id
        
      // If a specific date is selected, filter activities by that date
      if (selectedDate) {
        const dayStart = `${selectedDate}T00:00:00`;
        const dayEnd = `${selectedDate}T23:59:59`;
        activitiesQuery = activitiesQuery
          .gte('start_time', dayStart)
          .lt('start_time', dayEnd);
      }
      
      activitiesQuery = activitiesQuery.order('start_time');
      
      const { data: activities, error: activitiesError } = await activitiesQuery;

      if (activitiesError) throw activitiesError;
      if (!activities || activities.length === 0) {
        throw new Error('No activities found for this location');
      }

      // Delete existing time slots for the activities we're regenerating
      let deleteQuery = supabase
        .from('time_slots')
        .delete()
        .eq('location_id', locationId); // Changed to location_id
        
      // If a specific date is selected, only delete slots for that date
      if (selectedDate) {
        const dayStart = `${selectedDate}T00:00:00`;
        const dayEnd = `${selectedDate}T23:59:59`;
        deleteQuery = deleteQuery
          .gte('start_time', dayStart)
          .lt('start_time', dayEnd);
      }
      
      await deleteQuery;

      // Generate new time slots for each activity
      const allSlots = [];
      
      for (const activity of activities) {
        const startTime = new Date(activity.start_time);
        const endTime = new Date(activity.end_time);
        const activitySlotDuration = activity.slot_duration_minutes; // Get duration from activity
        
        let currentTime = new Date(startTime);
        while (currentTime < endTime) {
          const slotEnd = new Date(currentTime.getTime() + activitySlotDuration * 60000);
          if (slotEnd <= endTime) {
            allSlots.push({
              location_id: locationId, // Changed to location_id
              start_time: toLocalISOString(currentTime),
              end_time: toLocalISOString(slotEnd),
              level: activity.level,
              is_booked: false,
              activity_id: activity.id // Add activity_id to link the slot to the activity
            });
          }
          currentTime = slotEnd;
        }
      }

      if (allSlots.length > 0) {
        const { data, error } = await supabase
          .from('time_slots')
          .insert(allSlots)
          .select();

        if (error) {
          console.error('Time slot insertion error:', error);
          console.error('Error details:', error.details, error.hint, error.message);
          throw error;
        }
        return data;
      }

      return [];
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
      toast({
        title: "Success",
        description: "All time slots regenerated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useRegenerateActivitySlots = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ activityId, slotDurationMinutes }: { activityId: string, slotDurationMinutes: number }) => {
      // First, get the activity details
      const { data: activity } = await supabase
        .from('activities')
        .select('*')
        .eq('id', activityId)
        .single();

      if (!activity) {
        throw new Error('Activity not found');
      }

      // Check for any booked and paid slots
      const { data: bookedSlots } = await supabase
        .from('time_slots')
        .select('id, is_booked')
        .eq('activity_id', activityId)
        .eq('is_booked', true);

      const { data: paidBookings } = await supabase
        .from('bookings')
        .select('time_slot_id, payment_status')
        .in('time_slot_id', (bookedSlots || []).map(slot => slot.id))
        .eq('payment_status', 'paid');

      if (paidBookings && paidBookings.length > 0) {
        throw new Error('Cannot regenerate slots: some slots have paid bookings');
      }

      // Delete all existing slots for this activity
      await supabase
        .from('time_slots')
        .delete()
        .eq('activity_id', activityId);

      // Generate new time slots
      const startTime = new Date(activity.start_time);
      const endTime = new Date(activity.end_time);
      const slots = [];
      
      let currentTime = new Date(startTime);
      while (currentTime < endTime) {
        const slotEnd = new Date(currentTime.getTime() + slotDurationMinutes * 60000);
        if (slotEnd <= endTime) {
          slots.push({
            location_id: activity.location_id,
            start_time: toLocalISOString(currentTime),
            end_time: toLocalISOString(slotEnd),
            level: activity.level,
            is_booked: false,
            activity_id: activityId
          });
        }
        currentTime = slotEnd;
      }

      if (slots.length > 0) {
        const { data, error } = await supabase
          .from('time_slots')
          .insert(slots)
          .select();

        if (error) throw error;
        return {
          newSlots: data,
          totalSlots: data.length
        };
      }

      return {
        newSlots: [],
        totalSlots: 0
      };
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
      toast({
        title: "Success",
        description: `Time slots regenerated successfully. Created ${result.totalSlots} new slots.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteTimeSlotsForDate = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ locationId, date }: { locationId: string, date: string }) => {
      const dayStart = `${date}T00:00:00`;
      const dayEnd = `${date}T23:59:59`;
      
      const { data, error } = await supabase
        .from('time_slots')
        .delete()
        .eq('location_id', locationId)
        .gte('start_time', dayStart)
        .lt('start_time', dayEnd);

      if (error) throw error;
      return data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
      toast({
        title: "Success",
        description: `Deleted time slots for ${variables.date}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};
