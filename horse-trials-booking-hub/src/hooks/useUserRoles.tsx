import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase, supabaseAdmin } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useSession } from '@supabase/auth-helpers-react';
import { getErrorMessage } from '@/types/errors';

export interface UserRole {
  id: string;
  user_id: string;
  role: 'super_admin' | 'organizer' | 'user';
  created_at: string;
}

export interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}

export interface UserFromHook {
  id: string;
  full_name: string;
  email: string;
  phone?: string;
  user_roles: { role: 'super_admin' | 'organizer' | 'user' }[];
}

// Hook to create a new user
export const useCreateUser = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (userData: { email: string; full_name: string; phone?: string; role: string }) => {
      // Check if supabaseAdmin is available
      if (!supabaseAdmin) {
        console.error('❌ supabaseAdmin is null - check VITE_SUPABASE_SERVICE_ROLE_KEY environment variable');
        throw new Error('Admin client not available. Please check your environment configuration.');
      }

      console.log('🔍 Creating user with data:', userData);
      
      // Generate a temporary password
      const tempPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
      
      console.log('🔍 Generated temp password, creating auth user...');

      // Create the user in Supabase Auth using admin client
      const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
        email: userData.email,
        password: tempPassword,
        email_confirm: true, // Auto-confirm email
        user_metadata: {
          full_name: userData.full_name
        }
      });

      if (authError) {
        console.error('❌ Auth error:', authError);
        throw authError;
      }

      console.log('✅ Auth user created:', authData.user.id);

      // Debug: Log the email data we're about to use
      console.log('🔍 About to create/update profile with email:', userData.email);
      console.log('🔍 Full userData:', userData);

      // Check if profile already exists
      const { data: existingProfile } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single();

      let profileResult;
      if (existingProfile) {
        console.log('🔍 Profile already exists, updating...');
        console.log('🔍 Existing profile data:', existingProfile);
        // Update existing profile
        const { data: updateResult, error: profileError } = await supabaseAdmin
          .from('profiles')
          .update({
            full_name: userData.full_name,
            email: userData.email,
            phone: userData.phone || null
          })
          .eq('id', authData.user.id)
          .select()
          .single();
        
        if (profileError) {
          console.error('❌ Profile update error:', profileError);
          throw profileError;
        }
        profileResult = updateResult;
      } else {
        console.log('🔍 Creating new profile...');
        // Create new profile
        const profileData = {
          id: authData.user.id,
          full_name: userData.full_name,
          email: userData.email,
          phone: userData.phone || null
        };
        
        console.log('🔍 Inserting profile with data:', profileData);
        
        const { data: insertResult, error: profileError } = await supabaseAdmin
          .from('profiles')
          .insert(profileData)
          .select()
          .single();

        if (profileError) {
          console.error('❌ Profile error:', profileError);
          throw profileError;
        }
        profileResult = insertResult;
      }

      console.log('✅ Profile created/updated:', profileResult);

      // Since the handle_new_user trigger now creates a default 'user' role,
      // we just need to update it to the role selected by the admin.
      console.log('🔍 Updating user role...');
      const { error: roleError } = await supabaseAdmin
        .from('user_roles')
        .update({ role: userData.role })
        .eq('user_id', authData.user.id);

      if (roleError) {
        console.error('❌ User role update error:', roleError);
        throw roleError;
      }

      console.log('✅ User role updated');

      // Send password reset email using admin client
      const { error: resetError } = await supabaseAdmin.auth.admin.generateLink({
        type: 'recovery',
        email: userData.email
      });

      if (resetError) {
        console.error('❌ Reset email error:', resetError);
        throw resetError;
      }

      console.log('✅ Password reset email sent');

      return { user: authData.user, tempPassword };
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['all-users-comprehensive'] });
      toast({
        title: 'User created successfully',
        description: `User ${data.user.email} has been created. A password reset email has been sent to them.`,
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error creating user',
        description: getErrorMessage(error),
        variant: 'destructive',
      });
    },
  });
};

// Hook to get current user's roles
export const useUserRoles = (enabled = true) => {
  const session = useSession();
  const user = session?.user;
  
  return useQuery({
    queryKey: ['user-roles', user?.id],
    queryFn: async () => {
      if (!user) {
        console.log('❌ DEBUG: useUserRoles - No user found');
        return null;
      }

      console.log('🔍 DEBUG: useUserRoles - User:', user.id, user.email);
      
      try {
        const { data, error } = await supabase
          .from('user_roles')
          .select('*')
          .eq('user_id', user.id);

        console.log('🔍 DEBUG: useUserRoles - Query result:', { data, error });

        if (error) {
          console.error('❌ DEBUG: useUserRoles - Error:', error);
          throw error;
        }
        
        console.log('✅ DEBUG: useUserRoles - Found roles:', data);
        return data as UserRole[];
      } catch (error) {
        console.error('❌ DEBUG: useUserRoles - Exception:', error);
        throw error;
      }
    },
    // Only run the query when we have a user and session AND enabled is true
    enabled: enabled && !!user && !!session,
    // Add retry logic and better error handling
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    // Refetch when window gains focus to catch session changes
    refetchOnWindowFocus: false,
    // Refetch on mount to ensure we get the latest data
    refetchOnMount: true,
    // Add stale time to prevent unnecessary refetches
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to check if user is super admin
export const useIsSuperAdmin = (enabled = true) => {
  const { data: userRoles, isLoading, error } = useUserRoles(enabled);
  const isSuperAdmin = userRoles?.some(role => role.role === 'super_admin') || false;
  
  return {
    data: isSuperAdmin,
    isLoading,
    error
  };
};

// Hook to get all users (for super admin to manage)
export const useAllUsers = () => {
  return useQuery<UserFromHook[]>({
    queryKey: ['all-users-comprehensive'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_all_users_with_roles');

      if (error) {
        console.error('❌ DEBUG: useAllUsers - Error:', error);
        throw error;
      }

      return data as UserFromHook[];
    },
    // Add caching configuration to prevent unnecessary refetches
    staleTime: 5 * 60 * 1000, // 5 minutes - data is fresh for 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes - keep in cache for 10 minutes
    refetchOnWindowFocus: false, // Don't refetch when window gains focus
    refetchOnMount: false, // Don't refetch on mount if data exists
    refetchOnReconnect: false, // Don't refetch on reconnect
  });
};

// Hook to update user role
export const useUpdateUserRole = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { data: currentUserRoles } = useUserRoles();

  return useMutation({
    mutationFn: async ({ userId, role }: {
      userId: string;
      role: 'super_admin' | 'organizer' | 'user';
    }) => {
      // Check if current user is super admin
      const isSuperAdmin = currentUserRoles?.some(ur => ur.role === 'super_admin');
      if (!isSuperAdmin) {
        throw new Error('Only super admins can update user roles');
      }

      // First, delete existing roles for this user
      await supabase
        .from('user_roles')
        .delete()
        .eq('user_id', userId);

      // Then insert the new role
      const { data, error } = await supabase
        .from('user_roles')
        .insert({
          user_id: userId,
          role,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['all-users-comprehensive'] });
      queryClient.invalidateQueries({ queryKey: ['user-roles'] });
      toast({
        title: 'Role updated',
        description: 'The user role has been successfully updated.',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error updating role',
        description: getErrorMessage(error),
        variant: 'destructive',
      });
    },
  });
};

// Hook to get events that the current user can access
export const useAccessibleEvents = () => {
  const { data: userRoles } = useUserRoles();
  const session = useSession();
  const user = session?.user;
  const isSuperAdmin = userRoles?.some(role => role.role === 'super_admin') || false;

  return useQuery({
    queryKey: ['accessible-events', isSuperAdmin, user?.id],
    queryFn: async () => {
      if (!user) {
        console.log('❌ DEBUG: useAccessibleEvents - No user found');
        return [];
      }

      let query = supabase.from('events').select('*');
      
      if (isSuperAdmin) {
        // Super admins can see all events
        query = query.order('start_date', { ascending: false });
      } else {
        // Regular users can only see events they organize or created
        query = query.or(`organizer_id.eq.${user.id},created_by.eq.${user.id}`)
                    .order('start_date', { ascending: false });
      }

      const { data: events, error } = await query;

      if (error) {
        console.error('❌ DEBUG: useAccessibleEvents - Error:', error);
        throw error;
      }

      console.log('🔍 DEBUG: Found events:', events?.length);
      
      // Get organizer information for each event
      const eventsWithOrganizers = await Promise.all(
        events.map(async (event) => {
          // Get organizer profile if organizer_id exists
          let organizer = null;
          if (event.organizer_id) {
            const { data: organizerData, error: organizerError } = await supabase
              .from('organizers')
              .select('id, full_name, email, company_name, business_name')
              .eq('id', event.organizer_id)
              .single();

            if (!organizerError && organizerData) {
              organizer = organizerData;
            }
          }

          return {
            ...event,
            organizer
          };
        })
      );
      
      return eventsWithOrganizers || [];
    },
    enabled: !!user && !!session,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    refetchOnWindowFocus: false,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook to get current user's role as a string
export const useUserRole = (enabled = true) => {
  const { data: userRoles, isLoading } = useUserRoles(enabled);
  
  // Get the highest priority role (super_admin > organizer > user)
  const getHighestRole = (roles: UserRole[] | null | undefined): string => {
    if (!roles || roles.length === 0) return 'user';
    
    // Check for super_admin first
    if (roles.some(role => role.role === 'super_admin')) {
      return 'super_admin';
    }
    
    // Then check for organizer
    if (roles.some(role => role.role === 'organizer')) {
      return 'organizer';
    }
    
    // Default to user
    return 'user';
  };
  
  const role = getHighestRole(userRoles);
  
  return {
    userRole: role,
    isLoading
  };
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (userId: string) => {
      const { data, error } = await supabase.rpc('delete_user_completely', {
        user_id: userId,
      });

      if (error) {
        // This handles RPC-level errors, like the function not existing
        throw new Error(`RPC Error: ${error.message}`);
      }

      if (data === false) {
        // This handles the explicit "RETURN FALSE" from within the PL/pgSQL function
        throw new Error('An error occurred in the database during the deletion process.');
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['all-users'] });
      queryClient.invalidateQueries({ queryKey: ['all-users-comprehensive'] });
      toast({
        title: 'Success',
        description: 'User has been permanently deleted.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Deletion Failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
};
