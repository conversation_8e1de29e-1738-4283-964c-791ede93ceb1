import React from 'react';
import { renderHook } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useAppSettings } from './useAppSettings';
import { usePricingSettings } from './usePricingSettings';

// Mock usePricingSettings
vi.mock('./usePricingSettings', () => ({
  usePricingSettings: vi.fn(),
}));

const mockUsePricingSettings = vi.mocked(usePricingSettings);

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useAppSettings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getActivityPrice', () => {
    it('should return correct price for dressage activity', () => {
      const mockPricingSettings = {
        dressage_price: 45,
        show_jumping_price: 50,
        cross_country_price: 55,
        cart_retention_minutes: 15,
        currency_symbol: '£'
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getActivityPrice('dressage')).toBe(45);
    });

    it('should return correct price for show_jumping activity', () => {
      const mockPricingSettings = {
        dressage_price: 45,
        show_jumping_price: 50,
        cross_country_price: 55,
        cart_retention_minutes: 15,
        currency_symbol: '£'
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getActivityPrice('show_jumping')).toBe(50);
    });

    it('should return correct price for cross_country activity', () => {
      const mockPricingSettings = {
        dressage_price: 45,
        show_jumping_price: 50,
        cross_country_price: 55,
        cart_retention_minutes: 15,
        currency_symbol: '£'
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getActivityPrice('cross_country')).toBe(55);
    });

    it('should return default price for unknown activity type', () => {
      const mockPricingSettings = {
        dressage_price: 45,
        show_jumping_price: 50,
        cross_country_price: 55,
        cart_retention_minutes: 15,
        currency_symbol: '£'
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getActivityPrice('unknown_activity')).toBe(30);
    });

    it('should return default price when pricing settings are null', () => {
      mockUsePricingSettings.mockReturnValue({
        data: null,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getActivityPrice('dressage')).toBe(30);
      expect(result.current.getActivityPrice('show_jumping')).toBe(30);
      expect(result.current.getActivityPrice('cross_country')).toBe(30);
      expect(result.current.getActivityPrice('unknown_activity')).toBe(30);
    });

    it('should return default price when pricing settings are undefined', () => {
      mockUsePricingSettings.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getActivityPrice('dressage')).toBe(30);
      expect(result.current.getActivityPrice('show_jumping')).toBe(30);
      expect(result.current.getActivityPrice('cross_country')).toBe(30);
      expect(result.current.getActivityPrice('unknown_activity')).toBe(30);
    });
  });

  describe('getCartRetentionMinutes', () => {
    it('should return correct cart retention minutes from pricing settings', () => {
      const mockPricingSettings = {
        dressage_price: 45,
        show_jumping_price: 50,
        cross_country_price: 55,
        cart_retention_minutes: 20,
        currency_symbol: '£'
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getCartRetentionMinutes()).toBe(20);
    });

    it('should return default cart retention minutes when pricing settings are null', () => {
      mockUsePricingSettings.mockReturnValue({
        data: null,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getCartRetentionMinutes()).toBe(10);
    });

    it('should return default cart retention minutes when pricing settings are undefined', () => {
      mockUsePricingSettings.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getCartRetentionMinutes()).toBe(10);
    });

    it('should return default cart retention minutes when cart_retention_minutes is not set', () => {
      const mockPricingSettings = {
        dressage_price: 45,
        show_jumping_price: 50,
        cross_country_price: 55,
        currency_symbol: '£'
        // cart_retention_minutes is missing
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getCartRetentionMinutes()).toBe(10);
    });
  });

  describe('getCurrencySymbol', () => {
    it('should return correct currency symbol from pricing settings', () => {
      const mockPricingSettings = {
        dressage_price: 45,
        show_jumping_price: 50,
        cross_country_price: 55,
        cart_retention_minutes: 15,
        currency_symbol: '€'
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getCurrencySymbol()).toBe('€');
    });

    it('should return default currency symbol when pricing settings are null', () => {
      mockUsePricingSettings.mockReturnValue({
        data: null,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getCurrencySymbol()).toBe('$');
    });

    it('should return default currency symbol when pricing settings are undefined', () => {
      mockUsePricingSettings.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getCurrencySymbol()).toBe('$');
    });

    it('should return default currency symbol when currency_symbol is not set', () => {
      const mockPricingSettings = {
        dressage_price: 45,
        show_jumping_price: 50,
        cross_country_price: 55,
        cart_retention_minutes: 15
        // currency_symbol is missing
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getCurrencySymbol()).toBe('$');
    });
  });

  describe('Loading and Error States', () => {
    it('should pass through loading state from usePricingSettings', () => {
      mockUsePricingSettings.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.isPricingLoading).toBe(true);
      expect(result.current.pricingSettings).toBeUndefined();
    });

    it('should pass through error state from usePricingSettings', () => {
      const error = new Error('Failed to fetch pricing settings');
      mockUsePricingSettings.mockReturnValue({
        data: undefined,
        isLoading: false,
        error
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.isPricingLoading).toBe(false);
      expect(result.current.pricingSettings).toBeUndefined();
    });

    it('should pass through data from usePricingSettings', () => {
      const mockPricingSettings = {
        dressage_price: 45,
        show_jumping_price: 50,
        cross_country_price: 55,
        cart_retention_minutes: 15,
        currency_symbol: '£'
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.pricingSettings).toEqual(mockPricingSettings);
      expect(result.current.isPricingLoading).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty pricing settings object', () => {
      const mockPricingSettings = {};

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getActivityPrice('dressage')).toBe(30);
      expect(result.current.getCartRetentionMinutes()).toBe(10);
      expect(result.current.getCurrencySymbol()).toBe('$');
    });

    it('should handle partial pricing settings object', () => {
      const mockPricingSettings = {
        dressage_price: 45
        // Missing other properties
      };

      mockUsePricingSettings.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useAppSettings(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getActivityPrice('dressage')).toBe(45);
      expect(result.current.getActivityPrice('show_jumping')).toBe(30);
      expect(result.current.getCartRetentionMinutes()).toBe(10);
      expect(result.current.getCurrencySymbol()).toBe('$');
    });
  });
}); 