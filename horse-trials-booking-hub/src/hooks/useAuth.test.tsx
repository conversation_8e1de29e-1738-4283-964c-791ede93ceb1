import { render, renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useAuth, AuthProvider } from './useAuth';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { ReactNode } from 'react';

// Mock dependencies
vi.mock('@/integrations/supabase/client');
vi.mock('@/hooks/use-toast');

const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);

describe('useAuth Hook', () => {
  let queryClient: QueryClient;
  const mockToast = vi.fn();

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    
    vi.clearAllMocks();
    mockUseToast.mockReturnValue({ toast: mockToast });
    
    // Mock Supabase auth methods
    mockSupabase.auth = {
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(),
      signInWithPassword: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    } as any;

    // Default mock implementations
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null,
    });
    
    mockSupabase.auth.onAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: vi.fn() } },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const wrapper = ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>{children}</AuthProvider>
    </QueryClientProvider>
  );

  describe('AuthProvider', () => {
    it('renders children without crashing', async () => {
      // Ensure proper mock setup for this test
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current).toBeDefined();
    });

    it('initializes with loading state', async () => {
      // Delay the session response to test loading state
      let resolveSession: (value: any) => void;
      const sessionPromise = new Promise((resolve) => {
        resolveSession = resolve;
      });
      
      mockSupabase.auth.getSession.mockReturnValue(sessionPromise);

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Should start with loading true
      expect(result.current.loading).toBe(true);

      // Resolve the session
      resolveSession!({
        data: { session: null },
        error: null,
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });
    });

    it('loads initial session correctly', async () => {
      const mockSession = {
        user: { id: 'user1', email: '<EMAIL>' },
        access_token: 'token123',
      };

      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: mockSession },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.session).toEqual(mockSession);
        expect(result.current.user).toEqual(mockSession.user);
      });
    });

    it('handles auth state changes', async () => {
      const mockUser = { id: 'user1', email: '<EMAIL>' };
      let authStateCallback: (event: string, session: any) => void;

      mockSupabase.auth.onAuthStateChange.mockImplementation((callback) => {
        authStateCallback = callback;
        return { data: { subscription: { unsubscribe: vi.fn() } } };
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Simulate auth state change
      act(() => {
        authStateCallback!('SIGNED_IN', { user: mockUser });
      });

      expect(result.current.user).toEqual(mockUser);
    });

    it('invalidates queries on sign in', async () => {
      const mockUser = { id: 'user1', email: '<EMAIL>' };
      let authStateCallback: (event: string, session: any) => void;

      mockSupabase.auth.onAuthStateChange.mockImplementation((callback) => {
        authStateCallback = callback;
        return { data: { subscription: { unsubscribe: vi.fn() } } };
      });

      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Simulate sign in
      act(() => {
        authStateCallback!('SIGNED_IN', { user: mockUser });
      });

      expect(invalidateQueriesSpy).toHaveBeenCalled();
    });

    it('clears queries on sign out', async () => {
      let authStateCallback: (event: string, session: any) => void;

      mockSupabase.auth.onAuthStateChange.mockImplementation((callback) => {
        authStateCallback = callback;
        return { data: { subscription: { unsubscribe: vi.fn() } } };
      });

      const removeQueriesSpy = vi.spyOn(queryClient, 'removeQueries');

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Simulate sign out
      act(() => {
        authStateCallback!('SIGNED_OUT', null);
      });

      expect(removeQueriesSpy).toHaveBeenCalled();
    });
  });

  describe('useAuth Hook', () => {
    beforeEach(() => {
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null,
      });

      mockSupabase.auth.onAuthStateChange.mockReturnValue({
        data: {
          subscription: {
            unsubscribe: vi.fn(),
          },
        },
      });
    });

    it('throws error when used outside AuthProvider', () => {
      expect(() => {
        renderHook(() => useAuth());
      }).toThrow('useAuth must be used within an AuthProvider');
    });

    it('returns auth context when used within AuthProvider', async () => {
      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current).toHaveProperty('user');
      expect(result.current).toHaveProperty('session');
      expect(result.current).toHaveProperty('loading');
      expect(result.current).toHaveProperty('signIn');
      expect(result.current).toHaveProperty('signUp');
      expect(result.current).toHaveProperty('signOut');
    });
  });

  describe('signIn method', () => {
    beforeEach(() => {
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null,
      });

      mockSupabase.auth.onAuthStateChange.mockReturnValue({
        data: {
          subscription: {
            unsubscribe: vi.fn(),
          },
        },
      });
    });

    it('signs in successfully', async () => {
      const mockUser = { id: 'user1', email: '<EMAIL>' };
      const mockSession = { user: mockUser, access_token: 'token123' };

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.signIn('<EMAIL>', 'password');
      });

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
      });
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Signed in successfully",
      });
    });

    it('handles sign in error', async () => {
      const mockError = { message: 'Invalid credentials' };

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: mockError,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await expect(result.current.signIn('<EMAIL>', 'password')).rejects.toEqual(mockError);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Invalid credentials",
        variant: "destructive",
      });
    });
  });

  describe('signUp method', () => {
    beforeEach(() => {
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null,
      });

      mockSupabase.auth.onAuthStateChange.mockReturnValue({
        data: {
          subscription: {
            unsubscribe: vi.fn(),
          },
        },
      });
    });

    it('signs up successfully with full name', async () => {
      const mockUser = { id: 'user1', email: '<EMAIL>' };
      const mockSession = { user: mockUser, access_token: 'token123' };

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.signUp('<EMAIL>', 'password', 'John Doe');
      });

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
        options: {
          data: { full_name: 'John Doe' },
        },
      });
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Account created successfully! Please check your email to verify your account.",
      });
    });

    it('signs up successfully without full name', async () => {
      const mockUser = { id: 'user1', email: '<EMAIL>' };
      const mockSession = { user: mockUser, access_token: 'token123' };

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.signUp('<EMAIL>', 'password');
      });

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
        options: {
          data: { full_name: '' },
        },
      });
    });

    it('handles sign up error', async () => {
      const mockError = { message: 'Email already exists' };

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: null, session: null },
        error: mockError,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await expect(result.current.signUp('<EMAIL>', 'password')).rejects.toEqual(mockError);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Email already exists",
        variant: "destructive",
      });
    });
  });

  describe('signOut method', () => {
    beforeEach(() => {
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null,
      });

      mockSupabase.auth.onAuthStateChange.mockReturnValue({
        data: {
          subscription: {
            unsubscribe: vi.fn(),
          },
        },
      });
    });

    it('signs out successfully', async () => {
      mockSupabase.auth.signOut.mockResolvedValue({
        error: null,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.signOut();
      });

      expect(mockSupabase.auth.signOut).toHaveBeenCalled();
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Signed out successfully",
      });
    });

    it('handles session not found error gracefully', async () => {
      const mockError = { message: 'Session not found' };

      mockSupabase.auth.signOut.mockResolvedValue({
        error: mockError,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.signOut();
      });

      // Should still show success toast and not throw
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Signed out successfully",
      });
    });

    it('handles other sign out errors', async () => {
      const mockError = { message: 'Network error' };

      mockSupabase.auth.signOut.mockResolvedValue({
        error: mockError,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await expect(result.current.signOut()).rejects.toEqual(mockError);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Network error",
        variant: "destructive",
      });
    });

    it('clears local state on sign out', async () => {
      // Start with a user signed in
      const mockUser = { id: 'user1', email: '<EMAIL>' };
      const mockSession = { user: mockUser, access_token: 'token123' };

      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: mockSession },
        error: null,
      });

      mockSupabase.auth.signOut.mockResolvedValue({
        error: null,
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.user).toEqual(mockUser);
      });

      await act(async () => {
        await result.current.signOut();
      });

      // Local state should be cleared
      expect(result.current.user).toBe(null);
      expect(result.current.session).toBe(null);
    });
  });

  describe('Error handling', () => {
    it('handles getSession error gracefully', async () => {
      // Mock getSession to return an error response instead of rejecting
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: { message: 'Network error' },
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Should handle the error gracefully and set loading to false
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      }, { timeout: 3000 });

      // User and session should be null on error
      expect(result.current.user).toBe(null);
      expect(result.current.session).toBe(null);
    });

    it('handles auth state change subscription error', () => {
      // Mock onAuthStateChange to return a valid subscription object
      // The hook doesn't actually handle subscription errors, so we test the happy path
      mockSupabase.auth.onAuthStateChange.mockReturnValue({
        data: {
          subscription: {
            unsubscribe: vi.fn(),
          },
        },
      });

      // Should not throw when rendering
      expect(() => {
        renderHook(() => useAuth(), { wrapper });
      }).not.toThrow();
    });
  });
}); 