import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useUpdateBooking } from '../useBookings';
import { supabase } from '../../integrations/supabase/client';

// Mock Supabase
vi.mock('../../integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
    functions: {
      invoke: vi.fn(),
    },
  },
}));

// Mock toast
vi.mock('../../hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

const mockSupabase = vi.mocked(supabase);

describe('useUpdateBooking', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  it('should update booking and booking slots correctly', async () => {
    // Mock the booking lookup
    const mockBooking = { booking_code: 'test-booking-code' };
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockBooking, error: null }),
        }),
      }),
    } as any);

    // Mock the booking update
    mockSupabase.from.mockReturnValueOnce({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ error: null }),
      }),
    } as any);

    // Mock the booking slots update
    mockSupabase.from.mockReturnValueOnce({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ error: null }),
      }),
    } as any);

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-booking-id',
      updates: {
        participant_name: 'John Doe',
        horse_name: 'Thunder',
        payer_name: 'John Doe',
        payer_email: '<EMAIL>',
        event_dressage_test_id: 'test-dressage-id',
      },
    };

    await result.current.mutateAsync(updateData);

    // Verify booking lookup was called
    expect(mockSupabase.from).toHaveBeenCalledWith('bookings');
    
    // Verify booking update was called with correct fields
    expect(mockSupabase.from).toHaveBeenCalledWith('bookings');
    
    // Verify booking slots update was called with correct fields
    expect(mockSupabase.from).toHaveBeenCalledWith('booking_slots');
  });

  it('should handle booking not found error', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: null, error: null }),
        }),
      }),
    } as any);

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'non-existent-id',
      updates: {
        participant_name: 'John Doe',
        horse_name: 'Thunder',
      },
    };

    await expect(result.current.mutateAsync(updateData)).rejects.toThrow(
      'Booking not found or missing booking_code'
    );
  });

  it('should handle booking lookup error', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ 
            data: null, 
            error: { message: 'Database error' } 
          }),
        }),
      }),
    } as any);

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        participant_name: 'John Doe',
      },
    };

    await expect(result.current.mutateAsync(updateData)).rejects.toThrow(
      'Failed to get booking: Database error'
    );
  });

  it('should handle booking update error', async () => {
    // Mock successful booking lookup
    const mockBooking = { booking_code: 'test-booking-code' };
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockBooking, error: null }),
        }),
      }),
    } as any);

    // Mock booking update error
    mockSupabase.from.mockReturnValueOnce({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ 
          error: { message: 'Update failed' } 
        }),
      }),
    } as any);

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        payer_name: 'John Doe',
        payer_email: '<EMAIL>',
      },
    };

    await expect(result.current.mutateAsync(updateData)).rejects.toThrow(
      'Failed to update booking: Update failed'
    );
  });

  it('should handle booking slots update error', async () => {
    // Mock successful booking lookup
    const mockBooking = { booking_code: 'test-booking-code' };
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockBooking, error: null }),
        }),
      }),
    } as any);

    // Mock successful booking update
    mockSupabase.from.mockReturnValueOnce({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ error: null }),
      }),
    } as any);

    // Mock booking slots update error
    mockSupabase.from.mockReturnValueOnce({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ 
          error: { message: 'Slots update failed' } 
        }),
      }),
    } as any);

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        participant_name: 'John Doe',
        horse_name: 'Thunder',
      },
    };

    await expect(result.current.mutateAsync(updateData)).rejects.toThrow(
      'Failed to update booking slots: Slots update failed'
    );
  });

  it('should handle null dressage test ID', async () => {
    const mockBooking = { booking_code: 'test-booking-code' };
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockBooking, error: null }),
        }),
      }),
    } as any);

    mockSupabase.from.mockReturnValueOnce({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ error: null }),
      }),
    } as any);

    mockSupabase.from.mockReturnValueOnce({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ error: null }),
      }),
    } as any);

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        participant_name: 'John Doe',
        horse_name: 'Thunder',
        event_dressage_test_id: null,
      },
    };

    await result.current.mutateAsync(updateData);

    // Should complete without error
    expect(result.current.isSuccess).toBe(true);
  });

  it('should handle partial updates', async () => {
    const mockBooking = { booking_code: 'test-booking-code' };
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockBooking, error: null }),
        }),
      }),
    } as any);

    mockSupabase.from.mockReturnValueOnce({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ error: null }),
      }),
    } as any);

    mockSupabase.from.mockReturnValueOnce({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ error: null }),
      }),
    } as any);

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        participant_name: 'John Doe',
        // Only updating participant name, other fields undefined
      },
    };

    await result.current.mutateAsync(updateData);

    // Should complete without error
    expect(result.current.isSuccess).toBe(true);
  });
}); 