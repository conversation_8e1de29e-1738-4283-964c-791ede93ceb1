import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useAllUsersComprehensive, useDeleteUser, useUserByEmail, ComprehensiveUser } from './useAllUsers';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    rpc: vi.fn(),
  },
}));

const mockSupabase = vi.mocked(await import('@/integrations/supabase/client')).supabase;

// Test data
const mockUsers: ComprehensiveUser[] = [
  {
    auth_user_id: 'user1',
    auth_email: '<EMAIL>',
    full_name: 'Admin User',
    user_role: 'super_admin',
    role_display_name: 'Super Admin',
    account_status: 'active',
    profile_status: 'complete',
    role_status: 'active',
    auth_created_at: '2024-01-01T00:00:00Z',
    last_sign_in_at: '2024-01-15T00:00:00Z',
    phone: '************',
  },
  {
    auth_user_id: 'user2',
    auth_email: '<EMAIL>',
    full_name: 'Organizer User',
    user_role: 'organizer',
    role_display_name: 'Organizer',
    account_status: 'active',
    profile_status: 'complete',
    role_status: 'active',
    auth_created_at: '2024-01-02T00:00:00Z',
    last_sign_in_at: '2024-01-14T00:00:00Z',
    phone: null,
  },
];

const mockUser: ComprehensiveUser = {
  auth_user_id: 'user3',
  auth_email: '<EMAIL>',
  full_name: 'Rider User',
  user_role: 'rider',
  role_display_name: 'Rider',
  account_status: 'active',
  profile_status: 'complete',
  role_status: 'active',
  auth_created_at: '2024-01-03T00:00:00Z',
  last_sign_in_at: '2024-01-13T00:00:00Z',
  phone: '************',
};

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useAllUsers', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('useAllUsersComprehensive', () => {
    it('should fetch all users successfully', async () => {
      mockSupabase.rpc.mockResolvedValueOnce({
        data: mockUsers,
        error: null,
      });

      const { result } = renderHook(() => useAllUsersComprehensive(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockUsers);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_all_users_with_roles');
    });

    it('should handle error when fetching users fails', async () => {
      const error = new Error('Database error');
      mockSupabase.rpc.mockResolvedValueOnce({
        data: null,
        error,
      });

      const { result } = renderHook(() => useAllUsersComprehensive(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual([]);
    });

    it('should return empty array when exception occurs', async () => {
      mockSupabase.rpc.mockRejectedValueOnce(new Error('Network error'));

      const { result } = renderHook(() => useAllUsersComprehensive(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual([]);
    });

    it('should have correct query configuration', () => {
      const { result } = renderHook(() => useAllUsersComprehensive(), {
        wrapper: createWrapper(),
      });

      expect(result.current.data).toBeUndefined();
      expect(result.current.isLoading).toBe(true);
    });
  });

  describe('useDeleteUser', () => {
    it('should delete user successfully', async () => {
      const deleteResponse = { success: true, message: 'User deleted' };
      mockSupabase.rpc.mockResolvedValueOnce({
        data: deleteResponse,
        error: null,
      });

      const { result } = renderHook(() => useDeleteUser(), {
        wrapper: createWrapper(),
      });

      const userId = 'user1';
      result.current.mutate(userId);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockSupabase.rpc).toHaveBeenCalledWith('delete_user_safe', {
        user_id_param: userId,
      });
      expect(result.current.data).toEqual(deleteResponse);
    });

    it('should handle delete function error', async () => {
      const error = new Error('Delete function error');
      mockSupabase.rpc.mockResolvedValueOnce({
        data: null,
        error,
      });

      const { result } = renderHook(() => useDeleteUser(), {
        wrapper: createWrapper(),
      });

      result.current.mutate('user1');

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBe(error);
    });

    it('should handle delete function returning failure', async () => {
      const deleteResponse = {
        success: false,
        error: 'User not found',
        sqlstate: '23505',
        sqlerrm: 'User does not exist',
      };
      mockSupabase.rpc.mockResolvedValueOnce({
        data: deleteResponse,
        error: null,
      });

      const { result } = renderHook(() => useDeleteUser(), {
        wrapper: createWrapper(),
      });

      result.current.mutate('user1');

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error.message).toBe('User not found');
    });

    it('should handle delete function returning failure with no error message', async () => {
      const deleteResponse = {
        success: false,
        error: null,
        sqlstate: '23505',
        sqlerrm: 'User does not exist',
      };
      mockSupabase.rpc.mockResolvedValueOnce({
        data: deleteResponse,
        error: null,
      });

      const { result } = renderHook(() => useDeleteUser(), {
        wrapper: createWrapper(),
      });

      result.current.mutate('user1');

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error.message).toBe('Delete failed');
    });
  });

  describe('useUserByEmail', () => {
    it('should fetch user by email successfully', async () => {
      mockSupabase.rpc.mockResolvedValueOnce({
        data: [mockUser],
        error: null,
      });

      const { result } = renderHook(() => useUserByEmail('<EMAIL>'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockUser);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_user_by_email', {
        user_email: '<EMAIL>',
      });
    });

    it('should return null when no user found', async () => {
      mockSupabase.rpc.mockResolvedValueOnce({
        data: [],
        error: null,
      });

      const { result } = renderHook(() => useUserByEmail('<EMAIL>'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toBeNull();
    });

    it('should handle error when fetching user by email fails', async () => {
      const error = new Error('Database error');
      mockSupabase.rpc.mockResolvedValueOnce({
        data: null,
        error,
      });

      const { result } = renderHook(() => useUserByEmail('<EMAIL>'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toBeNull();
    });

    it('should return null when exception occurs', async () => {
      mockSupabase.rpc.mockRejectedValueOnce(new Error('Network error'));

      const { result } = renderHook(() => useUserByEmail('<EMAIL>'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toBeNull();
    });

    it('should not query when email is empty', () => {
      const { result } = renderHook(() => useUserByEmail(''), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.data).toBeUndefined();
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
    });

    it('should not query when email is null', () => {
      const { result } = renderHook(() => useUserByEmail(null as any), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.data).toBeUndefined();
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
    });

    it('should have correct query configuration', () => {
      const { result } = renderHook(() => useUserByEmail('<EMAIL>'), {
        wrapper: createWrapper(),
      });

      expect(result.current.data).toBeUndefined();
      expect(result.current.isLoading).toBe(true);
    });
  });
}); 