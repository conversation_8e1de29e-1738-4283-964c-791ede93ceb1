import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { 
  useTimeSlots, 
  useGenerateTimeSlots, 
  useRegenerateLocationTimeSlots, 
  useRegenerateActivitySlots, 
  useDeleteTimeSlotsForDate 
} from './useTimeSlots';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(),
          single: vi.fn(),
          gte: vi.fn(() => ({
            lte: vi.fn(() => ({
              order: vi.fn(),
            })),
          })),
        })),
        order: vi.fn(),
        gte: vi.fn(() => ({
          lte: vi.fn(() => ({
            order: vi.fn(),
          })),
        })),
        in: vi.fn(() => ({
          eq: vi.fn(),
        })),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(() => ({
          gte: vi.fn(() => ({
            lt: vi.fn(),
          })),
        })),
      })),
    })),
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);

describe('useTimeSlots', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockTimeSlots = [
    {
      id: 'slot-1',
      location_id: 'location-1',
      start_time: '2024-01-15T09:00:00',
      end_time: '2024-01-15T10:00:00',
      level: 'Intro',
      is_booked: false,
      created_at: '2024-01-01T00:00:00Z',
      activity_id: 'activity-1',
    },
    {
      id: 'slot-2',
      location_id: 'location-1',
      start_time: '2024-01-15T10:00:00',
      end_time: '2024-01-15T11:00:00',
      level: 'Intro',
      is_booked: true,
      created_at: '2024-01-01T00:00:00Z',
      activity_id: 'activity-1',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
  });

  describe('useTimeSlots', () => {
    it('fetches time slots for a specific location', async () => {
      mockUseQuery.mockReturnValue({
        data: mockTimeSlots,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useTimeSlots('location-1'));

      expect(result.current.data).toEqual(mockTimeSlots);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('returns empty array when no locationId provided', () => {
      mockUseQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useTimeSlots());

      expect(result.current.data).toEqual([]);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useTimeSlots('location-1'));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch time slots');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useTimeSlots('location-1'));

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('is disabled when no locationId provided', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useTimeSlots());

      expect(mockUseQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false,
        })
      );
    });
  });

  describe('useGenerateTimeSlots', () => {
    it('generates time slots successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        newSlots: [
          {
            id: 'new-slot-1',
            location_id: 'location-1',
            start_time: '2024-01-15T09:00:00',
            end_time: '2024-01-15T10:00:00',
            level: 'Intro',
            is_booked: false,
            activity_id: 'activity-1',
          },
        ],
        existingSlots: [],
        totalSlots: 1,
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useGenerateTimeSlots());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during generation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useGenerateTimeSlots());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during generation', () => {
      const mockError = new Error('Failed to generate time slots');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useGenerateTimeSlots());

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useRegenerateLocationTimeSlots', () => {
    it('regenerates location time slots successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue([
        {
          id: 'new-slot-1',
          location_id: 'location-1',
          start_time: '2024-01-15T09:00:00',
          end_time: '2024-01-15T10:00:00',
          level: 'Intro',
          is_booked: false,
          activity_id: 'activity-1',
        },
      ]);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useRegenerateLocationTimeSlots());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during regeneration', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useRegenerateLocationTimeSlots());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during regeneration', () => {
      const mockError = new Error('Failed to regenerate time slots');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useRegenerateLocationTimeSlots());

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useRegenerateActivitySlots', () => {
    it('regenerates activity slots successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        newSlots: [
          {
            id: 'new-slot-1',
            location_id: 'location-1',
            start_time: '2024-01-15T09:00:00',
            end_time: '2024-01-15T10:00:00',
            level: 'Intro',
            is_booked: false,
            activity_id: 'activity-1',
          },
        ],
        totalSlots: 1,
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useRegenerateActivitySlots());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during regeneration', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useRegenerateActivitySlots());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during regeneration', () => {
      const mockError = new Error('Failed to regenerate activity slots');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useRegenerateActivitySlots());

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useDeleteTimeSlotsForDate', () => {
    it('deletes time slots for a specific date successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue([]);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteTimeSlotsForDate());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during deletion', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteTimeSlotsForDate());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during deletion', () => {
      const mockError = new Error('Failed to delete time slots');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useDeleteTimeSlotsForDate());

      expect(result.current.error).toBe(mockError);
    });
  });
}); 