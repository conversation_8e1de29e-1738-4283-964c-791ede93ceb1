import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase, supabaseAdmin } from '@/integrations/supabase/client';

export interface ComprehensiveUser {
  auth_user_id: string;
  auth_email: string;
  full_name: string;
  user_role: string;
  role_display_name: string;
  account_status: string;
  profile_status: string;
  role_status: string;
  auth_created_at: string;
  last_sign_in_at: string;
  phone?: string | null;
}

// Hook to get all users using the comprehensive view
export const useAllUsersComprehensive = () => {
  return useQuery({
    queryKey: ['all-users-comprehensive'],
    queryFn: async () => {
      try {
        // Use the function we created to get all users with roles
        const { data, error } = await supabase
          .rpc('get_all_users_with_roles');

        if (error) {
          console.error('Error fetching users:', error);
          throw error;
        }

        console.log('✅ Fetched users from comprehensive view:', data?.length);
        
        // Debug: Log the first user's structure to see what fields are available
        if (data && data.length > 0) {
          console.log('🔍 First user data structure:', data[0]);
          console.log('🔍 Available fields:', Object.keys(data[0]));
        }
        
        return data as ComprehensiveUser[];
      } catch (error) {
        console.error('Error in useAllUsersComprehensive:', error);
        return [];
      }
    },
    // Add caching configuration to prevent unnecessary refetches
    staleTime: 5 * 60 * 1000, // 5 minutes - data is fresh for 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes - keep in cache for 10 minutes
    refetchOnWindowFocus: false, // Don't refetch when window gains focus
    refetchOnMount: false, // Don't refetch on mount if data exists
    refetchOnReconnect: false, // Don't refetch on reconnect
  });
};

// Hook to delete a user completely
export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userId: string) => {
      console.log('🔍 Starting user deletion for ID:', userId);
      
      const { data, error } = await supabase
        .rpc('delete_user_safe', { user_id_param: userId });

      console.log('🔍 Delete function response:', { data, error });

      if (error) {
        console.error('❌ Error calling delete function:', error);
        throw error;
      }

      console.log('✅ Delete function returned:', data);
      
      if (!data.success) {
        console.error('❌ Delete failed:', data.error);
        console.error('❌ SQL State:', data.sqlstate);
        console.error('❌ SQL Error:', data.sqlerrm);
        throw new Error(data.error || 'Delete failed');
      }

      return data;
    },
    onSuccess: (data) => {
      console.log('✅ User deletion successful, invalidating queries');
      // Invalidate and refetch the users list
      queryClient.invalidateQueries({ queryKey: ['all-users-comprehensive'] });
      console.log('✅ User deleted successfully');
    },
    onError: (error) => {
      console.error('❌ Failed to delete user:', error);
    },
  });
};

// Hook to get a specific user by email
export const useUserByEmail = (email: string) => {
  return useQuery({
    queryKey: ['user-by-email', email],
    queryFn: async () => {
      if (!email) return null;
      
      try {
        const { data, error } = await supabase
          .rpc('get_user_by_email', { user_email: email });

        if (error) {
          console.error('Error fetching user by email:', error);
          throw error;
        }

        // Ensure we always return a defined value
        return data?.[0] as ComprehensiveUser | null || null;
      } catch (error) {
        console.error('Error in useUserByEmail:', error);
        return null;
      }
    },
    enabled: !!email,
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
  });
};

// Hook to update user profile information
export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, profileData }: {
      userId: string;
      profileData: {
        full_name?: string;
        email?: string;
        phone?: string; // Use 'phone' to match database schema
      };
    }) => {
      console.log('🔍 Starting user profile update for ID:', userId);
      console.log('🔍 Profile data to update:', profileData);
      
      // Check if supabaseAdmin is available
      if (!supabaseAdmin) {
        console.error('❌ supabaseAdmin is null - check VITE_SUPABASE_SERVICE_ROLE_KEY environment variable');
        throw new Error('Admin client not available. Please check your environment configuration.');
      }
      
      const { data, error } = await supabaseAdmin
        .from('profiles')
        .update(profileData)
        .eq('id', userId)
        .select()
        .single();

      console.log('🔍 Profile update response:', { data, error });

      if (error) {
        console.error('❌ Error updating user profile:', error);
        throw error;
      }

      console.log('✅ Profile updated successfully:', data);
      return data;
    },
    onSuccess: (data) => {
      console.log('✅ User profile update successful, invalidating queries');
      // Invalidate and refetch the users list
      queryClient.invalidateQueries({ queryKey: ['all-users-comprehensive'] });
      console.log('✅ User profile updated successfully');
    },
    onError: (error) => {
      console.error('❌ Failed to update user profile:', error);
    },
  });
}; 