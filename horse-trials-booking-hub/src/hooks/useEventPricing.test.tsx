import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { 
  useEventPricing, 
  useEventActivityPricing, 
  useCreateEventPricing, 
  useUpdateEventPricing, 
  useDeleteEventPricing 
} from './useEventPricing';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { usePricingSettings, getActivityPrice } from '@/hooks/usePricingSettings';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            is: vi.fn(() => ({
              single: vi.fn(),
            })),
          })),
          is: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            is: vi.fn(() => ({
              single: vi.fn(),
            })),
          })),
          is: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
        is: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
    auth: {
      getUser: vi.fn(),
    },
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

// Mock usePricingSettings
vi.mock('@/hooks/usePricingSettings', () => ({
  usePricingSettings: vi.fn(),
  getActivityPrice: vi.fn(),
}));

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);
const mockUsePricingSettings = vi.mocked(usePricingSettings);
const mockGetActivityPrice = vi.mocked(getActivityPrice);

describe('useEventPricing', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
    setQueryData: vi.fn(),
    getQueryCache: vi.fn(() => ({
      getAll: vi.fn(() => []),
    })),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockGlobalPricing = {
    currency_symbol: '$',
    dressage_price: 50,
    show_jumping_price: 75,
    cross_country_price: 100,
  };

  const mockEventPricing = [
    {
      id: 'pricing-1',
      event_id: 'event-1',
      activity_type: 'dressage',
      level: 'Intro',
      price: 45,
      currency_symbol: '$',
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'pricing-2',
      event_id: 'event-1',
      activity_type: 'show_jumping',
      level: null,
      price: 70,
      currency_symbol: '$',
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'pricing-3',
      event_id: 'event-1',
      activity_type: 'cross_country',
      level: 'Prelim',
      price: 95,
      currency_symbol: '$',
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
    mockUsePricingSettings.mockReturnValue({
      data: mockGlobalPricing,
      isLoading: false,
      error: null,
    } as any);
    mockGetActivityPrice.mockReturnValue(50);
  });

  describe('useEventPricing', () => {
    it('fetches event pricing for a specific event', async () => {
      mockUseQuery.mockReturnValue({
        data: mockEventPricing,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventPricing('event-1'));

      expect(result.current.data).toEqual(mockEventPricing);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('returns empty array when no eventId provided', () => {
      mockUseQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventPricing(''));

      expect(result.current.data).toEqual([]);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventPricing('event-1'));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch event pricing');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useEventPricing('event-1'));

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('is disabled when no eventId provided', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useEventPricing(''));

      expect(mockUseQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false,
        })
      );
    });
  });

  describe('useEventActivityPricing', () => {
    it('returns event-specific level pricing when available', () => {
      const mockPricingResult = {
        price: 45,
        currency_symbol: '$',
        source: 'event_level' as const,
      };

      mockUseQuery.mockReturnValue({
        data: mockPricingResult,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventActivityPricing('event-1', 'dressage', 'Intro'));

      expect(result.current.data).toEqual(mockPricingResult);
      expect(result.current.isLoading).toBe(false);
    });

    it('returns event-specific activity pricing when level not found', () => {
      const mockPricingResult = {
        price: 70,
        currency_symbol: '$',
        source: 'event_activity' as const,
      };

      mockUseQuery.mockReturnValue({
        data: mockPricingResult,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventActivityPricing('event-1', 'show_jumping'));

      expect(result.current.data).toEqual(mockPricingResult);
    });

    it('falls back to global pricing when no event pricing found', () => {
      const mockPricingResult = {
        price: 50,
        currency_symbol: '$',
        source: 'global' as const,
      };

      mockUseQuery.mockReturnValue({
        data: mockPricingResult,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventActivityPricing('event-1', 'dressage'));

      expect(result.current.data).toEqual(mockPricingResult);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventActivityPricing('event-1', 'dressage'));

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch activity pricing');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useEventActivityPricing('event-1', 'dressage'));

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useCreateEventPricing', () => {
    it('creates event pricing successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEventPricing());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during creation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEventPricing());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during creation', () => {
      const mockError = new Error('Failed to create event pricing');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useCreateEventPricing());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useCreateEventPricing());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });
  });

  describe('useUpdateEventPricing', () => {
    it('updates event pricing successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue([{
        id: 'pricing-1',
        event_id: 'event-1',
        activity_type: 'dressage',
        level: 'Intro',
        price: 50,
        currency_symbol: '$',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
      }]);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateEventPricing());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during update', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateEventPricing());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during update', () => {
      const mockError = new Error('Failed to update event pricing');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useUpdateEventPricing());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries and updates cache on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue([{
        id: 'pricing-1',
        event_id: 'event-1',
        activity_type: 'dressage',
        level: 'Intro',
        price: 50,
        currency_symbol: '$',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
      }]);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useUpdateEventPricing());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });
  });

  describe('useDeleteEventPricing', () => {
    it('deletes event pricing successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEventPricing());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during deletion', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEventPricing());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during deletion', () => {
      const mockError = new Error('Failed to delete event pricing');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useDeleteEventPricing());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useDeleteEventPricing());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });
  });
}); 