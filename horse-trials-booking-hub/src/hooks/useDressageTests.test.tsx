import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { 
  useDressageTests, 
  useEventDressageTests, 
  useCreateEventDressageTest, 
  useDeleteEventDressageTest 
} from './useDressageTests';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(),
          })),
          order: vi.fn(),
        })),
        order: vi.fn(),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);

describe('useDressageTests', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockDressageTests = [
    {
      id: 'test-1',
      label: 'Intro Test A',
      description: 'Introductory dressage test',
      level_id: 'level-1',
      organization: 'USDF',
      is_active: true,
      levels: {
        id: 'level-1',
        name: 'Intro',
        discipline: 'dressage',
      },
    },
    {
      id: 'test-2',
      label: 'Prelim Test 1',
      description: 'Preliminary dressage test',
      level_id: 'level-2',
      organization: 'USDF',
      is_active: true,
      levels: {
        id: 'level-2',
        name: 'Prelim',
        discipline: 'dressage',
      },
    },
  ];

  const mockEventDressageTests = [
    {
      id: 'event-test-1',
      event_id: 'event-1',
      test_id: 'test-1',
      dressage_test_library_id: 'test-1',
      dressage_test_library: {
        id: 'test-1',
        label: 'Intro Test A',
        description: 'Introductory dressage test',
        level_id: 'level-1',
        levels: {
          id: 'level-1',
          name: 'Intro',
          discipline: 'dressage',
        },
      },
    },
    {
      id: 'event-test-2',
      event_id: 'event-1',
      test_id: 'test-2',
      dressage_test_library_id: 'test-2',
      dressage_test_library: {
        id: 'test-2',
        label: 'Prelim Test 1',
        description: 'Preliminary dressage test',
        level_id: 'level-2',
        levels: {
          id: 'level-2',
          name: 'Prelim',
          discipline: 'dressage',
        },
      },
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
  });

  describe('useDressageTests', () => {
    it('fetches all dressage tests when no levelId specified', async () => {
      mockUseQuery.mockReturnValue({
        data: mockDressageTests,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDressageTests());

      expect(result.current.data).toEqual(mockDressageTests);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('fetches dressage tests for a specific level', async () => {
      const introTests = mockDressageTests.filter(test => test.level_id === 'level-1');
      mockUseQuery.mockReturnValue({
        data: introTests,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDressageTests('level-1'));

      expect(result.current.data).toEqual(introTests);
      expect(result.current.data?.every(test => test.level_id === 'level-1')).toBe(true);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useDressageTests());

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch dressage tests');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useDressageTests());

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('filters out inactive tests', () => {
      const activeTests = mockDressageTests.filter(test => test.is_active);
      mockUseQuery.mockReturnValue({
        data: activeTests,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDressageTests());

      expect(result.current.data).toEqual(activeTests);
      expect(result.current.data?.every(test => test.is_active)).toBe(true);
    });

    it('transforms data to include levels information', () => {
      mockUseQuery.mockReturnValue({
        data: mockDressageTests,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDressageTests());

      expect(result.current.data?.[0].levels).toBeDefined();
      expect(result.current.data?.[0].levels?.name).toBe('Intro');
      expect(result.current.data?.[0].levels?.discipline).toBe('dressage');
    });
  });

  describe('useEventDressageTests', () => {
    it('fetches event dressage tests for a specific event', async () => {
      mockUseQuery.mockReturnValue({
        data: mockEventDressageTests,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventDressageTests('event-1'));

      expect(result.current.data).toEqual(mockEventDressageTests);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('returns empty array when no eventId provided', () => {
      mockUseQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventDressageTests(undefined));

      expect(result.current.data).toEqual([]);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventDressageTests('event-1'));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch event dressage tests');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useEventDressageTests('event-1'));

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('is disabled when no eventId provided', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useEventDressageTests(undefined));

      expect(mockUseQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false,
        })
      );
    });

    it('includes dressage test library details in the response', () => {
      mockUseQuery.mockReturnValue({
        data: mockEventDressageTests,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventDressageTests('event-1'));

      expect(result.current.data?.[0].dressage_test_library).toBeDefined();
      expect(result.current.data?.[0].dressage_test_library?.label).toBe('Intro Test A');
      expect(result.current.data?.[0].dressage_test_library?.levels?.name).toBe('Intro');
    });

    it('handles missing dressage test library gracefully', () => {
      const testsWithMissingLibrary = [
        {
          id: 'event-test-1',
          event_id: 'event-1',
          test_id: 'test-1',
          dressage_test_library_id: 'test-1',
          dressage_test_library: null,
        },
      ];

      mockUseQuery.mockReturnValue({
        data: testsWithMissingLibrary,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventDressageTests('event-1'));

      expect(result.current.data?.[0].dressage_test_library).toBeNull();
    });
  });

  describe('useCreateEventDressageTest', () => {
    it('creates event dressage test successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'event-test-3',
        event_id: 'event-1',
        test_id: 'test-3',
        dressage_test_library_id: 'test-3',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEventDressageTest());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during creation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEventDressageTest());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during creation', () => {
      const mockError = new Error('Failed to create event dressage test');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useCreateEventDressageTest());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'event-test-3',
        event_id: 'event-1',
        test_id: 'test-3',
        dressage_test_library_id: 'test-3',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useCreateEventDressageTest());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });

    it('sets dressage_test_library_id to test_id value', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'event-test-3',
        event_id: 'event-1',
        test_id: 'test-3',
        dressage_test_library_id: 'test-3',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useCreateEventDressageTest());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          mutationFn: expect.any(Function),
        })
      );
    });
  });

  describe('useDeleteEventDressageTest', () => {
    it('deletes event dressage test successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEventDressageTest());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during deletion', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEventDressageTest());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during deletion', () => {
      const mockError = new Error('Failed to delete event dressage test');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useDeleteEventDressageTest());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useDeleteEventDressageTest());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });
  });
}); 