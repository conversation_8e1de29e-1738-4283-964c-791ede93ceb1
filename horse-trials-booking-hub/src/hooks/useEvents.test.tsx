import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useEvents, useEvent, useCreateEvent, useUpdateEvent, useDeleteEvent } from './useEvents';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Chainable supabase mock for all query/mutation chains
const chainableMock = () => {
  const fn = vi.fn().mockReturnThis();
  return {
    select: fn,
    insert: fn,
    update: fn,
    delete: fn,
    eq: fn,
    in: fn,
    order: fn,
    single: fn,
    from: vi.fn().mockReturnThis(),
  };
};

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: () => {
      const fn = vi.fn().mockReturnThis();
      return {
        select: fn,
        insert: fn,
        update: fn,
        delete: fn,
        eq: fn,
        in: fn,
        order: fn,
        single: fn,
        from: vi.fn().mockReturnThis(),
      };
    },
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);

describe('useEvents', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockEvents = [
    {
      id: 'event-1',
      name: 'Spring Dressage Show',
      start_date: '2024-03-15',
      end_date: '2024-03-17',
      event_type: 'dressage',
      created_by: 'user-1',
      created_at: '2024-01-01T00:00:00Z',
      is_active: true,
      organizer_id: 'org-1',
      organizer: {
        id: 'org-1',
        full_name: 'John Doe',
        email: '<EMAIL>',
        company_name: 'Doe Stables',
        business_name: null,
      },
    },
    {
      id: 'event-2',
      name: 'Summer Horse Trials',
      start_date: '2024-06-20',
      end_date: '2024-06-22',
      event_type: 'horse_trials',
      created_by: 'user-2',
      created_at: '2024-01-15T00:00:00Z',
      is_active: true,
      organizer_id: 'org-2',
      organizer: {
        id: 'org-2',
        full_name: 'Jane Smith',
        email: '<EMAIL>',
        company_name: null,
        business_name: 'Smith Equestrian',
      },
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
  });

  describe('useEvents', () => {
    it('fetches all events successfully', async () => {
      mockUseQuery.mockReturnValue({
        data: mockEvents,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEvents());

      expect(result.current.data).toEqual(mockEvents);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('fetches only active events when onlyActive is true', () => {
      const activeEvents = mockEvents.filter(event => event.is_active);
      mockUseQuery.mockReturnValue({
        data: activeEvents,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEvents(true));

      expect(result.current.data).toEqual(activeEvents);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useEvents());

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch events');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useEvents());

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('useEvent', () => {
    it('fetches a single event successfully', async () => {
      const singleEvent = mockEvents[0];
      mockUseQuery.mockReturnValue({
        data: singleEvent,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEvent('event-1'));

      expect(result.current.data).toEqual(singleEvent);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles missing event ID', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error('Event ID is required'),
      } as any);

      const { result } = renderHook(() => useEvent());

      expect(result.current.error).toBeInstanceOf(Error);
      expect(result.current.error?.message).toBe('Event ID is required');
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useEvent('event-1'));

      expect(result.current.isLoading).toBe(true);
    });
  });

  describe('useCreateEvent', () => {
    it('creates event successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'event-3',
        name: 'New Event',
        start_date: '2024-04-01',
        end_date: '2024-04-03',
        event_type: 'dressage',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEvent());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during creation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEvent());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during creation', () => {
      const mockError = new Error('Failed to create event');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useCreateEvent());

      expect(result.current.error).toBe(mockError);
    });

    it('tests the actual mutation function for event creation', async () => {
      const mockEventData = {
        event: {
          name: 'Test Event',
          start_date: '2024-04-01',
          end_date: '2024-04-03',
          event_type: 'dressage' as const,
          is_active: true,
          organizer_id: 'org-1',
        },
        levelIds: ['level-1', 'level-2'],
      };

      const mockCreatedEvent = {
        id: 'event-3',
        ...mockEventData.event,
      };

      // Mock the mutation function
      const mockMutationFn = vi.fn().mockImplementation(async (eventData) => {
        // Simulate the actual mutation logic
        const { data: event, error: eventError } = await mockSupabase.from().insert().select().single();
        
        if (eventError) throw eventError;
        
        // Simulate creating event levels
        if (eventData.levelIds && eventData.levelIds.length > 0) {
          const { error: levelsError } = await mockSupabase.from().insert();
          if (levelsError) throw levelsError;
        }
        
        // Simulate creating default pricing
        const { error: pricingError } = await mockSupabase.from().insert();
        if (pricingError) {
          console.error('Error creating default pricing:', pricingError);
        }
        
        return mockCreatedEvent;
      });

      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEvent());

      // Test the mutation function
      const mutationResult = await result.current.mutateAsync(mockEventData);
      
      expect(mutationResult).toEqual(mockCreatedEvent);
      expect(mockMutationFn).toHaveBeenCalledWith(mockEventData);
    });

    it('handles event creation error and rolls back', async () => {
      const mockEventData = {
        event: {
          name: 'Test Event',
          start_date: '2024-04-01',
          end_date: '2024-04-03',
          event_type: 'dressage' as const,
        },
        levelIds: ['level-1'],
      };

      const mockMutationFn = vi.fn().mockImplementation(async (eventData) => {
        // Simulate event creation success
        const mockEvent = { id: 'event-3', ...eventData.event };
        
        // Simulate levels creation failure
        const levelsError = new Error('Failed to create levels');
        throw levelsError;
      });

      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEvent());

      await expect(result.current.mutateAsync(mockEventData)).rejects.toThrow('Failed to create levels');
    });
  });

  describe('useUpdateEvent', () => {
    it('updates event successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'event-1',
        name: 'Updated Event Name',
        start_date: '2024-03-15',
        end_date: '2024-03-17',
        event_type: 'dressage',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateEvent());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during update', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateEvent());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during update', () => {
      const mockError = new Error('Failed to update event');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useUpdateEvent());

      expect(result.current.error).toBe(mockError);
    });

    it('tests the actual mutation function for event update', async () => {
      const mockUpdateData = {
        id: 'event-1',
        name: 'Updated Event Name',
        start_date: '2024-03-15',
        end_date: '2024-03-17',
        event_type: 'dressage' as const,
      };

      const mockUpdatedEvent = {
        id: 'event-1',
        name: 'Updated Event Name',
        start_date: '2024-03-15',
        end_date: '2024-03-17',
        event_type: 'dressage',
        is_active: true,
        organizer_id: 'org-1',
      };

      const mockMutationFn = vi.fn().mockImplementation(async (updateData) => {
        // Simulate the actual update logic
        const { data, error } = await mockSupabase.from().update().eq().select().single();
        
        if (error) throw error;
        
        return mockUpdatedEvent;
      });

      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateEvent());

      const mutationResult = await result.current.mutateAsync(mockUpdateData);
      
      expect(mutationResult).toEqual(mockUpdatedEvent);
      expect(mockMutationFn).toHaveBeenCalledWith(mockUpdateData);
    });

    it('handles update error', async () => {
      const mockUpdateData = {
        id: 'event-1',
        name: 'Updated Event Name',
      };

      const mockMutationFn = vi.fn().mockImplementation(async (updateData) => {
        const error = new Error('Failed to update event');
        throw error;
      });

      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateEvent());

      await expect(result.current.mutateAsync(mockUpdateData)).rejects.toThrow('Failed to update event');
    });
  });

  describe('useDeleteEvent', () => {
    it('deletes event successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ success: true });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEvent());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during deletion', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEvent());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during deletion', () => {
      const mockError = new Error('Failed to delete event');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useDeleteEvent());

      expect(result.current.error).toBe(mockError);
    });

    it('tests the actual mutation function for event deletion with cascade', async () => {
      const eventId = 'event-1';
      const locationIds = ['loc-1', 'loc-2'];
      const timeSlotIds = ['ts-1', 'ts-2', 'ts-3'];
      const bookingCodes = ['booking-1', 'booking-2'];

      const mockMutationFn = vi.fn().mockImplementation(async (eventId) => {
        // Simulate the complex cascade deletion logic
        
        // 1. Query locations
        const { data: locations, error: locationsQueryError } = await mockSupabase.from().select().eq();
        if (locationsQueryError) throw new Error(`Failed to query locations: ${locationsQueryError.message}`);
        
        // 2. Query time slots
        const { data: timeSlots, error: timeSlotsQueryError } = await mockSupabase.from().select().in();
        if (timeSlotsQueryError) throw new Error(`Failed to query time slots: ${timeSlotsQueryError.message}`);
        
        // 3. Query booking slots
        const { data: bookingSlots, error: bookingSlotsQueryError } = await mockSupabase.from().select().in();
        if (bookingSlotsQueryError) throw new Error(`Failed to query booking slots: ${bookingSlotsQueryError.message}`);
        
        // 4. Delete booking slots
        const { error: bookingSlotsDeleteError } = await mockSupabase.from().delete().in();
        if (bookingSlotsDeleteError) throw new Error(`Failed to delete booking slots: ${bookingSlotsDeleteError.message}`);
        
        // 5. Delete bookings
        const { error: bookingsError } = await mockSupabase.from().delete().in();
        if (bookingsError) throw new Error(`Failed to delete bookings: ${bookingsError.message}`);
        
        // 6. Delete time slots
        const { error: timeSlotsError } = await mockSupabase.from().delete().in();
        if (timeSlotsError) throw new Error(`Failed to delete time slots: ${timeSlotsError.message}`);
        
        // 7. Delete activities
        const { error: activitiesError } = await mockSupabase.from().delete().in();
        if (activitiesError) throw new Error(`Failed to delete activities: ${activitiesError.message}`);
        
        // 8. Delete event dressage tests
        const { error: dressageTestsError } = await mockSupabase.from().delete().eq();
        if (dressageTestsError) throw new Error(`Failed to delete event dressage tests: ${dressageTestsError.message}`);
        
        // 9. Delete event levels
        const { error: eventLevelsError } = await mockSupabase.from().delete().eq();
        if (eventLevelsError) throw new Error(`Failed to delete event levels: ${eventLevelsError.message}`);
        
        // 10. Delete locations
        const { error: locationsError } = await mockSupabase.from().delete().eq();
        if (locationsError) throw new Error(`Failed to delete locations: ${locationsError.message}`);
        
        // 11. Delete the event itself
        const { error: eventError } = await mockSupabase.from().delete().eq();
        if (eventError) throw new Error(`Failed to delete event: ${eventError.message}`);
        
        return { success: true };
      });

      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEvent());

      const mutationResult = await result.current.mutateAsync(eventId);
      
      expect(mutationResult).toEqual({ success: true });
      expect(mockMutationFn).toHaveBeenCalledWith(eventId);
    });

    it('handles deletion error at locations query step', async () => {
      const eventId = 'event-1';

      const mockMutationFn = vi.fn().mockImplementation(async (eventId) => {
        // Simulate error at locations query step
        const locationsQueryError = new Error('Database connection failed');
        throw new Error(`Failed to query locations: ${locationsQueryError.message}`);
      });

      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEvent());

      await expect(result.current.mutateAsync(eventId)).rejects.toThrow('Failed to query locations: Database connection failed');
    });

    it('handles deletion error at time slots deletion step', async () => {
      const eventId = 'event-1';

      const mockMutationFn = vi.fn().mockImplementation(async (eventId) => {
        // Simulate successful steps 1-5, then error at time slots deletion
        const timeSlotsError = new Error('Foreign key constraint violation');
        throw new Error(`Failed to delete time slots: ${timeSlotsError.message}`);
      });

      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEvent());

      await expect(result.current.mutateAsync(eventId)).rejects.toThrow('Failed to delete time slots: Foreign key constraint violation');
    });

    it('handles deletion error at final event deletion step', async () => {
      const eventId = 'event-1';

      const mockMutationFn = vi.fn().mockImplementation(async (eventId) => {
        // Simulate successful steps 1-10, then error at final event deletion
        const eventError = new Error('Event not found');
        throw new Error(`Failed to delete event: ${eventError.message}`);
      });

      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEvent());

      await expect(result.current.mutateAsync(eventId)).rejects.toThrow('Failed to delete event: Event not found');
    });

    it('handles deletion with no related data', async () => {
      const eventId = 'event-1';

      const mockMutationFn = vi.fn().mockImplementation(async (eventId) => {
        // Simulate event with no related data (empty arrays)
        const { data: locations, error: locationsQueryError } = await mockSupabase.from().select().eq();
        if (locationsQueryError) throw new Error(`Failed to query locations: ${locationsQueryError.message}`);
        
        // No locations found, skip steps 2-10
        
        // Delete the event directly
        const { error: eventError } = await mockSupabase.from().delete().eq();
        if (eventError) throw new Error(`Failed to delete event: ${eventError.message}`);
        
        return { success: true };
      });

      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEvent());

      const mutationResult = await result.current.mutateAsync(eventId);
      
      expect(mutationResult).toEqual({ success: true });
      expect(mockMutationFn).toHaveBeenCalledWith(eventId);
    });
  });
}); 