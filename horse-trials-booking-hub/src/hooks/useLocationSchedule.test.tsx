import React from 'react';
import { renderHook } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useLocationSchedule, type ScheduleItem } from './useLocationSchedule';
import { useActivities, type Activity } from './useActivities';

// Mock useActivities
vi.mock('./useActivities', () => ({
  useActivities: vi.fn(),
}));

const mockUseActivities = vi.mocked(useActivities);

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useLocationSchedule', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Schedule Generation', () => {
    it('should generate schedule with activities only', () => {
      const mockActivities: Activity[] = [
        {
          id: 'activity-1',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:00:00Z',
          end_time: '2024-01-01T09:45:00Z',
          activity_type: 'dressage',
          level: 'Training',
          description: 'Dressage session'
        },
        {
          id: 'activity-2',
          location_id: 'loc-1',
          start_time: '2024-01-01T10:00:00Z',
          end_time: '2024-01-01T10:30:00Z',
          activity_type: 'jumping',
          level: 'Preliminary',
          description: 'Jumping session'
        }
      ];

      mockUseActivities.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.schedule).toHaveLength(3); // 2 activities + 1 break
      expect(result.current.schedule[0]).toEqual({
        id: 'activity-1',
        type: 'activity',
        start_time: '2024-01-01T09:00:00Z',
        end_time: '2024-01-01T09:45:00Z',
        duration_minutes: 45,
        activity_type: 'dressage',
        level: 'Training',
        description: 'Dressage session'
      });
      expect(result.current.schedule[1]).toEqual({
        id: 'break-activity-1-activity-2',
        type: 'break',
        start_time: '2024-01-01T09:45:00Z',
        end_time: '2024-01-01T10:00:00Z',
        duration_minutes: 15
      });
      expect(result.current.schedule[2]).toEqual({
        id: 'activity-2',
        type: 'activity',
        start_time: '2024-01-01T10:00:00Z',
        end_time: '2024-01-01T10:30:00Z',
        duration_minutes: 30,
        activity_type: 'jumping',
        level: 'Preliminary',
        description: 'Jumping session'
      });
    });

    it('should filter activities by selected date', () => {
      const mockActivities: Activity[] = [
        {
          id: 'activity-1',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:00:00Z',
          end_time: '2024-01-01T09:45:00Z',
          activity_type: 'dressage',
        },
        {
          id: 'activity-2',
          location_id: 'loc-1',
          start_time: '2024-01-02T10:00:00Z',
          end_time: '2024-01-02T10:30:00Z',
          activity_type: 'jumping',
        }
      ];

      mockUseActivities.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1', '2024-01-01'), {
        wrapper: createWrapper(),
      });

      expect(result.current.schedule).toHaveLength(1);
      expect(result.current.schedule[0].id).toBe('activity-1');
    });

    it('should not add breaks shorter than 1 minute', () => {
      const mockActivities: Activity[] = [
        {
          id: 'activity-1',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:00:00Z',
          end_time: '2024-01-01T09:45:00Z',
          activity_type: 'dressage',
        },
        {
          id: 'activity-2',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:45:20Z', // 20 second gap
          end_time: '2024-01-01T10:30:00Z',
          activity_type: 'jumping',
        }
      ];

      mockUseActivities.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.schedule).toHaveLength(2); // Only activities, no break
      expect(result.current.schedule[0].type).toBe('activity');
      expect(result.current.schedule[1].type).toBe('activity');
    });

    it('should add breaks for gaps of 1 minute or more', () => {
      const mockActivities: Activity[] = [
        {
          id: 'activity-1',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:00:00Z',
          end_time: '2024-01-01T09:45:00Z',
          activity_type: 'dressage',
        },
        {
          id: 'activity-2',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:47:00Z', // 2 minutes gap
          end_time: '2024-01-01T10:30:00Z',
          activity_type: 'jumping',
        }
      ];

      mockUseActivities.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.schedule).toHaveLength(3); // 2 activities + 1 break
      expect(result.current.schedule[1].type).toBe('break');
      expect(result.current.schedule[1].duration_minutes).toBe(2);
    });
  });

  describe('Schedule Statistics', () => {
    it('should calculate correct statistics for mixed schedule', () => {
      const mockActivities: Activity[] = [
        {
          id: 'activity-1',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:00:00Z',
          end_time: '2024-01-01T09:45:00Z',
          activity_type: 'dressage',
        },
        {
          id: 'activity-2',
          location_id: 'loc-1',
          start_time: '2024-01-01T10:00:00Z',
          end_time: '2024-01-01T10:30:00Z',
          activity_type: 'jumping',
        }
      ];

      mockUseActivities.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.scheduleStats).toEqual({
        start_time: '2024-01-01T09:00:00Z',
        end_time: '2024-01-01T10:30:00Z',
        total_duration_minutes: 90, // 45 + 15 + 30
        activity_minutes: 75, // 45 + 30
        break_minutes: 15,
        activity_count: 2,
        break_count: 1
      });
    });

    it('should handle empty schedule', () => {
      mockUseActivities.mockReturnValue({
        data: [],
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.scheduleStats).toEqual({
        start_time: null,
        end_time: null,
        total_duration_minutes: 0,
        activity_minutes: 0,
        break_minutes: 0,
        activity_count: 0,
        break_count: 0
      });
    });

    it('should handle single activity', () => {
      const mockActivities: Activity[] = [
        {
          id: 'activity-1',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:00:00Z',
          end_time: '2024-01-01T09:45:00Z',
          activity_type: 'dressage',
        }
      ];

      mockUseActivities.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.scheduleStats).toEqual({
        start_time: '2024-01-01T09:00:00Z',
        end_time: '2024-01-01T09:45:00Z',
        total_duration_minutes: 45,
        activity_minutes: 45,
        break_minutes: 0,
        activity_count: 1,
        break_count: 0
      });
    });
  });

  describe('Time Formatting', () => {
    it('should format time correctly', () => {
      mockUseActivities.mockReturnValue({
        data: [],
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      // Test that formatTime returns a properly formatted time string
      // Note: The actual time will depend on the local timezone
      const formattedTime = result.current.formatTime('2024-01-01T09:00:00Z');
      expect(typeof formattedTime).toBe('string');
      expect(formattedTime).toMatch(/^\d{1,2}:\d{2}\s(AM|PM)$/);
      
      const formattedTime2 = result.current.formatTime('2024-01-01T14:30:00Z');
      expect(typeof formattedTime2).toBe('string');
      expect(formattedTime2).toMatch(/^\d{1,2}:\d{2}\s(AM|PM)$/);
      
      const formattedTime3 = result.current.formatTime('2024-01-01T00:00:00Z');
      expect(typeof formattedTime3).toBe('string');
      expect(formattedTime3).toMatch(/^\d{1,2}:\d{2}\s(AM|PM)$/);
    });
  });

  describe('Loading and Error States', () => {
    it('should handle loading state', () => {
      mockUseActivities.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(true);
      expect(result.current.schedule).toEqual([]);
      expect(result.current.scheduleStats).toEqual({
        start_time: null,
        end_time: null,
        total_duration_minutes: 0,
        activity_minutes: 0,
        break_minutes: 0,
        activity_count: 0,
        break_count: 0
      });
    });

    it('should handle error state', () => {
      const error = new Error('Failed to fetch activities');
      mockUseActivities.mockReturnValue({
        data: undefined,
        isLoading: false,
        error
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.error).toBe(error);
      expect(result.current.schedule).toEqual([]);
      expect(result.current.scheduleStats).toEqual({
        start_time: null,
        end_time: null,
        total_duration_minutes: 0,
        activity_minutes: 0,
        break_minutes: 0,
        activity_count: 0,
        break_count: 0
      });
    });

    it('should handle null activities data', () => {
      mockUseActivities.mockReturnValue({
        data: null,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.schedule).toEqual([]);
      expect(result.current.scheduleStats).toEqual({
        start_time: null,
        end_time: null,
        total_duration_minutes: 0,
        activity_minutes: 0,
        break_minutes: 0,
        activity_count: 0,
        break_count: 0
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle activities with exact time overlap', () => {
      const mockActivities: Activity[] = [
        {
          id: 'activity-1',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:00:00Z',
          end_time: '2024-01-01T09:45:00Z',
          activity_type: 'dressage',
        },
        {
          id: 'activity-2',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:45:00Z', // Exact overlap
          end_time: '2024-01-01T10:30:00Z',
          activity_type: 'jumping',
        }
      ];

      mockUseActivities.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.schedule).toHaveLength(2); // No break
      expect(result.current.schedule[0].type).toBe('activity');
      expect(result.current.schedule[1].type).toBe('activity');
    });

    it('should handle activities with time overlap (end time after next start)', () => {
      const mockActivities: Activity[] = [
        {
          id: 'activity-1',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:00:00Z',
          end_time: '2024-01-01T09:50:00Z',
          activity_type: 'dressage',
        },
        {
          id: 'activity-2',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:45:00Z', // Overlaps
          end_time: '2024-01-01T10:30:00Z',
          activity_type: 'jumping',
        }
      ];

      mockUseActivities.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.schedule).toHaveLength(2); // No break
      expect(result.current.schedule[0].type).toBe('activity');
      expect(result.current.schedule[1].type).toBe('activity');
    });

    it('should handle activities with very short durations', () => {
      const mockActivities: Activity[] = [
        {
          id: 'activity-1',
          location_id: 'loc-1',
          start_time: '2024-01-01T09:00:00Z',
          end_time: '2024-01-01T09:01:00Z', // 1 minute duration
          activity_type: 'dressage',
        }
      ];

      mockUseActivities.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null
      });

      const { result } = renderHook(() => useLocationSchedule('loc-1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.schedule[0].duration_minutes).toBe(1);
      expect(result.current.scheduleStats.activity_minutes).toBe(1);
    });
  });
}); 