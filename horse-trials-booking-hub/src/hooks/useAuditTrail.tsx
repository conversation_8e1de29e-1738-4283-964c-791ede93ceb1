import { useQuery, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';

export interface AuditRecord {
  id: string;
  table_name: string;
  record_id: string;
  action: 'INSERT' | 'UPDATE' | 'DELETE';
  old_values: any;
  new_values: any;
  changed_fields: string[];
  user_id: string;
  user_role: string;
  user_email: string;
  ip_address: string | null;
  user_agent: string | null;
  reason: string | null;
  created_at: string;
  session_id: string | null;
}

export interface AuditTrailParams {
  table_name: string;
  record_id: string;
  limit?: number;
}

export interface UserAuditTrailParams {
  user_id: string;
  limit?: number;
}

// Hook to get audit trail for a specific record
export const useAuditTrail = ({ table_name, record_id, limit = 100 }: AuditTrailParams) => {
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  const isSuperAdmin = userRole === 'super_admin';

  return useQuery({
    queryKey: ['audit-trail', table_name, record_id, limit],
    queryFn: async (): Promise<AuditRecord[]> => {
      if (!isSuperAdmin) {
        throw new Error('Only super admins can access audit trails');
      }

      const { data, error } = await supabase.rpc('get_audit_trail', {
        p_table_name: table_name,
        p_record_id: record_id,
        p_limit: limit
      });

      if (error) {
        throw error;
      }

      // Cast the action field to the correct type
      return (data || []).map(record => ({
        ...record,
        action: record.action as 'INSERT' | 'UPDATE' | 'DELETE'
      }));
    },
    enabled: !!user && isSuperAdmin && !!table_name && !!record_id,
  });
};

// Hook to get audit trail for a specific user
export const useUserAuditTrail = ({ user_id, limit = 100 }: UserAuditTrailParams) => {
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  const isSuperAdmin = userRole === 'super_admin';

  return useQuery({
    queryKey: ['user-audit-trail', user_id, limit],
    queryFn: async (): Promise<AuditRecord[]> => {
      if (!isSuperAdmin) {
        throw new Error('Only super admins can access audit trails');
      }

      const { data, error } = await supabase.rpc('get_user_audit_trail', {
        p_user_id: user_id,
        p_limit: limit
      });

      if (error) {
        throw error;
      }

      // Cast the action field to the correct type
      return (data || []).map(record => ({
        ...record,
        action: record.action as 'INSERT' | 'UPDATE' | 'DELETE'
      }));
    },
    enabled: !!user && isSuperAdmin && !!user_id,
  });
};

// Hook to get all audit records (for super admin dashboard)
export const useAllAuditTrail = (limit: number = 100) => {
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  const isSuperAdmin = userRole === 'super_admin';

  return useQuery({
    queryKey: ['all-audit-trail', limit],
    queryFn: async (): Promise<AuditRecord[]> => {
      if (!isSuperAdmin) {
        throw new Error('Only super admins can access audit trails');
      }

      const { data, error } = await supabase
        .from('audit_trail_view')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      // Cast the action field to the correct type and remove source_table
      return (data || []).map(({ source_table, ...record }) => ({
        ...record,
        action: record.action as 'INSERT' | 'UPDATE' | 'DELETE'
      }));
    },
    enabled: !!user && isSuperAdmin,
  });
};

// Hook to set audit reason for the next operation
export const useSetAuditReason = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (reason: string) => {
      const { error } = await supabase.rpc('set_audit_reason', {
        reason_text: reason
      });

      if (error) {
        throw error;
      }

      return true;
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to set audit reason: ${error.message}`,
        variant: "destructive",
      });
    },
  });
};

// Hook to get audit trail with filters
export const useFilteredAuditTrail = (
  filters: {
    table_name?: string;
    user_id?: string;
    action?: 'INSERT' | 'UPDATE' | 'DELETE' | '';
    date_from?: string;
    date_to?: string;
    limit?: number;
  } = {}
) => {
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  const isSuperAdmin = userRole === 'super_admin';

  return useQuery({
    queryKey: ['filtered-audit-trail', filters],
    queryFn: async (): Promise<AuditRecord[]> => {
      if (!isSuperAdmin) {
        throw new Error('Only super admins can access audit trails');
      }

      let query = supabase
        .from('audit_trail_view')
        .select('*')
        .order('created_at', { ascending: false });

      if (filters.table_name) {
        query = query.eq('table_name', filters.table_name);
      }

      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id);
      }

      if (filters.action && filters.action !== '') {
        query = query.eq('action', filters.action);
      }

      if (filters.date_from) {
        query = query.gte('created_at', filters.date_from);
      }

      if (filters.date_to) {
        query = query.lte('created_at', filters.date_to);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      } else {
        query = query.limit(100);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      // Cast the action field to the correct type and remove source_table
      return (data || []).map(({ source_table, ...record }) => ({
        ...record,
        action: record.action as 'INSERT' | 'UPDATE' | 'DELETE'
      }));
    },
    enabled: !!user && isSuperAdmin,
  });
};

// Utility function to format audit records for display
export const formatAuditRecord = (record: AuditRecord) => {
  const actionColors = {
    INSERT: 'text-green-600',
    UPDATE: 'text-blue-600',
    DELETE: 'text-red-600'
  };

  const actionLabels = {
    INSERT: 'Created',
    UPDATE: 'Updated',
    DELETE: 'Deleted'
  };

  return {
    ...record,
    actionColor: actionColors[record.action],
    actionLabel: actionLabels[record.action],
    formattedDate: new Date(record.created_at).toLocaleString(),
    changedFieldsText: record.changed_fields?.join(', ') || 'N/A'
  };
};

// Utility function to get human-readable change description
export const getChangeDescription = (record: AuditRecord) => {
  if (record.action === 'INSERT') {
    return `Created new ${record.table_name.replace('_', ' ')}`;
  }

  if (record.action === 'DELETE') {
    return `Deleted ${record.table_name.replace('_', ' ')}`;
  }

  if (record.action === 'UPDATE' && record.changed_fields) {
    const fields = record.changed_fields.map(field => 
      field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    );
    return `Updated ${fields.join(', ')}`;
  }

  return `Modified ${record.table_name.replace('_', ' ')}`;
}; 