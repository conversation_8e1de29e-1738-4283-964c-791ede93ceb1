import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { 
  useLevels, 
  useEventLevels, 
  useCreateEventLevel, 
  useDeleteEventLevel 
} from './useLevels';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(),
          })),
          order: vi.fn(),
        })),
        in: vi.fn(() => ({
          select: vi.fn(),
        })),
        order: vi.fn(),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);

describe('useLevels', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockLevels = [
    {
      id: 'level-1',
      name: 'Intro',
      discipline: 'dressage',
      description: 'Introductory level',
      sort_order: 1,
      is_active: true,
    },
    {
      id: 'level-2',
      name: 'Prelim',
      discipline: 'dressage',
      description: 'Preliminary level',
      sort_order: 2,
      is_active: true,
    },
    {
      id: 'level-3',
      name: 'Novice',
      discipline: 'show_jumping',
      description: 'Novice level',
      sort_order: 1,
      is_active: true,
    },
  ];

  const mockEventLevels = [
    {
      id: 'event-level-1',
      event_id: 'event-1',
      level_id: 'level-1',
      is_enabled: true,
      levels: {
        id: 'level-1',
        name: 'Intro',
        discipline: 'dressage',
        description: 'Introductory level',
        sort_order: 1,
        is_active: true,
      },
    },
    {
      id: 'event-level-2',
      event_id: 'event-1',
      level_id: 'level-2',
      is_enabled: true,
      levels: {
        id: 'level-2',
        name: 'Prelim',
        discipline: 'dressage',
        description: 'Preliminary level',
        sort_order: 2,
        is_active: true,
      },
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
  });

  describe('useLevels', () => {
    it('fetches all levels when no discipline specified', async () => {
      mockUseQuery.mockReturnValue({
        data: mockLevels,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useLevels());

      expect(result.current.data).toEqual(mockLevels);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('fetches levels for a specific discipline', async () => {
      const dressageLevels = mockLevels.filter(level => level.discipline === 'dressage');
      mockUseQuery.mockReturnValue({
        data: dressageLevels,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useLevels('dressage'));

      expect(result.current.data).toEqual(dressageLevels);
      expect(result.current.data?.every(level => level.discipline === 'dressage')).toBe(true);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useLevels());

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch levels');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useLevels());

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('filters out inactive levels', () => {
      const activeLevels = mockLevels.filter(level => level.is_active);
      mockUseQuery.mockReturnValue({
        data: activeLevels,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useLevels());

      expect(result.current.data).toEqual(activeLevels);
      expect(result.current.data?.every(level => level.is_active)).toBe(true);
    });
  });

  describe('useEventLevels', () => {
    it('fetches event levels for a specific event', async () => {
      mockUseQuery.mockReturnValue({
        data: mockEventLevels,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventLevels('event-1'));

      expect(result.current.data).toEqual(mockEventLevels);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('returns empty array when no eventId provided', () => {
      mockUseQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventLevels(undefined));

      expect(result.current.data).toEqual([]);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventLevels('event-1'));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch event levels');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useEventLevels('event-1'));

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('is disabled when no eventId provided', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useEventLevels(undefined));

      expect(mockUseQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false,
        })
      );
    });

    it('includes level details in the response', () => {
      mockUseQuery.mockReturnValue({
        data: mockEventLevels,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useEventLevels('event-1'));

      expect(result.current.data?.[0].levels).toBeDefined();
      expect(result.current.data?.[0].levels?.name).toBe('Intro');
      expect(result.current.data?.[0].levels?.discipline).toBe('dressage');
    });
  });

  describe('useCreateEventLevel', () => {
    it('creates event level successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'event-level-3',
        event_id: 'event-1',
        level_id: 'level-3',
        is_enabled: true,
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEventLevel());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during creation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateEventLevel());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during creation', () => {
      const mockError = new Error('Failed to create event level');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useCreateEventLevel());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'event-level-3',
        event_id: 'event-1',
        level_id: 'level-3',
        is_enabled: true,
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useCreateEventLevel());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });
  });

  describe('useDeleteEventLevel', () => {
    it('deletes event level successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEventLevel());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during deletion', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteEventLevel());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during deletion', () => {
      const mockError = new Error('Failed to delete event level');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useDeleteEventLevel());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useDeleteEventLevel());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });
  });
}); 