import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useCancelBooking } from './useCancelBooking';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      delete: vi.fn(() => ({
        match: vi.fn(() => ({
          eq: vi.fn(),
        })),
        eq: vi.fn(),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
  },
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockToast = vi.mocked(toast);

describe('useCancelBooking', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
  });

  describe('useCancelBooking', () => {
    it('cancels booking successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ success: true });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCancelBooking());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during cancellation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useCancelBooking());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during cancellation', () => {
      const mockError = new Error('Failed to cancel booking');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useCancelBooking());

      expect(result.current.error).toBe(mockError);
    });

    it('shows success toast on successful cancellation', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ success: true });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useCancelBooking());

      // The mutation should be configured to show success toast
      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });

    it('shows error toast on failed cancellation', () => {
      const mockMutationFn = vi.fn().mockRejectedValue(new Error('Cancellation failed'));
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useCancelBooking());

      // The mutation should be configured to show error toast
      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onError: expect.any(Function),
        })
      );
    });

    it('invalidates relevant queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ success: true });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useCancelBooking());

      // The mutation should be configured to invalidate queries on success
      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });

    it('handles booking not found error', () => {
      const mockMutationFn = vi.fn().mockRejectedValue(new Error('Failed to fetch booking'));
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: new Error('Failed to fetch booking'),
      } as any);

      const { result } = renderHook(() => useCancelBooking());

      expect(result.current.error?.message).toBe('Failed to fetch booking');
    });

    it('handles slot deletion error', () => {
      const mockMutationFn = vi.fn().mockRejectedValue(new Error('Failed to delete booking slot'));
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: new Error('Failed to delete booking slot'),
      } as any);

      const { result } = renderHook(() => useCancelBooking());

      expect(result.current.error?.message).toBe('Failed to delete booking slot');
    });

    it('handles time slot update error', () => {
      const mockMutationFn = vi.fn().mockRejectedValue(new Error('Failed to update time slot'));
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: new Error('Failed to update time slot'),
      } as any);

      const { result } = renderHook(() => useCancelBooking());

      expect(result.current.error?.message).toBe('Failed to update time slot');
    });

    it('handles remaining slots check error', () => {
      const mockMutationFn = vi.fn().mockRejectedValue(new Error('Failed to check remaining slots'));
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: new Error('Failed to check remaining slots'),
      } as any);

      const { result } = renderHook(() => useCancelBooking());

      expect(result.current.error?.message).toBe('Failed to check remaining slots');
    });

    it('handles booking deletion error when no slots remain', () => {
      const mockMutationFn = vi.fn().mockRejectedValue(new Error('Failed to delete booking'));
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: new Error('Failed to delete booking'),
      } as any);

      const { result } = renderHook(() => useCancelBooking());

      expect(result.current.error?.message).toBe('Failed to delete booking');
    });
  });
}); 