import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { 
  useLocations, 
  useCreateLocation, 
  useUpdateLocation, 
  useDeleteLocation 
} from './useLocations';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(),
          })),
        })),
        order: vi.fn(),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);

describe('useLocations', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockLocations = [
    {
      id: 'location-1',
      name: 'Main Arena',
      event_id: 'event-1',
      capacity: 50,
      activity_type: 'dressage',
      description: 'Main dressage arena',
      created_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'location-2',
      name: 'Jumping Arena',
      event_id: 'event-1',
      capacity: 30,
      activity_type: 'show_jumping',
      description: 'Show jumping course',
      created_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'location-3',
      name: 'Cross Country Course',
      event_id: 'event-1',
      capacity: 20,
      activity_type: 'cross_country',
      description: 'Cross country course',
      created_at: '2024-01-01T00:00:00Z',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
  });

  describe('useLocations', () => {
    it('fetches locations for a specific event', async () => {
      mockUseQuery.mockReturnValue({
        data: mockLocations,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useLocations('event-1'));

      expect(result.current.data).toEqual(mockLocations);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('returns empty array when no eventId provided', () => {
      mockUseQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useLocations(undefined));

      expect(result.current.data).toEqual([]);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useLocations('event-1'));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch locations');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useLocations('event-1'));

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('is disabled when no eventId provided', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useLocations(undefined));

      expect(mockUseQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false,
        })
      );
    });

    it('filters active locations only', () => {
      mockUseQuery.mockReturnValue({
        data: mockLocations.filter(loc => loc.name !== 'Inactive Arena'),
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useLocations('event-1'));

      expect(result.current.data).toHaveLength(3);
      expect(result.current.data?.every(loc => loc.name !== 'Inactive Arena')).toBe(true);
    });
  });

  describe('useCreateLocation', () => {
    it('creates location successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'location-4',
        name: 'New Arena',
        event_id: 'event-1',
        capacity: 40,
        activity_type: 'dressage',
        description: 'New dressage arena',
        created_at: '2024-01-15T10:00:00Z',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateLocation());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during creation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateLocation());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during creation', () => {
      const mockError = new Error('Failed to create location');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useCreateLocation());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'location-4',
        event_id: 'event-1',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useCreateLocation());

      // The mutation should be configured to invalidate queries on success
      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });
  });

  describe('useUpdateLocation', () => {
    it('updates location successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'location-1',
        name: 'Updated Main Arena',
        event_id: 'event-1',
        capacity: 60,
        activity_type: 'dressage',
        description: 'Updated main dressage arena',
        created_at: '2024-01-01T00:00:00Z',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateLocation());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during update', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateLocation());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during update', () => {
      const mockError = new Error('Failed to update location');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useUpdateLocation());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'location-1',
        event_id: 'event-1',
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useUpdateLocation());

      // The mutation should be configured to invalidate queries on success
      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });
  });

  describe('useDeleteLocation', () => {
    it('deletes location successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteLocation());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during deletion', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteLocation());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during deletion', () => {
      const mockError = new Error('Failed to delete location');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useDeleteLocation());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useDeleteLocation());

      // The mutation should be configured to invalidate queries on success
      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });
  });
}); 