import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { getErrorMessage } from '@/types/errors';

export interface Organizer {
  id: string;
  full_name: string;
  email: string;
  company_name?: string;
  business_name?: string;
  created_at?: string;
  updated_at?: string;
}

// Hook to get all organizers
export const useOrganizers = () => {
  return useQuery({
    queryKey: ['organizers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('organizers')
        .select('*')
        .order('full_name');

      if (error) {
        console.error('Error fetching organizers:', error);
        throw error;
      }

      return data as Organizer[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook to create a new organizer
export const useCreateOrganizer = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (organizerData: Omit<Organizer, 'id' | 'created_at' | 'updated_at'>) => {
      const { data, error } = await supabase
        .from('organizers')
        .insert([organizerData])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data as Organizer;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizers'] });
      toast({
        title: "Success",
        description: "Organizer created successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};

// Hook to update an organizer
export const useUpdateOrganizer = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...organizerData }: Partial<Organizer> & { id: string }) => {
      const { data, error } = await supabase
        .from('organizers')
        .update(organizerData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data as Organizer;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizers'] });
      toast({
        title: "Success",
        description: "Organizer updated successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};

// Hook to delete an organizer
export const useDeleteOrganizer = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (organizerId: string) => {
      const { error } = await supabase
        .from('organizers')
        .delete()
        .eq('id', organizerId);

      if (error) {
        throw error;
      }

      return organizerId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizers'] });
      toast({
        title: "Success",
        description: "Organizer deleted successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
}; 