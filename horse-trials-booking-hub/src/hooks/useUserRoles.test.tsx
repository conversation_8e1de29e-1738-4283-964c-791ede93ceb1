import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useAllUsers, useUpdateUserRole, useUserRole, useIsSuperAdmin } from './useUserRoles';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useSession } from '@supabase/auth-helpers-react';
import { useToast } from '@/hooks/use-toast';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    rpc: vi.fn(),
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
    })),
  },
}));

// Mock Supabase Auth Helpers
vi.mock('@supabase/auth-helpers-react', () => ({
  useSession: vi.fn(),
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseSession = vi.mocked(useSession);
const mockUseToast = vi.mocked(useToast);

describe('useUserRoles', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockSession = {
    user: {
      id: 'user-1',
      email: '<EMAIL>',
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
    mockUseSession.mockReturnValue(mockSession);
  });

  describe('useAllUsers', () => {
    it('fetches all users successfully', async () => {
      const mockUsers = [
        {
          id: 'user-1',
          full_name: 'John Doe',
          email: '<EMAIL>',
          phone: '************',
          user_roles: [{ role: 'super_admin' }],
        },
        {
          id: 'user-2',
          full_name: 'Jane Smith',
          email: '<EMAIL>',
          phone: null,
          user_roles: [{ role: 'user' }],
        },
      ];

      mockUseQuery.mockReturnValue({
        data: mockUsers,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useAllUsers());

      expect(result.current.data).toEqual(mockUsers);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useAllUsers());

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch users');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useAllUsers());

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('useUpdateUserRole', () => {
    it('updates user role successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ id: 'role-1', user_id: 'user-1', role: 'organizer' });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateUserRole());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during mutation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateUserRole());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during mutation', () => {
      const mockError = new Error('Failed to update role');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useUpdateUserRole());

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useUserRole', () => {
    it('returns super_admin as highest role', () => {
      const mockUserRoles = [
        { id: 'role-1', user_id: 'user-1', role: 'super_admin', created_at: '2024-01-01' },
        { id: 'role-2', user_id: 'user-1', role: 'organizer', created_at: '2024-01-01' },
      ];

      mockUseQuery.mockReturnValue({
        data: mockUserRoles,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUserRole());

      expect(result.current.userRole).toBe('super_admin');
      expect(result.current.isLoading).toBe(false);
    });

    it('returns organizer as highest role when no super_admin', () => {
      const mockUserRoles = [
        { id: 'role-1', user_id: 'user-1', role: 'organizer', created_at: '2024-01-01' },
        { id: 'role-2', user_id: 'user-1', role: 'user', created_at: '2024-01-01' },
      ];

      mockUseQuery.mockReturnValue({
        data: mockUserRoles,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUserRole());

      expect(result.current.userRole).toBe('organizer');
    });

    it('returns user as default role when no roles exist', () => {
      mockUseQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUserRole());

      expect(result.current.userRole).toBe('user');
    });

    it('returns user as default role when roles is null', () => {
      mockUseQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUserRole());

      expect(result.current.userRole).toBe('user');
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useUserRole());

      expect(result.current.isLoading).toBe(true);
    });
  });

  describe('useIsSuperAdmin', () => {
    it('returns true when user has super_admin role', () => {
      const mockUserRoles = [
        { id: 'role-1', user_id: 'user-1', role: 'super_admin', created_at: '2024-01-01' },
      ];

      mockUseQuery.mockReturnValue({
        data: mockUserRoles,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useIsSuperAdmin());

      expect(result.current.data).toBe(true);
      expect(result.current.isLoading).toBe(false);
    });

    it('returns false when user does not have super_admin role', () => {
      const mockUserRoles = [
        { id: 'role-1', user_id: 'user-1', role: 'organizer', created_at: '2024-01-01' },
      ];

      mockUseQuery.mockReturnValue({
        data: mockUserRoles,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useIsSuperAdmin());

      expect(result.current.data).toBe(false);
    });

    it('returns false when no roles exist', () => {
      mockUseQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useIsSuperAdmin());

      expect(result.current.data).toBe(false);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useIsSuperAdmin());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch roles');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useIsSuperAdmin());

      expect(result.current.error).toBe(mockError);
    });
  });
}); 