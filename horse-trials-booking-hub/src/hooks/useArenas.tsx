import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { getErrorMessage } from '@/types/errors';

export interface Arena {
  id: string;
  name: string;
  activity_type: string;
  slot_duration_minutes: number;
  fixed_level?: string;
  description?: string;
  event_id: string;
  created_at?: string;
  created_by?: string;
}

export const useArenas = (eventId?: string) => {
  return useQuery({
    queryKey: ['arenas', eventId],
    queryFn: async () => {
      console.log('Fetching arenas for event:', eventId);
      let query = supabase.from('arenas').select('*');
      
      if (eventId) {
        query = query.eq('event_id', eventId);
      }
      
      const { data, error } = await query.order('name');

      if (error) {
        console.error('Error fetching arenas:', error);
        throw error;
      }

      console.log('Arenas fetched:', data);
      return data as Arena[];
    },
    enabled: !!eventId,
  });
};

export const useCreateArena = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (arenaData: Omit<Arena, 'id' | 'created_at'>) => {
      const { data, error } = await supabase
        .from('arenas')
        .insert([arenaData])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['arenas'] });
      toast({
        title: "Success",
        description: "Arena created successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};

export const useUpdateArena = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...arenaData }: Partial<Arena> & { id: string }) => {
      const { data, error } = await supabase
        .from('arenas')
        .update(arenaData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['arenas'] });
      toast({
        title: "Success",
        description: "Arena updated successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};

export const useDeleteArena = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (arenaId: string) => {
      const { error } = await supabase
        .from('arenas')
        .delete()
        .eq('id', arenaId);

      if (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['arenas'] });
      toast({
        title: "Success",
        description: "Arena deleted successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};
