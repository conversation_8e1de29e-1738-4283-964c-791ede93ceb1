import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { 
  usePricingSettings, 
  useUpdatePricingSettings, 
  getCartRetentionMinutes, 
  getActivityPrice,
  defaultPricingSettings 
} from './usePricingSettings';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(),
        })),
      })),
    })),
    rpc: vi.fn(),
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);

describe('usePricingSettings', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockPricingSettings = {
    dressage_price: 45,
    show_jumping_price: 60,
    cross_country_price: 75,
    currency_symbol: '$',
    cart_retention_minutes: 20,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
  });

  describe('usePricingSettings', () => {
    it('fetches pricing settings successfully', async () => {
      mockUseQuery.mockReturnValue({
        data: mockPricingSettings,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => usePricingSettings());

      expect(result.current.data).toEqual(mockPricingSettings);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('returns default settings when no settings found', () => {
      mockUseQuery.mockReturnValue({
        data: defaultPricingSettings,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => usePricingSettings());

      expect(result.current.data).toEqual(defaultPricingSettings);
      expect(result.current.data?.dressage_price).toBe(30);
      expect(result.current.data?.currency_symbol).toBe('$');
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => usePricingSettings());

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch pricing settings');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => usePricingSettings());

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('validates and sanitizes pricing settings data', () => {
      const invalidSettings = {
        dressage_price: 'invalid',
        show_jumping_price: null,
        cross_country_price: 75,
        currency_symbol: 123,
        cart_retention_minutes: 'not a number',
      };

      const expectedValidatedSettings = {
        dressage_price: 30, // default
        show_jumping_price: 25, // default
        cross_country_price: 75, // valid
        currency_symbol: '$', // default
        cart_retention_minutes: 15, // default
      };

      mockUseQuery.mockReturnValue({
        data: expectedValidatedSettings,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => usePricingSettings());

      expect(result.current.data).toEqual(expectedValidatedSettings);
    });
  });

  describe('useUpdatePricingSettings', () => {
    it('updates pricing settings successfully via RPC', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ success: true });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdatePricingSettings());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during update', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdatePricingSettings());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during update', () => {
      const mockError = new Error('Failed to update pricing settings');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useUpdatePricingSettings());

      expect(result.current.error).toBe(mockError);
    });

    it('invalidates queries on success', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ success: true });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useUpdatePricingSettings());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });

    it('validates input data before updating', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ success: true });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useUpdatePricingSettings());

      expect(mockUseMutation).toHaveBeenCalledWith(
        expect.objectContaining({
          mutationFn: expect.any(Function),
        })
      );
    });
  });

  describe('getCartRetentionMinutes', () => {
    it('returns cart retention minutes from settings', () => {
      const result = getCartRetentionMinutes(mockPricingSettings);
      expect(result).toBe(20);
    });

    it('returns default when no settings provided', () => {
      const result = getCartRetentionMinutes();
      expect(result).toBe(15); // default value
    });

    it('returns default when settings is undefined', () => {
      const result = getCartRetentionMinutes(undefined);
      expect(result).toBe(15);
    });

    it('returns default when cart_retention_minutes is missing', () => {
      const settingsWithoutRetention = {
        ...mockPricingSettings,
        cart_retention_minutes: undefined,
      };
      const result = getCartRetentionMinutes(settingsWithoutRetention as any);
      expect(result).toBe(15);
    });
  });

  describe('getActivityPrice', () => {
    it('returns dressage price for dressage activity', () => {
      const result = getActivityPrice('dressage', mockPricingSettings);
      expect(result).toBe(45);
    });

    it('returns show jumping price for show_jumping activity', () => {
      const result = getActivityPrice('show_jumping', mockPricingSettings);
      expect(result).toBe(60);
    });

    it('returns cross country price for cross_country activity', () => {
      const result = getActivityPrice('cross_country', mockPricingSettings);
      expect(result).toBe(75);
    });

    it('returns default dressage price for unknown activity', () => {
      const result = getActivityPrice('unknown_activity', mockPricingSettings);
      expect(result).toBe(30); // default dressage price
    });

    it('returns default price when no settings provided', () => {
      const result = getActivityPrice('dressage');
      expect(result).toBe(30);
    });

    it('returns default price when settings is undefined', () => {
      const result = getActivityPrice('dressage', undefined);
      expect(result).toBe(30);
    });

    it('handles case insensitive activity types', () => {
      const result = getActivityPrice('DRESSAGE', mockPricingSettings);
      expect(result).toBe(30); // default because it doesn't match exactly
    });

    it('returns default for empty activity type', () => {
      const result = getActivityPrice('', mockPricingSettings);
      expect(result).toBe(30);
    });
  });

  describe('defaultPricingSettings', () => {
    it('has correct default values', () => {
      expect(defaultPricingSettings.dressage_price).toBe(30);
      expect(defaultPricingSettings.show_jumping_price).toBe(25);
      expect(defaultPricingSettings.cross_country_price).toBe(40);
      expect(defaultPricingSettings.currency_symbol).toBe('$');
      expect(defaultPricingSettings.cart_retention_minutes).toBe(15);
    });

    it('is used as fallback when settings are invalid', () => {
      const invalidSettings = {
        dressage_price: 'not a number',
        show_jumping_price: null,
        cross_country_price: undefined,
        currency_symbol: 123,
        cart_retention_minutes: 'invalid',
      };

      // This would be handled by the validation logic in usePricingSettings
      expect(defaultPricingSettings).toEqual({
        dressage_price: 30,
        show_jumping_price: 25,
        cross_country_price: 40,
        currency_symbol: '$',
        cart_retention_minutes: 15,
      });
    });
  });
}); 