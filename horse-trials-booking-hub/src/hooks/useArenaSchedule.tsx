import { useMemo } from 'react';
import { useActivities, Activity } from './useActivities';

export interface ScheduleItem {
  id: string;
  type: 'activity' | 'break';
  start_time: string;
  end_time: string;
  duration_minutes: number;
  activity_type?: string;
  level?: string;
  description?: string;
}

export const useArenaSchedule = (arenaId?: string) => {
  const { data: activities, isLoading, error } = useActivities(arenaId);
  
  const schedule = useMemo(() => {
    if (!activities || activities.length === 0) {
      return [];
    }
    
    // Sort activities by start_time
    const sortedActivities = [...activities].sort((a, b) => 
      new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
    );
    
    const scheduleItems: ScheduleItem[] = [];
    
    // Process each activity and identify breaks
    for (let i = 0; i < sortedActivities.length; i++) {
      const activity = sortedActivities[i];
      const activityStart = new Date(activity.start_time);
      const activityEnd = new Date(activity.end_time);
      
      // Calculate duration in minutes
      const durationMinutes = Math.round((activityEnd.getTime() - activityStart.getTime()) / (1000 * 60));
      
      // Add the activity to the schedule
      scheduleItems.push({
        id: activity.id,
        type: 'activity',
        start_time: activity.start_time,
        end_time: activity.end_time,
        duration_minutes: durationMinutes,
        activity_type: activity.activity_type,
        level: activity.level,
        description: activity.description
      });
      
      // Check if there's a break before the next activity
      if (i < sortedActivities.length - 1) {
        const nextActivity = sortedActivities[i + 1];
        const nextActivityStart = new Date(nextActivity.start_time);
        
        // If there's a gap between this activity end and next activity start
        if (activityEnd.getTime() < nextActivityStart.getTime()) {
          const breakDurationMinutes = Math.round(
            (nextActivityStart.getTime() - activityEnd.getTime()) / (1000 * 60)
          );
          
          // Only add breaks that are at least 5 minutes
          if (breakDurationMinutes >= 5) {
            scheduleItems.push({
              id: `break-${activity.id}-${nextActivity.id}`,
              type: 'break',
              start_time: activity.end_time,
              end_time: nextActivity.start_time,
              duration_minutes: breakDurationMinutes
            });
          }
        }
      }
    }
    
    return scheduleItems;
  }, [activities]);
  
  // Calculate overall schedule statistics
  const scheduleStats = useMemo(() => {
    if (!schedule || schedule.length === 0) {
      return {
        start_time: null,
        end_time: null,
        total_duration_minutes: 0,
        activity_minutes: 0,
        break_minutes: 0,
        activity_count: 0,
        break_count: 0
      };
    }
    
    const start_time = schedule[0].start_time;
    const end_time = schedule[schedule.length - 1].end_time;
    
    let activity_minutes = 0;
    let break_minutes = 0;
    let activity_count = 0;
    let break_count = 0;
    
    schedule.forEach(item => {
      if (item.type === 'activity') {
        activity_minutes += item.duration_minutes;
        activity_count++;
      } else {
        break_minutes += item.duration_minutes;
        break_count++;
      }
    });
    
    const total_duration_minutes = activity_minutes + break_minutes;
    
    return {
      start_time,
      end_time,
      total_duration_minutes,
      activity_minutes,
      break_minutes,
      activity_count,
      break_count
    };
  }, [schedule]);
  
  // Format time for display (e.g., "09:30 AM")
  const formatTime = (isoTime: string) => {
    const date = new Date(isoTime);
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };
  
  return {
    schedule,
    scheduleStats,
    formatTime,
    isLoading,
    error
  };
};
