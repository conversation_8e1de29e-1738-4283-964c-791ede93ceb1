import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { usePricingSettings, getCartRetentionMinutes } from '@/hooks/usePricingSettings';

export interface SlotReservation {
  id: string;
  time_slot_id: string;
  user_session_id: string;
  reserved_at: string;
  expires_at: string;
  created_at: string;
}

// Generate a session ID for anonymous users with better persistence
export const getUserSessionId = (): string => {
  const STORAGE_KEY = 'user_session_id';
  
  // Try to get from localStorage first
  let sessionId = localStorage.getItem(STORAGE_KEY);
  
  if (!sessionId) {
    // Create a new session ID with timestamp and random string
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem(STORAGE_KEY, sessionId);
    console.log('Created new session ID:', sessionId);
  }
  
  return sessionId;
};

export const useSlotReservations = (userSessionId: string) => {
  return useQuery({
    queryKey: ['slot_reservations', userSessionId],
    queryFn: async () => {
      if (!userSessionId) return [];
      
      // First, get the basic reservation data
      const { data: reservations, error } = await supabase
        .from('slot_reservations')
        .select(`
          *,
          time_slots (
            *,
            locations (
              name,
              activity_type,
              fixed_level
            )
          )
        `)
        .eq('user_session_id', userSessionId)
        .gt('expires_at', new Date().toISOString());
      
      if (error) {
        console.error('Error fetching reservations:', error);
        throw error;
      }
      
      // For reservations with dressage test IDs, fetch the test details separately
      const enhancedReservations = await Promise.all(reservations.map(async (reservation) => {
        const timeSlot = reservation.time_slots;
        const location = timeSlot?.locations;
        
        // Format the time for display
        let formattedTime = '';
        if (timeSlot?.start_time && timeSlot?.end_time) {
          const start = new Date(timeSlot.start_time);
          const end = new Date(timeSlot.end_time);
          formattedTime = `${start.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} - ${end.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
        }
        return {
          ...reservation,
          time_slot_id: timeSlot?.id,
          location_name: location?.name || 'Unknown Location',
          activity_type: location?.activity_type || 'booking',
          level: timeSlot?.level || location?.fixed_level,
          time: formattedTime || 'Unknown Time',
        };
      }));
      
      return enhancedReservations;
    },
    refetchInterval: 10000, // Refetch every 10 seconds to update expiration times
  });
};

export const useReserveSlot = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { data: pricingSettings } = usePricingSettings();

  return useMutation({
    mutationFn: async ({ 
      timeSlotId, 
      userSessionId, 
    }: { 
      timeSlotId: string, 
      userSessionId: string,
    }) => {
      console.log('🔍 [DEBUG] useReserveSlot: Starting reservation process for timeSlotId:', timeSlotId);
      
      // First check if slot is already reserved or booked
      const { data: existingReservation, error: checkError } = await supabase
        .from('slot_reservations')
        .select('id, time_slot_id, user_session_id, expires_at')
        .eq('time_slot_id', timeSlotId)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('❌ [DEBUG] useReserveSlot: Error checking existing reservations:', checkError);
        throw checkError;
      }

      if (existingReservation) {
        console.log('❌ [DEBUG] useReserveSlot: Slot already reserved by another user:', existingReservation);
        throw new Error('This time slot is already reserved by another user');
      }

      // Check if time slot is already booked
      const { data: timeSlot, error: slotError } = await supabase
        .from('time_slots')
        .select('is_booked, location_id')
        .eq('id', timeSlotId)
        .single();

      if (slotError) {
        console.error('❌ [DEBUG] useReserveSlot: Error checking time slot:', slotError);
        throw slotError;
      }

      console.log('🔍 [DEBUG] useReserveSlot: Time slot check result:', {
        timeSlotId,
        timeSlot,
        isBooked: timeSlot?.is_booked,
        locationId: timeSlot?.location_id
      });

      if (timeSlot?.is_booked) {
        console.log('❌ [DEBUG] useReserveSlot: Time slot is already booked');
        throw new Error('This time slot is already booked');
      }

      // Get retention time from settings
      const retentionMinutes = getCartRetentionMinutes(pricingSettings);
      const expiresAt = new Date(Date.now() + retentionMinutes * 60 * 1000).toISOString();

      console.log('🔍 [DEBUG] useReserveSlot: Creating reservation with data:', {
        timeSlotId,
        userSessionId,
        retentionMinutes,
        expiresAt
      });

      // Create reservation
      const { data, error } = await supabase
        .from('slot_reservations')
        .insert([{
          time_slot_id: timeSlotId,
          user_session_id: userSessionId,
          expires_at: expiresAt,
        }])
        .select()
        .single();

      if (error) {
        console.error('❌ [DEBUG] useReserveSlot: Error creating reservation:', error);
        throw error;
      }
      
      console.log('✅ [DEBUG] useReserveSlot: Successfully created reservation:', data);
      return data;
    },
    onSuccess: () => {
      // Invalidate and refetch reservations immediately
      queryClient.invalidateQueries({ queryKey: ['slot_reservations'] });
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
      
      toast({
        title: "Slot Reserved",
        description: "Time slot added to your cart",
      });
    },
    onError: (error: any) => {
      console.error('❌ [DEBUG] useReserveSlot: Reservation failed:', error);
      toast({
        title: "Reservation Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useExtendReservation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ reservationId }: { reservationId: string }) => {
      const { data, error } = await supabase
        .from('slot_reservations')
        .update({
          expires_at: new Date(Date.now() + 15 * 60 * 1000).toISOString()
        })
        .eq('id', reservationId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['slot_reservations'] });
    },
  });
};

export const useReleaseReservation = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ reservationId }: { reservationId: string }) => {
      console.log('Releasing reservation:', reservationId);
      
      const { error } = await supabase
        .from('slot_reservations')
        .delete()
        .eq('id', reservationId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['slot_reservations'] });
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
      
      toast({
        title: "Removed from Cart",
        description: "Time slot released",
      });
    },
    onError: (error: any) => {
      console.error('Failed to release reservation:', error);
      toast({
        title: "Error",
        description: "Failed to remove slot from cart",
        variant: "destructive",
      });
    },
  });
};

export const useCleanupExpiredReservations = () => {
  return useMutation({
    mutationFn: async () => {
      const { error } = await supabase.rpc('cleanup_expired_reservations');
      if (error) throw error;
    },
  });
};
