import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { usePricingSettings, getCartRetentionMinutes } from '@/hooks/usePricingSettings';
import { getErrorMessage } from '../types/errors';

export interface SlotReservation {
  id: string;
  time_slot_id: string;
  user_session_id: string;
  expires_at: string;
  created_at: string;
  // Event information from the view
  event_id?: string;
  event_name?: string;
  event_start_date?: string;
  event_end_date?: string;
  // Location information from the view
  location_id?: string;
  location_name?: string;
  location_activity_type?: string;
  // Time slot information from the view
  start_time?: string;
  end_time?: string;
  time_slot_level?: string;
  is_booked?: boolean;
  // Activity information from the view
  activity_id?: string;
  activity_type?: string;
  activity_level?: string;
}

// Generate a session ID for anonymous users with better persistence
export const getUserSessionId = (): string => {
  const STORAGE_KEY = 'user_session_id';
  
  // Try to get from localStorage first
  let sessionId = localStorage.getItem(STORAGE_KEY);
  
  if (!sessionId) {
    // Create a new session ID with timestamp and random string
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem(STORAGE_KEY, sessionId);
    console.log('Created new session ID:', sessionId);
  }
  
  return sessionId;
};

// Hook to get reservations for a specific event or all events
export const useSlotReservations = (userSessionId: string, eventId?: string) => {
  return useQuery({
    queryKey: ['slot_reservations', userSessionId, eventId],
    queryFn: async () => {
      if (!userSessionId) return [];
      
      // Use the new view that includes event information
      let query = supabase
        .from('slot_reservations_with_event')
        .select('*')
        .eq('user_session_id', userSessionId)
        .gt('expires_at', new Date().toISOString());
      
      // If eventId is provided, filter by event
      if (eventId) {
        query = query.eq('event_id', eventId);
      }
      
      const { data: reservations, error } = await query;
      
      if (error) {
        console.error('Error fetching reservations:', error);
        throw error;
      }
      
      // Format the time for display
      const enhancedReservations = reservations.map((reservation) => {
        // Format the time for display
        let formattedTime = '';
        if (reservation.start_time && reservation.end_time) {
          const start = new Date(reservation.start_time);
          const end = new Date(reservation.end_time);
          formattedTime = `${start.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} - ${end.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
        }
        
        return {
          ...reservation,
          time: formattedTime || 'Unknown Time',
          // Use activity_type from the view, fallback to location_activity_type
          activity_type: reservation.activity_type || reservation.location_activity_type || 'booking',
          // Use level from activity, then time slot, then location
          level: reservation.activity_level || reservation.time_slot_level || null,
        };
      });
      
      return enhancedReservations;
    },
    refetchInterval: 10000, // Refetch every 10 seconds to update expiration times
  });
};

export const useReserveSlot = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { data: pricingSettings } = usePricingSettings();

  return useMutation({
    mutationFn: async ({ 
      timeSlotId, 
      userSessionId,
      eventId, // Keep eventId for validation
    }: { 
      timeSlotId: string, 
      userSessionId: string,
      eventId: string,
    }) => {
      console.log('🔍 [DEBUG] useReserveSlot: Starting reservation process for timeSlotId:', timeSlotId, 'eventId:', eventId);
      
      // First check if slot is already reserved or booked - use the view instead of direct table access
      const { data: existingReservations, error: checkError } = await supabase
        .from('slot_reservations_with_event')
        .select('id, time_slot_id, user_session_id, expires_at')
        .eq('time_slot_id', timeSlotId)
        .gt('expires_at', new Date().toISOString());

      if (checkError) {
        console.error('❌ [DEBUG] useReserveSlot: Error checking existing reservations:', checkError);
        throw checkError;
      }

      // Check if any other user has reserved this slot
      const otherUserReservation = existingReservations?.find(res => res.user_session_id !== userSessionId);
      if (otherUserReservation) {
        console.log('❌ [DEBUG] useReserveSlot: Slot already reserved by another user:', otherUserReservation);
        throw new Error('This time slot is already reserved by another user');
      }

      // Check if time slot is already booked and validate it belongs to the correct event
      const { data: timeSlot, error: slotError } = await supabase
        .from('time_slots')
        .select(`
          is_booked, 
          location_id,
          locations (
            event_id
          )
        `)
        .eq('id', timeSlotId)
        .single();

      if (slotError) {
        console.error('❌ [DEBUG] useReserveSlot: Error checking time slot:', slotError);
        throw slotError;
      }

      console.log('🔍 [DEBUG] useReserveSlot: Time slot check result:', {
        timeSlotId,
        timeSlot,
        isBooked: timeSlot?.is_booked,
        locationId: timeSlot?.location_id,
        slotEventId: timeSlot?.locations?.event_id,
        requestedEventId: eventId
      });

      if (timeSlot?.is_booked) {
        console.log('❌ [DEBUG] useReserveSlot: Time slot is already booked');
        throw new Error('This time slot is already booked');
      }

      // Validate that the slot belongs to the requested event
      if (timeSlot?.locations?.event_id !== eventId) {
        console.log('❌ [DEBUG] useReserveSlot: Slot does not belong to requested event');
        throw new Error('This time slot does not belong to the current event');
      }

      // Get retention time from settings
      const retentionMinutes = getCartRetentionMinutes(pricingSettings);
      const expiresAt = new Date(Date.now() + retentionMinutes * 60 * 1000).toISOString();

      console.log('🔍 [DEBUG] useReserveSlot: Creating reservation with data:', {
        timeSlotId,
        userSessionId,
        eventId,
        retentionMinutes,
        expiresAt
      });

      // Create reservation using the view (which will trigger the insert function)
      const { data, error } = await supabase
        .from('slot_reservations_with_event')
        .insert([{
          time_slot_id: timeSlotId,
          user_session_id: userSessionId,
          expires_at: expiresAt,
        }])
        .select()
        .single();

      if (error) {
        console.error('❌ [DEBUG] useReserveSlot: Error creating reservation:', error);
        throw error;
      }
      
      console.log('✅ [DEBUG] useReserveSlot: Successfully created reservation:', data);
      return data;
    },
    onSuccess: () => {
      // Invalidate and refetch reservations immediately
      queryClient.invalidateQueries({ queryKey: ['slot_reservations'] });
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
      
      toast({
        title: "Slot Reserved",
        description: "Time slot added to your cart",
      });
    },
    onError: (error: unknown) => {
      console.error('❌ [DEBUG] useReserveSlot: Reservation failed:', error);
      toast({
        title: "Reservation Failed",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};

export const useExtendReservation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ reservationId }: { reservationId: string }) => {
      // Use the view for updates instead of direct table access
      const { data, error } = await supabase
        .from('slot_reservations_with_event')
        .update({
          expires_at: new Date(Date.now() + 15 * 60 * 1000).toISOString()
        })
        .eq('id', reservationId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['slot_reservations'] });
    },
  });
};

export const useReleaseReservation = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ reservationId }: { reservationId: string }) => {
      console.log('Releasing reservation:', reservationId);
      
      // Use the view for deletes instead of direct table access
      const { error } = await supabase
        .from('slot_reservations_with_event')
        .delete()
        .eq('id', reservationId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['slot_reservations'] });
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
      
      toast({
        title: "Removed from Cart",
        description: "Time slot released",
      });
    },
    onError: (error: unknown) => {
      console.error('Failed to release reservation:', error);
      toast({
        title: "Error",
        description: "Failed to remove slot from cart",
        variant: "destructive",
      });
    },
  });
};

export const useCleanupExpiredReservations = () => {
  return useMutation({
    mutationFn: async () => {
      const { error } = await supabase.rpc('cleanup_expired_reservations');
      if (error) throw error;
    },
  });
};
