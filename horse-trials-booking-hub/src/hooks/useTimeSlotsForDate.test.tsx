import React from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useTimeSlotsForDate } from './useTimeSlotsForDate';
import { supabase } from '@/integrations/supabase/client';

vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
  },
}));

const mockSupabase = vi.mocked(supabase);

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useTimeSlotsForDate', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('returns empty array if locationId or date is missing', async () => {
    const { result } = renderHook(() => useTimeSlotsForDate(undefined, undefined), {
      wrapper: createWrapper(),
    });
    expect(result.current.data).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
  });

  it('fetches and maps slots and bookings correctly', async () => {
    const slotDefs = [
      { id: 'slot-1', location_id: 'loc-1', start_time: '09:00:00', end_time: '10:00:00', is_bookable: true },
      { id: 'slot-2', location_id: 'loc-1', start_time: '10:00:00', end_time: '11:00:00', is_bookable: true },
      { id: 'slot-3', location_id: 'loc-1', start_time: '11:00:00', end_time: '12:00:00', is_bookable: false },
    ];
    const bookings = [
      { id: 'booking-1', time_slot_id: 'slot-1', booking_date: '2024-07-28', user_id: 'user-1', rider_name: 'Rider 1', horse_name: 'Horse 1', status: 'confirmed', location_id: 'loc-1' },
      { id: 'booking-2', time_slot_id: 'slot-2', booking_date: '2024-07-28', user_id: 'user-2', rider_name: 'Rider 2', horse_name: 'Horse 2', status: 'pending', location_id: 'loc-1' },
      { id: 'booking-3', time_slot_id: 'slot-3', booking_date: '2024-07-28', user_id: 'user-3', rider_name: 'Rider 3', horse_name: 'Horse 3', status: 'cancelled', location_id: 'loc-1' },
    ];
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === 'time_slots') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockResolvedValue({ data: slotDefs, error: null }),
        } as any;
      }
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockReturnThis(),
          // Only return bookings for the correct date/location
          data: bookings,
          mockResolvedValue: vi.fn(),
          then: function (cb: any) { cb({ data: bookings, error: null }); return this; },
        } as any;
      }
      return {} as any;
    });

    const { result } = renderHook(() => useTimeSlotsForDate('loc-1', '2024-07-28'), {
      wrapper: createWrapper(),
    });

    await waitFor(() => expect(result.current.isSuccess).toBe(true));
    expect(result.current.data).toEqual([
      {
        id: 'slot-1-2024-07-28',
        time: '09:00 AM',
        status: 'booked',
        riderName: 'Rider 1',
        horseName: 'Horse 1',
        bookingId: 'booking-1',
      },
      {
        id: 'slot-2-2024-07-28',
        time: '10:00 AM',
        status: 'pending',
        riderName: 'Rider 2',
        horseName: 'Horse 2',
        bookingId: 'booking-2',
      },
      {
        id: 'slot-3-2024-07-28',
        time: '11:00 AM',
        status: 'unavailable',
        riderName: undefined,
        horseName: undefined,
        bookingId: undefined,
      },
    ]);
  });

  it('handles no bookings', async () => {
    const slotDefs = [
      { id: 'slot-1', location_id: 'loc-1', start_time: '09:00:00', end_time: '10:00:00', is_bookable: true },
    ];
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === 'time_slots') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockResolvedValue({ data: slotDefs, error: null }),
        } as any;
      }
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockReturnThis(),
          data: [],
          mockResolvedValue: vi.fn(),
          then: function (cb: any) { cb({ data: [], error: null }); return this; },
        } as any;
      }
      return {} as any;
    });
    const { result } = renderHook(() => useTimeSlotsForDate('loc-1', '2024-07-28'), {
      wrapper: createWrapper(),
    });
    await waitFor(() => expect(result.current.isSuccess).toBe(true));
    expect(result.current.data?.[0].status).toBe('available');
  });

  it('handles unavailable slots', async () => {
    const slotDefs = [
      { id: 'slot-1', location_id: 'loc-1', start_time: '09:00:00', end_time: '10:00:00', is_bookable: false },
    ];
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === 'time_slots') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockResolvedValue({ data: slotDefs, error: null }),
        } as any;
      }
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockReturnThis(),
          data: [],
          mockResolvedValue: vi.fn(),
          then: function (cb: any) { cb({ data: [], error: null }); return this; },
        } as any;
      }
      return {} as any;
    });
    const { result } = renderHook(() => useTimeSlotsForDate('loc-1', '2024-07-28'), {
      wrapper: createWrapper(),
    });
    await waitFor(() => expect(result.current.isSuccess).toBe(true));
    expect(result.current.data?.[0].status).toBe('unavailable');
  });

  it('handles cancelled bookings (should not show as booked)', async () => {
    const slotDefs = [
      { id: 'slot-1', location_id: 'loc-1', start_time: '09:00:00', end_time: '10:00:00', is_bookable: true },
    ];
    const bookings = [
      { id: 'booking-1', time_slot_id: 'slot-1', booking_date: '2024-07-28', user_id: 'user-1', rider_name: 'Rider 1', horse_name: 'Horse 1', status: 'cancelled', location_id: 'loc-1' },
    ];
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === 'time_slots') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockResolvedValue({ data: slotDefs, error: null }),
        } as any;
      }
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockReturnThis(),
          data: bookings,
          mockResolvedValue: vi.fn(),
          then: function (cb: any) { cb({ data: bookings, error: null }); return this; },
        } as any;
      }
      return {} as any;
    });
    const { result } = renderHook(() => useTimeSlotsForDate('loc-1', '2024-07-28'), {
      wrapper: createWrapper(),
    });
    await waitFor(() => expect(result.current.isSuccess).toBe(true));
    expect(result.current.data?.[0].status).toBe('available');
  });

  it('handles error from time_slots query', async () => {
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === 'time_slots') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockResolvedValue({ data: null, error: new Error('Slots error') }),
        } as any;
      }
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockReturnThis(),
          data: [],
          mockResolvedValue: vi.fn(),
          then: function (cb: any) { cb({ data: [], error: null }); return this; },
        } as any;
      }
      return {} as any;
    });
    const { result } = renderHook(() => useTimeSlotsForDate('loc-1', '2024-07-28'), {
      wrapper: createWrapper(),
    });
    await waitFor(() => expect(result.current.isError).toBe(true));
    expect(result.current.error).toBeInstanceOf(Error);
  });

  it('handles error from bookings query', async () => {
    const slotDefs = [
      { id: 'slot-1', location_id: 'loc-1', start_time: '09:00:00', end_time: '10:00:00', is_bookable: true },
    ];
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === 'time_slots') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockResolvedValue({ data: slotDefs, error: null }),
        } as any;
      }
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          order: vi.fn().mockReturnThis(),
          data: null,
          mockResolvedValue: vi.fn(),
          then: function (cb: any) { cb({ data: null, error: new Error('Bookings error') }); return this; },
        } as any;
      }
      return {} as any;
    });
    const { result } = renderHook(() => useTimeSlotsForDate('loc-1', '2024-07-28'), {
      wrapper: createWrapper(),
    });
    await waitFor(() => expect(result.current.isError).toBe(true));
    expect(result.current.error).toBeInstanceOf(Error);
  });
}); 