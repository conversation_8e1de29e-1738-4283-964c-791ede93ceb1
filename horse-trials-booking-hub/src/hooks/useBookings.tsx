import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Booking {
  id: string;
  event_id?: string;
  user_id?: string;
  time_slot_id?: string | null;
  activity_id?: string | null;
  participant_name: string;
  horse_name: string;
  email: string;
  phone?: string | null;
  notes?: string | null;
  payment_status: 'pending' | 'paid' | 'refunded' | 'failed';
  total_price: number;
  created_at: string;
  updated_at?: string;
  booking_code: string;
  paid_at?: string | null;
  payment_intent_id?: string | null;
  event_dressage_test_id?: string | null;
  level_id?: string | null;
  payer_name?: string | null;
  payer_email?: string | null;
  activity_description?: string | null;
  activity_date?: string | null;
}

export interface BookingFromView {
  booking_id: string;
  user_id: string;
  participant_name: string;
  horse_name: string;
  booking_code: string;
  activity_description?: string | null;
  payment_status: string;
  booking_created_at: string;
  booking_event_dressage_test_fk?: string | null;
  payer_name?: string | null;
  payer_email?: string | null;
  activity_date?: string | null;
  total_price?: number | null;
  stripe_payment_id?: string | null;

  time_slot_id?: string | null;
  time_slot_start_time?: string | null;
  time_slot_end_time?: string | null;
  time_slot_level?: string | null;
  time_slot_is_booked?: boolean | null;
  time_slot_activity_fk?: string | null;
  time_slot_location_fk?: string | null;

  location_id?: string | null;
  location_name?: string | null;
  location_event_id?: string | null;
  location_activity_type?: string | null;

  activity_id?: string | null;
  activity_specific_type?: string | null;
  activity_level?: string | null;
  activity_specific_description?: string | null;
  activity_slot_duration_minutes?: number | null;

  event_dressage_test_link_id?: string | null;
  dressage_test_id?: string | null;
  dressage_test_label?: string | null;
  dressage_test_description?: string | null;
  dressage_level_id?: string | null;
  dressage_level_name?: string | null;
  dressage_level_discipline?: string | null;
}

// New interface for the public entry list view
export interface PublicEntryFromView {
  booking_id: string;
  participant_name: string;
  horse_name: string;
  booking_code: string;
  activity_description?: string | null;
  time_slot_start_time?: string | null;
  time_slot_end_time?: string | null;
  time_slot_level?: string | null;
  location_name?: string | null;
  location_event_id?: string | null;
  activity_specific_type?: string | null;
  activity_level?: string | null;
  dressage_test_label?: string | null;
  dressage_level_name?: string | null;
}

export const useBookings = (eventId?: string) => {
  return useQuery<BookingFromView[], Error>({
    queryKey: ['bookings', eventId],
    queryFn: async () => {
      console.log(`[useBookings] Attempting to fetch bookings for eventId: ${eventId}`);
      let query = supabase.from('bookings_with_details_view').select('*');
      if (eventId) {
        query = query.eq('location_event_id', eventId);
        console.log(`[useBookings] Applied location_event_id filter: ${eventId}`);
      }
      const { data, error } = await query.order('booking_created_at', { ascending: false });
      if (error) {
        console.error(`[useBookings] Supabase error fetching bookings for eventId ${eventId}:`, error);
        throw new Error(error.message);
      }
      console.log(`[useBookings] Successfully fetched ${data?.length || 0} bookings for eventId ${eventId}. Data:`, data);
      return data || [];
    },
    enabled: !!eventId,
  });
};

// New hook for public entry list
export const usePublicEntries = (eventId?: string) => {
  return useQuery<PublicEntryFromView[], Error>({
    queryKey: ['public-entries', eventId],
    queryFn: async () => {
      console.log(`[usePublicEntries] Attempting to fetch public entries for eventId: ${eventId}`);
      let query = supabase.from('public_bookings_with_details_view').select('*');
      if (eventId) {
        query = query.eq('location_event_id', eventId);
        console.log(`[usePublicEntries] Applied location_event_id filter: ${eventId}`);
      }
      const { data, error } = await query.order('time_slot_start_time', { ascending: true });
      if (error) {
        console.error(`[usePublicEntries] Supabase error fetching public entries for eventId ${eventId}:`, error);
        throw new Error(error.message);
      }
      console.log(`[usePublicEntries] Successfully fetched ${data?.length || 0} public entries for eventId ${eventId}. Data:`, data);
      return data || [];
    },
    enabled: !!eventId,
  });
};

export const useCreateBooking = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (newBooking: {
      payer_name: string;
      payer_email: string;
      user_id: string | null;
      slots: Array<{
        time_slot_id: string;
        participant_name: string;
        horse_name: string;
        event_dressage_test_id?: string;
        activity_description?: string;
      }>;
    }) => {
      console.log('%c[BOOKING DEBUG] Starting booking creation', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;');
      console.log('%c[BOOKING DEBUG] Input data:', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', JSON.stringify(newBooking, null, 2));

      // First get the time slots to get their activity_ids
      const { data: timeSlots, error: timeSlotsError } = await supabase
        .from('time_slots')
        .select('id, activity_id')
        .in('id', newBooking.slots.map(slot => slot.time_slot_id));

      if (timeSlotsError) {
        console.error('%c[BOOKING DEBUG] Error fetching time slots:', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', timeSlotsError);
        throw new Error(`Failed to fetch time slots: ${timeSlotsError.message}`);
      }

      // Create a map of time slot IDs to activity IDs
      const timeSlotActivityMap = new Map(
        timeSlots?.map(ts => [ts.id, ts.activity_id]) || []
      );

      // Validate UUIDs before proceeding
      newBooking.slots.forEach((slot, index) => {
        console.log('%c[BOOKING DEBUG] Validating slot ' + (index + 1), 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', {
          time_slot_id: slot.time_slot_id,
          event_dressage_test_id: slot.event_dressage_test_id,
          activity_id: timeSlotActivityMap.get(slot.time_slot_id),
          is_valid_time_slot: isValidUUID(slot.time_slot_id),
          is_valid_test_id: slot.event_dressage_test_id ? isValidUUID(slot.event_dressage_test_id) : true,
          is_valid_activity_id: timeSlotActivityMap.get(slot.time_slot_id) ? isValidUUID(timeSlotActivityMap.get(slot.time_slot_id)!) : true
        });

        if (!slot.time_slot_id) {
          throw new Error(`Missing time_slot_id for slot ${index + 1}`);
        }
        if (!isValidUUID(slot.time_slot_id)) {
          throw new Error(`Invalid time_slot_id format for slot ${index + 1}: ${slot.time_slot_id}`);
        }
        if (slot.event_dressage_test_id && !isValidUUID(slot.event_dressage_test_id)) {
          throw new Error(`Invalid event_dressage_test_id format for slot ${index + 1}: ${slot.event_dressage_test_id}`);
        }
        const activityId = timeSlotActivityMap.get(slot.time_slot_id);
        if (activityId && !isValidUUID(activityId)) {
          throw new Error(`Invalid activity_id format for slot ${index + 1}: ${activityId}`);
        }
      });

      // First create the booking record
      const bookingInsert = {
        payer_name: newBooking.payer_name,
        payer_email: newBooking.payer_email,
        user_id: newBooking.user_id,
        payment_status: 'unpaid'
      };
      console.log('%c[BOOKING DEBUG] Creating booking record:', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', JSON.stringify(bookingInsert, null, 2));

      try {
        const { data: booking, error: bookingError } = await supabase
          .from('bookings')
          .insert([bookingInsert])
          .select()
          .single();

        if (bookingError) {
          console.error('%c[BOOKING DEBUG] Error creating booking:', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', bookingError);
          throw new Error(`Failed to create booking: ${bookingError.message}`);
        }

        if (!booking || !booking.booking_code) {
          throw new Error('Booking created but no booking_code returned');
        }

        console.log('%c[BOOKING DEBUG] Created booking:', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', JSON.stringify(booking, null, 2));

        // Then create the booking slots
        const bookingSlots = newBooking.slots.map(slot => {
          const slotData = {
            booking_code: booking.booking_code,
            time_slot_id: slot.time_slot_id,
            participant_name: slot.participant_name,
            horse_name: slot.horse_name,
            event_dressage_test_id: slot.event_dressage_test_id || null,
            activity_description: slot.activity_description || null,
            activity_id: timeSlotActivityMap.get(slot.time_slot_id) || null
          };
          console.log('%c[BOOKING DEBUG] Creating booking slot:', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', JSON.stringify(slotData, null, 2));
          return slotData;
        });

        console.log('%c[BOOKING DEBUG] Inserting all booking slots:', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', JSON.stringify(bookingSlots, null, 2));

        // Try inserting slots one at a time to identify which one fails
        for (let i = 0; i < bookingSlots.length; i++) {
          const slot = bookingSlots[i];
          console.log('%c[BOOKING DEBUG] Inserting slot ' + (i + 1), 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', JSON.stringify(slot, null, 2));
          
          const { error: slotError } = await supabase
            .from('booking_slots')
            .insert([slot]);

          if (slotError) {
            console.error('%c[BOOKING DEBUG] Error inserting slot ' + (i + 1), 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', slotError);
            throw new Error(`Failed to create booking slot ${i + 1}: ${slotError.message}`);
          }
        }

        console.log('%c[BOOKING DEBUG] Successfully created all booking slots', 'background: #00ff00; color: black; padding: 2px 5px; border-radius: 2px;');
        return booking;
      } catch (error) {
        console.error('%c[BOOKING DEBUG] Unexpected error in booking creation:', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', error);
        throw error;
      }
    },
    onSuccess: () => {
      console.log('%c[BOOKING DEBUG] Booking mutation succeeded', 'background: #00ff00; color: black; padding: 2px 5px; border-radius: 2px;');
      toast({ title: 'Success', description: 'Booking created successfully.' });
      queryClient.invalidateQueries({ queryKey: ['bookings'] });
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
    },
    onError: (error: Error) => {
      console.error('%c[BOOKING DEBUG] Booking mutation failed:', 'background: #ff0000; color: white; padding: 2px 5px; border-radius: 2px;', error);
      toast({ title: 'Error', description: `Failed to create booking: ${error.message}`, variant: 'destructive' });
    },
  });
};

// Helper function to validate UUID format
function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

export const useDeleteBooking = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (bookingId: string) => {
      const { data, error } = await supabase.functions.invoke('delete-booking', {
        body: { bookingId },
      });

      if (error) {
        console.error('Error calling delete-booking function:', error);
        throw new Error(error.message || 'Failed to delete booking via Edge Function.');
      }

      if (data && data.error) {
        console.error('Error from delete-booking function:', data.error);
        throw new Error(data.error.message || 'An error occurred in the booking deletion process.');
      }
      if (data && data.warning) {
        toast({ title: "Warning", description: data.warning, variant: "default" });
      }

      return bookingId;
    },
    onSuccess: () => {
      toast({ title: 'Success', description: 'Booking deleted successfully.' });
      queryClient.invalidateQueries({ queryKey: ['bookings'] });
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
    },
    onError: (error: Error) => {
      toast({ title: 'Error', description: `Failed to delete booking: ${error.message}`, variant: 'destructive' });
    },
  });
};

export const useUpdateBooking = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ bookingId, updates }: { 
      bookingId: string; 
      updates: {
        participant_name?: string;
        horse_name?: string;
        payer_name?: string;
        payer_email?: string;
        event_dressage_test_id?: string | null;
      }
    }) => {
      // First get the booking to get the booking_code
      const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .select('booking_code')
        .eq('id', bookingId)
        .single();

      if (bookingError) {
        throw new Error(`Failed to get booking: ${bookingError.message}`);
      }

      if (!booking || !booking.booking_code) {
        throw new Error('Booking not found or missing booking_code');
      }

      // Update the bookings table with payer info
      const { error: bookingUpdateError } = await supabase
        .from('bookings')
        .update({
          payer_name: updates.payer_name,
          payer_email: updates.payer_email
        })
        .eq('id', bookingId);

      if (bookingUpdateError) {
        throw new Error(`Failed to update booking: ${bookingUpdateError.message}`);
      }

      // Update the booking slots with participant and dressage test info
      const { error: slotError } = await supabase
        .from('booking_slots')
        .update({
          participant_name: updates.participant_name,
          horse_name: updates.horse_name,
          event_dressage_test_id: updates.event_dressage_test_id
        })
        .eq('booking_code', booking.booking_code);

      if (slotError) {
        throw new Error(`Failed to update booking slots: ${slotError.message}`);
      }

      return booking;
    },
    onSuccess: () => {
      toast({ title: 'Success', description: 'Booking updated successfully.' });
      queryClient.invalidateQueries({ queryKey: ['bookings'] });
    },
    onError: (error: Error) => {
      toast({ title: 'Error', description: `Failed to update booking: ${error.message}`, variant: 'destructive' });
    },
  });
};
