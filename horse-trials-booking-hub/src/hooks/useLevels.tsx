import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Level {
  id: string;
  name: string;
  discipline: string;
  description?: string;
  sort_order?: number;
  is_active?: boolean;
}

export interface EventLevel {
  id: string;
  event_id: string;
  level_id: string;
  is_enabled?: boolean;
  levels?: Level;
}

export const useLevels = (discipline?: string) => {
  return useQuery({
    queryKey: ['levels', discipline],
    queryFn: async () => {
      
      let query = supabase
        .from('levels_library')
        .select('*')
        .eq('is_active', true);
      
      if (discipline && discipline !== '' && discipline !== 'undefined') {
        query = query.eq('discipline', discipline);
      }
      
      const { data, error } = await query.order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching levels:', error);
        throw error;
      }

      return data as Level[];
    },
  });
};

export const useEventLevels = (eventId?: string) => {
  return useQuery({
    queryKey: ['event-levels', eventId],
    queryFn: async () => {
      if (!eventId) return [];
      console.log('eventId', eventId);
    
      // Use a simpler query that doesn't rely on foreign key names
      const { data, error } = await supabase
        .from('event_levels')
        .select(`
          id,
          event_id,
          level_id,
          is_enabled
        `)
        .eq('event_id', eventId)
        .eq('is_enabled', true);

      if (error) {
        console.error('Error fetching event levels:', error);
        throw error;
      }

      // Now fetch the level details separately
      if (data && data.length > 0) {
        const levelIds = data.map(item => item.level_id).filter(Boolean);
        
        if (levelIds.length > 0) {
          const { data: levelData, error: levelError } = await supabase
            .from('levels_library')
            .select('*')
            .in('id', levelIds);

          if (levelError) {
            console.error('Error fetching level details:', levelError);
            throw levelError;
          }

          // Combine the data
          const transformedData = data.map(eventLevel => {
            const levelDetails = levelData?.find(level => level.id === eventLevel.level_id);
            return {
              ...eventLevel,
              levels: levelDetails
            };
          });

          console.log('transformedData', transformedData);
          return transformedData as EventLevel[];
        }
      }

      return [] as EventLevel[];
    },
    enabled: !!eventId,
  });
};

export const useCreateEventLevel = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (eventLevelData: Omit<EventLevel, 'id' | 'levels'>) => {
      const { data, error } = await supabase
        .from('event_levels')
        .insert([eventLevelData])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['event-levels'] });
      toast({
        title: "Success",
        description: "Level added to event successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteEventLevel = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('event_levels')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['event-levels'] });
      toast({
        title: "Success",
        description: "Level removed from event",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};
