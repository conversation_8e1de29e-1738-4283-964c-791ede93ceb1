import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// This is the structure LocationScheduleView expects
export interface ScheduleViewTimeSlot {
  id: string;
  time: string; // e.g., "09:00 AM"
  status: 'available' | 'booked' | 'unavailable' | 'pending';
  riderName?: string;
  horseName?: string;
  bookingId?: string;
}

// Assumed structure for your Supabase 'time_slots' table (templates/definitions)
interface DbTimeSlot {
  id: string;
  location_id: string;
  start_time: string; // e.g., "09:00:00"
  end_time: string;   // e.g., "10:00:00"
  activity_id?: string | null;
  is_bookable: boolean;
  // Add other relevant fields like max_capacity, day_of_week, etc.
}

// Assumed structure for your Supabase 'bookings' table
interface DbBooking {
  id: string;
  time_slot_id: string;
  booking_date: string; // e.g., "2024-07-28"
  user_id: string;
  rider_name?: string | null;
  horse_name?: string | null;
  status: string; // e.g., "confirmed", "pending", "cancelled"
  location_id: string;
}

const TIME_SLOTS_FOR_DATE_QUERY_KEY = 'timeSlotsForDate';

// Helper to format time from "HH:MM:SS" to "HH:MM AM/PM"
const formatTime = (timeStr: string): string => {
  const [hours, minutes] = timeStr.split(':');
  const h = parseInt(hours, 10);
  const ampm = h >= 12 ? 'PM' : 'AM';
  const formattedHours = h % 12 || 12; // Convert 0 to 12 for 12 AM, and 12 to 12 for 12 PM
  return `${String(formattedHours).padStart(2, '0')}:${minutes} ${ampm}`;
};

const fetchTimeSlotsForDate = async (
  locationId: string,
  date: string // YYYY-MM-DD format
): Promise<ScheduleViewTimeSlot[]> => {
  console.log(`[useTimeSlotsForDate] Fetching real data for locationId: ${locationId}, date: ${date}`);

  // 1. Fetch all time slot definitions for the location
  const { data: timeSlotDefinitions, error: slotsError } = await supabase
    .from('time_slots') // Make sure 'time_slots' is your actual table name
    .select('*')
    .eq('location_id', locationId)
    .order('start_time', { ascending: true });

  if (slotsError) {
    console.error('Error fetching time slot definitions:', slotsError);
    throw slotsError;
  }
  if (!timeSlotDefinitions) return [];

  // 2. Fetch all bookings for this location on the specified date
  const { data: bookings, error: bookingsError } = await supabase
    .from('bookings') // Make sure 'bookings' is your actual table name
    .select('*')
    .eq('location_id', locationId)
    .eq('booking_date', date); // Filter by the specific date

  if (bookingsError) {
    console.error('Error fetching bookings:', bookingsError);
    throw bookingsError;
  }

  // 3. Combine definitions and bookings to create the schedule view
  const scheduleSlots = (timeSlotDefinitions as DbTimeSlot[]).map((slotDef): ScheduleViewTimeSlot => {
    const booking = (bookings as DbBooking[] | null)?.find(
      (b) => b.time_slot_id === slotDef.id && b.status !== 'cancelled' // Ensure booking is for this slot def and not cancelled
    );

    let status: ScheduleViewTimeSlot['status'] = 'available';
    if (!slotDef.is_bookable) {
      status = 'unavailable';
    } else if (booking) {
      // You might need more nuanced status mapping based on your booking.status values
      status = booking.status === 'confirmed' ? 'booked' : booking.status === 'pending' ? 'pending' : 'booked';
    }

    return {
      id: `${slotDef.id}-${date}`, // Create a unique ID for the slot instance on this date for UI keys
      time: formatTime(slotDef.start_time),
      status: status,
      riderName: booking?.rider_name || undefined,
      horseName: booking?.horse_name || undefined,
      bookingId: booking?.id || undefined,
    };
  });

  return scheduleSlots;
};

export const useTimeSlotsForDate = (locationId: string | undefined, date: string | undefined) => {
  return useQuery<ScheduleViewTimeSlot[], Error>({
    queryKey: [TIME_SLOTS_FOR_DATE_QUERY_KEY, locationId, date],
    queryFn: async () => {
      if (!locationId || !date) return [];
      return fetchTimeSlotsForDate(locationId, date);
    },
    enabled: !!locationId && !!date,
    staleTime: 1000 * 60 * 1, // Cache for 1 minute, adjust as needed
  });
};