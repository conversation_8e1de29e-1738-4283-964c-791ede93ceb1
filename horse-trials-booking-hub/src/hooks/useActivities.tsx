import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { getErrorMessage } from '@/types/errors';

export interface Activity {
  id: string;
  location_id: string; // Changed from arena_id
  activity_type: string;
  start_time: string;
  end_time: string;
  level?: string;
  description?: string;
  slot_duration_minutes?: number;
  created_at?: string;
}

export const useActivities = (locationId?: string) => { // Changed parameter name
  return useQuery({
    queryKey: ['activities', locationId],
    queryFn: async () => {
      let query = supabase.from('activities').select('*');
      
      if (locationId) {
        query = query.eq('location_id', locationId); // Changed to location_id
      }
      
      const { data, error } = await query.order('start_time');

      if (error) {
        console.error('Error fetching activities:', error);
        throw error;
      }

      // Log specifically to check if slot_duration_minutes is coming from the database
      if (data && data.length > 0) {
        console.log('Sample activity slot duration:', data[0].slot_duration_minutes);
      }
      
      return data as Activity[];
    },
    enabled: !!locationId, // Use locationId for enabled check
  });
};

export const useCreateActivity = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (activityData: Omit<Activity, 'id' | 'created_at'>) => {
      const { data, error } = await supabase
        .from('activities')
        .insert([activityData])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['activities'] });
      toast({
        title: "Success",
        description: "Activity created successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};

export const useUpdateActivity = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...activityData }: Partial<Activity> & { id: string }) => {
      const { data, error } = await supabase
        .from('activities')
        .update(activityData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['activities'] });
      toast({
        title: "Success",
        description: "Activity updated successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};

export const useDeleteActivity = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (activityId: string) => {
      const { error } = await supabase
        .from('activities')
        .delete()
        .eq('id', activityId);

      if (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['activities'] });
      queryClient.invalidateQueries({ queryKey: ['time_slots'] });
      toast({
        title: "Success",
        description: "Activity deleted successfully",
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });
};
