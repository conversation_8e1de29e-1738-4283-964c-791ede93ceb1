import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useActivities, useCreateActivity, useUpdateActivity, useDeleteActivity } from './useActivities';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useQueryClient: vi.fn(),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(),
        })),
        order: vi.fn(),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockUseQuery = vi.mocked(useQuery);
const mockUseMutation = vi.mocked(useMutation);
const mockUseQueryClient = vi.mocked(useQueryClient);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);

describe('useActivities', () => {
  const mockQueryClient = {
    invalidateQueries: vi.fn(),
  };

  const mockToast = {
    toast: vi.fn(),
  };

  const mockActivities = [
    {
      id: 'activity-1',
      location_id: 'location-1',
      activity_type: 'dressage',
      start_time: '2024-01-15T09:00:00Z',
      end_time: '2024-01-15T10:00:00Z',
      level: 'Intro',
      description: 'Basic dressage test',
      slot_duration_minutes: 60,
      created_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'activity-2',
      location_id: 'location-1',
      activity_type: 'show_jumping',
      start_time: '2024-01-15T10:15:00Z',
      end_time: '2024-01-15T11:15:00Z',
      level: 'Prelim',
      description: 'Show jumping course',
      slot_duration_minutes: 60,
      created_at: '2024-01-01T00:00:00Z',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseQueryClient.mockReturnValue(mockQueryClient);
    mockUseToast.mockReturnValue(mockToast);
  });

  describe('useActivities', () => {
    it('fetches activities for a specific location', async () => {
      mockUseQuery.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useActivities('location-1'));

      expect(result.current.data).toEqual(mockActivities);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('fetches all activities when no locationId provided', () => {
      mockUseQuery.mockReturnValue({
        data: mockActivities,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useActivities());

      expect(result.current.data).toEqual(mockActivities);
    });

    it('handles loading state', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useActivities('location-1'));

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
    });

    it('handles error state', () => {
      const mockError = new Error('Failed to fetch activities');
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useActivities('location-1'));

      expect(result.current.error).toBe(mockError);
      expect(result.current.isLoading).toBe(false);
    });

    it('is disabled when no locationId provided', () => {
      mockUseQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      } as any);

      renderHook(() => useActivities());

      expect(mockUseQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false,
        })
      );
    });
  });

  describe('useCreateActivity', () => {
    it('creates activity successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'activity-3',
        location_id: 'location-1',
        activity_type: 'cross_country',
        start_time: '2024-01-15T12:00:00Z',
        end_time: '2024-01-15T13:00:00Z',
        level: 'Intro',
        description: 'Cross country course',
        slot_duration_minutes: 60,
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateActivity());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during creation', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useCreateActivity());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during creation', () => {
      const mockError = new Error('Failed to create activity');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useCreateActivity());

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useUpdateActivity', () => {
    it('updates activity successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: 'activity-1',
        location_id: 'location-1',
        activity_type: 'dressage',
        start_time: '2024-01-15T09:00:00Z',
        end_time: '2024-01-15T10:00:00Z',
        level: 'Prelim', // Updated level
        description: 'Updated dressage test',
        slot_duration_minutes: 60,
      });
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateActivity());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during update', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useUpdateActivity());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during update', () => {
      const mockError = new Error('Failed to update activity');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useUpdateActivity());

      expect(result.current.error).toBe(mockError);
    });
  });

  describe('useDeleteActivity', () => {
    it('deletes activity successfully', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue(undefined);
      const mockMutate = vi.fn();

      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutationFn,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteActivity());

      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles loading state during deletion', () => {
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: true,
        error: null,
      } as any);

      const { result } = renderHook(() => useDeleteActivity());

      expect(result.current.isLoading).toBe(true);
    });

    it('handles error state during deletion', () => {
      const mockError = new Error('Failed to delete activity');
      mockUseMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: vi.fn(),
        isLoading: false,
        error: mockError,
      } as any);

      const { result } = renderHook(() => useDeleteActivity());

      expect(result.current.error).toBe(mockError);
    });
  });
}); 