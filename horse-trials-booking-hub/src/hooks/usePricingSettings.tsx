import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface PricingSettings {
  dressage_price: number;
  show_jumping_price: number;
  cross_country_price: number;
  currency_symbol: string;
  cart_retention_minutes: number;
}

// Default pricing settings
export const defaultPricingSettings: PricingSettings = {
  dressage_price: 30,
  show_jumping_price: 25,
  cross_country_price: 40,
  currency_symbol: '$',
  cart_retention_minutes: 15
};

export const usePricingSettings = () => {
  return useQuery({
    queryKey: ['pricing_settings'],
    queryFn: async () => {
      try {
        
        const { data, error } = await supabase
          .from('app_settings')
          .select('value')
          .eq('key', 'pricing_settings')
          .single();

        if (error) {
          console.error('Error fetching pricing settings:', error);
          
          if (error.code === 'PGRST116') {
            // No settings found, return defaults
            console.log('No settings found, using defaults');
            return defaultPricingSettings;
          }
          
          throw error;
        }

        
        // Parse the JSON value from the database
        let jsonValue: any;
        try {
          // If it's already an object (JSONB), use it directly
          if (typeof data.value === 'object' && data.value !== null) {
            jsonValue = data.value;
          } else {
            // If it's a string, parse it as JSON
            jsonValue = JSON.parse(data.value as string);
          }
        } catch (parseError) {
          console.error('Error parsing pricing settings JSON:', parseError);
          console.log('Raw value from database:', data.value);
          return defaultPricingSettings;
        }

        // Validate that it's an object
        if (typeof jsonValue === 'object' && jsonValue !== null && !Array.isArray(jsonValue)) {
          const settings = jsonValue as Record<string, unknown>;
          
          // Validate and convert each field
          const validatedSettings: PricingSettings = {
            dressage_price: typeof settings.dressage_price === 'number' ? settings.dressage_price : defaultPricingSettings.dressage_price,
            show_jumping_price: typeof settings.show_jumping_price === 'number' ? settings.show_jumping_price : defaultPricingSettings.show_jumping_price,
            cross_country_price: typeof settings.cross_country_price === 'number' ? settings.cross_country_price : defaultPricingSettings.cross_country_price,
            currency_symbol: typeof settings.currency_symbol === 'string' ? settings.currency_symbol : defaultPricingSettings.currency_symbol,
            cart_retention_minutes: typeof settings.cart_retention_minutes === 'number' ? settings.cart_retention_minutes : defaultPricingSettings.cart_retention_minutes
          };
          
          return validatedSettings;
        }
        
        // If we can't parse the JSON, return defaults
        console.warn('Invalid pricing settings format, using defaults');
        console.log('Parsed value:', jsonValue);
        return defaultPricingSettings;
      } catch (error) {
        console.error('Error in pricing settings query:', error);
        return defaultPricingSettings;
      }
    },
  });
};

export const useUpdatePricingSettings = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (settings: PricingSettings) => {
      
      // Validate the settings
      const dressage_price = Number(settings.dressage_price) || 0;
      const show_jumping_price = Number(settings.show_jumping_price) || 0;
      const cross_country_price = Number(settings.cross_country_price) || 0;
      const currency_symbol = settings.currency_symbol || '$';
      const cart_retention_minutes = Number(settings.cart_retention_minutes) || 15;
      
      try {
        // Try using the RPC function that bypasses RLS
        const { data, error } = await supabase.rpc('update_pricing_settings', {
          p_dressage_price: dressage_price,
          p_show_jumping_price: show_jumping_price,
          p_cross_country_price: cross_country_price,
          p_currency_symbol: currency_symbol,
          p_cart_retention_minutes: cart_retention_minutes
        });
        
        if (error) {
          console.error('Error calling update_pricing_settings function:', error);
          throw error;
        }
        
        return data;
      } catch (error: any) {
        console.error('Error in mutation:', error);
        
        // Fall back to direct update if RPC fails
        try {
          console.log('Falling back to direct update');
          
          const validatedSettings = {
            dressage_price,
            show_jumping_price,
            cross_country_price,
            currency_symbol,
            cart_retention_minutes
          };
          
          const { data, error: updateError } = await supabase
            .from('app_settings')
            .update({ value: validatedSettings })
            .eq('key', 'pricing_settings')
            .select();
            
          if (updateError) throw updateError;
          
          console.log('Settings updated successfully via direct update:', data);
          return data;
        } catch (fallbackError: any) {
          console.error('Fallback update also failed:', fallbackError);
          throw new Error(`Failed to update settings: ${fallbackError.message}`);
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pricing_settings'] });
      toast({
        title: "Success",
        description: "Pricing settings updated successfully",
      });
    },
    onError: (error: any) => {
      console.error('Mutation error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update pricing settings",
        variant: "destructive",
      });
    },
  });
};

export const getCartRetentionMinutes = (pricingSettings?: PricingSettings) => {
  return pricingSettings?.cart_retention_minutes || defaultPricingSettings.cart_retention_minutes;
};

export const getActivityPrice = (activityType: string, pricingSettings?: PricingSettings) => {
  if (!pricingSettings) return defaultPricingSettings.dressage_price; // Default fallback
  
  switch (activityType) {
    case 'dressage':
      return pricingSettings.dressage_price;
    case 'show_jumping':
      return pricingSettings.show_jumping_price;
    case 'cross_country':
      return pricingSettings.cross_country_price;
    default:
      return defaultPricingSettings.dressage_price; // Default fallback
  }
};
