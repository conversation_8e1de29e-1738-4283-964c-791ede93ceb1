import { usePricingSettings } from './usePricingSettings';

// A central hook to access all application settings
export const useAppSettings = () => {
  const { data: pricingSettings, isLoading: isPricingLoading } = usePricingSettings();
  
  return {
    // Pricing settings
    pricingSettings,
    isPricingLoading,
    
    // Helper functions
    getActivityPrice: (activityType: string) => {
      if (!pricingSettings) return 30; // Default fallback
      
      switch (activityType) {
        case 'dressage':
          return pricingSettings.dressage_price;
        case 'show_jumping':
          return pricingSettings.show_jumping_price;
        case 'cross_country':
          return pricingSettings.cross_country_price;
        default:
          return 30; // Default fallback
      }
    },
    
    getCartRetentionMinutes: () => pricingSettings?.cart_retention_minutes || 10,
    getCurrencySymbol: () => pricingSettings?.currency_symbol || '$',
  };
};