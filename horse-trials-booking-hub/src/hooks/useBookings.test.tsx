import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useUpdateBooking } from './useBookings';
import { supabase } from '../integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Mock Supabase
vi.mock('../integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
    functions: {
      invoke: vi.fn(),
    },
  },
}));

// Mock toast
vi.mock('../../hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

interface MockSupabaseResponse {
  data: unknown;
  error: { message: string } | null;
}

interface MockSupabaseQuery {
  select: () => MockSupabaseQuery;
  eq: () => MockSupabaseQuery;
  single: () => Promise<MockSupabaseResponse>;
  update: () => MockSupabaseQuery;
}

type MockSupabaseTable = Partial<{
  select: () => MockSupabaseQuery;
  eq: () => MockSupabaseQuery;
  single: () => Promise<MockSupabaseResponse>;
  update: () => MockSupabaseQuery;
}>;

describe('useUpdateBooking', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    vi.clearAllMocks();
    supabase.from = vi.fn((table) => {
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({ data: { booking_code: 'default-code' }, error: null }),
            }),
          }),
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      if (table === 'booking_slots') {
        return {
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      return {};
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  it('should update booking and booking slots correctly', async () => {
    // Mock the booking lookup
    const mockBooking = { booking_code: 'test-booking-code' };
    supabase.from.mockImplementation((table) => {
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({ data: mockBooking, error: null }),
            }),
          }),
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      if (table === 'booking_slots') {
        return {
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      return {};
    });

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-booking-id',
      updates: {
        participant_name: 'John Doe',
        horse_name: 'Thunder',
        payer_name: 'John Doe',
        payer_email: '<EMAIL>',
        event_dressage_test_id: 'test-dressage-id',
      },
    };

    await result.current.mutateAsync(updateData);

    // Verify booking lookup was called
    expect(supabase.from).toHaveBeenCalledWith('bookings');
    // Verify booking update was called with correct fields
    expect(supabase.from).toHaveBeenCalledWith('bookings');
    // Verify booking slots update was called with correct fields
    expect(supabase.from).toHaveBeenCalledWith('booking_slots');
  });

  it('should handle booking not found error', async () => {
    supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: null, error: null }),
        }),
      }),
    });

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'non-existent-id',
      updates: {
        participant_name: 'John Doe',
        horse_name: 'Thunder',
      },
    };

    await expect(result.current.mutateAsync(updateData)).rejects.toThrow(
      'Booking not found or missing booking_code'
    );
  });

  it('should handle booking lookup error', async () => {
    supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ 
            data: null, 
            error: { message: 'Database error' } 
          }),
        }),
      }),
    });

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        participant_name: 'John Doe',
      },
    };

    await expect(result.current.mutateAsync(updateData)).rejects.toThrow(
      'Failed to get booking: Database error'
    );
  });

  it('should handle booking update error', async () => {
    // Mock successful booking lookup, then booking update error
    supabase.from.mockImplementation((table) => {
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({ data: { booking_code: 'test-booking-code' }, error: null }),
            }),
          }),
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: { message: 'Update failed' } }),
          }),
        };
      }
      if (table === 'booking_slots') {
        return {
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      return {};
    });

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        payer_name: 'John Doe',
        payer_email: '<EMAIL>',
      },
    };

    await expect(result.current.mutateAsync(updateData)).rejects.toThrow(
      'Failed to update booking: Update failed'
    );
  });

  it('should handle booking slots update error', async () => {
    // Mock successful booking lookup and booking update, then booking slots update error
    supabase.from.mockImplementation((table) => {
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({ data: { booking_code: 'test-booking-code' }, error: null }),
            }),
          }),
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      if (table === 'booking_slots') {
        return {
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: { message: 'Slots update failed' } }),
          }),
        };
      }
      return {};
    });

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        participant_name: 'John Doe',
        horse_name: 'Thunder',
      },
    };

    await waitFor(async () => {
      await expect(result.current.mutateAsync(updateData)).rejects.toThrow(
        'Failed to update booking slots: Slots update failed'
      );
    });
  });

  it('should handle null dressage test ID', async () => {
    supabase.from.mockImplementation((table) => {
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({ data: { booking_code: 'test-booking-code' }, error: null }),
            }),
          }),
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      if (table === 'booking_slots') {
        return {
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      return {};
    });

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        participant_name: 'John Doe',
        horse_name: 'Thunder',
      },
    };

    await expect(result.current.mutateAsync(updateData)).resolves.not.toThrow();
  });

  it('should handle partial updates', async () => {
    supabase.from.mockImplementation((table) => {
      if (table === 'bookings') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({ data: { booking_code: 'test-booking-code' }, error: null }),
            }),
          }),
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      if (table === 'booking_slots') {
        return {
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({ error: null }),
          }),
        };
      }
      return {};
    });

    const { result } = renderHook(() => useUpdateBooking(), { wrapper });

    const updateData = {
      bookingId: 'test-id',
      updates: {
        participant_name: 'Jane Doe',
      },
    };

    await expect(result.current.mutateAsync(updateData)).resolves.not.toThrow();
  });
}); 