import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { 
  useOrganizers, 
  useCreateOrganizer, 
  useUpdateOrganizer, 
  useDeleteOrganizer,
  type Organizer 
} from './useOrganizers';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
  },
}));

// Mock useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(),
}));

const mockSupabase = vi.mocked(await import('@/integrations/supabase/client')).supabase;
const mockUseToast = vi.mocked(await import('@/hooks/use-toast')).useToast;

// Test data
const mockOrganizer: Organizer = {
  id: 'org-1',
  full_name: '<PERSON>',
  email: '<EMAIL>',
  company_name: 'Doe <PERSON>',
  business_name: '<PERSON>e Event Management',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockOrganizers: Organizer[] = [
  mockOrganizer,
  {
    id: 'org-2',
    full_name: 'Jane Smith',
    email: '<EMAIL>',
    company_name: 'Smith Productions',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z'
  }
];

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useOrganizers', () => {
  const mockToast = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseToast.mockReturnValue({ toast: mockToast });
    
    // Mock Supabase chain
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        order: vi.fn().mockResolvedValue({
          data: mockOrganizers,
          error: null,
        }),
      }),
      insert: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockOrganizer,
            error: null,
          }),
        }),
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockOrganizer,
              error: null,
            }),
          }),
        }),
      }),
      delete: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
      }),
    } as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('useOrganizers', () => {
    it('should fetch organizers successfully', async () => {
      const { result } = renderHook(() => useOrganizers(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockOrganizers);
      expect(mockSupabase.from).toHaveBeenCalledWith('organizers');
    });

    it('should handle error when fetching organizers fails', async () => {
      const error = new Error('Database error');
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          order: vi.fn().mockResolvedValue({
            data: null,
            error,
          }),
        }),
      } as any);

      const { result } = renderHook(() => useOrganizers(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBe(error);
    });

    it('should have correct query configuration', () => {
      const { result } = renderHook(() => useOrganizers(), {
        wrapper: createWrapper(),
      });

      expect(result.current.data).toBeUndefined();
      expect(result.current.isLoading).toBe(true);
    });
  });

  describe('useCreateOrganizer', () => {
    it('should create organizer successfully', async () => {
      const newOrganizer = {
        full_name: 'New Organizer',
        email: '<EMAIL>',
        company_name: 'New Company'
      };

      const { result } = renderHook(() => useCreateOrganizer(), {
        wrapper: createWrapper(),
      });

      result.current.mutate(newOrganizer);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('organizers');
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Organizer created successfully",
      });
    });

    it('should handle error when creating organizer fails', async () => {
      const error = new Error('Creation failed');
      mockSupabase.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error,
            }),
          }),
        }),
      } as any);

      const { result } = renderHook(() => useCreateOrganizer(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        full_name: 'Test Organizer',
        email: '<EMAIL>'
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBe(error);
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Creation failed",
        variant: "destructive",
      });
    });

    it('should invalidate organizers query on success', async () => {
      const newOrganizer = {
        full_name: 'Test Organizer',
        email: '<EMAIL>'
      };

      const { result } = renderHook(() => useCreateOrganizer(), {
        wrapper: createWrapper(),
      });

      result.current.mutate(newOrganizer);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Check that the query was invalidated
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Organizer created successfully",
      });
    });
  });

  describe('useUpdateOrganizer', () => {
    it('should update organizer successfully', async () => {
      const updateData = {
        id: 'org-1',
        full_name: 'Updated Name',
        email: '<EMAIL>'
      };

      const { result } = renderHook(() => useUpdateOrganizer(), {
        wrapper: createWrapper(),
      });

      result.current.mutate(updateData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('organizers');
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Organizer updated successfully",
      });
    });

    it('should handle error when updating organizer fails', async () => {
      const error = new Error('Update failed');
      mockSupabase.from.mockReturnValue({
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: null,
                error,
              }),
            }),
          }),
        }),
      } as any);

      const { result } = renderHook(() => useUpdateOrganizer(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        id: 'org-1',
        full_name: 'Updated Name'
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBe(error);
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Update failed",
        variant: "destructive",
      });
    });

    it('should invalidate organizers query on success', async () => {
      const updateData = {
        id: 'org-1',
        full_name: 'Updated Name'
      };

      const { result } = renderHook(() => useUpdateOrganizer(), {
        wrapper: createWrapper(),
      });

      result.current.mutate(updateData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Organizer updated successfully",
      });
    });
  });

  describe('useDeleteOrganizer', () => {
    it('should delete organizer successfully', async () => {
      const { result } = renderHook(() => useDeleteOrganizer(), {
        wrapper: createWrapper(),
      });

      result.current.mutate('org-1');

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('organizers');
      expect(result.current.data).toBe('org-1');
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Organizer deleted successfully",
      });
    });

    it('should handle error when deleting organizer fails', async () => {
      const error = new Error('Delete failed');
      mockSupabase.from.mockReturnValue({
        delete: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({
            data: null,
            error,
          }),
        }),
      } as any);

      const { result } = renderHook(() => useDeleteOrganizer(), {
        wrapper: createWrapper(),
      });

      result.current.mutate('org-1');

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBe(error);
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Delete failed",
        variant: "destructive",
      });
    });

    it('should invalidate organizers query on success', async () => {
      const { result } = renderHook(() => useDeleteOrganizer(), {
        wrapper: createWrapper(),
      });

      result.current.mutate('org-1');

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Organizer deleted successfully",
      });
    });
  });
}); 