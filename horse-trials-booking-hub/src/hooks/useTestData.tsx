import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export const useCreateTestData = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      console.log('Creating test data...');
      
      // Check if user is authenticated
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('You must be logged in to create test data');
      }

      // Create test events
      const { data: events, error: eventsError } = await supabase
        .from('events')
        .insert([
          {
            name: 'Spring Horse Trials 2024',
            start_date: '2024-06-15',
            end_date: '2024-06-16',
            schooling_start_date: '2024-06-10',
            schooling_end_date: '2024-06-14',
            created_by: user.id
          },
          {
            name: 'Summer Dressage Championship',
            start_date: '2024-07-20',
            end_date: '2024-07-22',
            schooling_start_date: '2024-07-15',
            schooling_end_date: '2024-07-19',
            created_by: user.id
          },
          {
            name: 'Autumn Cross Country Event',
            start_date: '2024-09-12',
            end_date: '2024-09-14',
            schooling_start_date: '2024-09-07',
            schooling_end_date: '2024-09-11',
            created_by: user.id
          }
        ])
        .select();

      if (eventsError) {
        console.error('Error creating events:', eventsError);
        throw eventsError;
      }

      console.log('Events created:', events);

      // Create test arenas for each event
      const arenaData = [];
      events.forEach(event => {
        arenaData.push(
          {
            name: 'Dressage Arena A',
            activity_type: 'dressage',
            start_time: `${event.schooling_start_date}T08:00:00`,
            end_time: `${event.schooling_start_date}T17:00:00`,
            slot_duration_minutes: 30,
            event_id: event.id,
            created_by: user.id
          },
          {
            name: 'Show Jumping Arena',
            activity_type: 'show_jumping',
            start_time: `${event.schooling_start_date}T09:00:00`,
            end_time: `${event.schooling_start_date}T16:00:00`,
            slot_duration_minutes: 45,
            fixed_level: 'Novice',
            event_id: event.id,
            created_by: user.id
          },
          {
            name: 'Dressage Arena B',
            activity_type: 'dressage',
            start_time: `${event.schooling_start_date}T08:30:00`,
            end_time: `${event.schooling_start_date}T17:30:00`,
            slot_duration_minutes: 30,
            event_id: event.id,
            created_by: user.id
          },
          {
            name: 'Cross Country Arena',
            activity_type: 'show_jumping',
            start_time: `${event.schooling_start_date}T10:00:00`,
            end_time: `${event.schooling_start_date}T15:00:00`,
            slot_duration_minutes: 60,
            fixed_level: 'Preliminary',
            event_id: event.id,
            created_by: user.id
          }
        );
      });

      const { data: arenas, error: arenasError } = await supabase
        .from('arenas')
        .insert(arenaData)
        .select();

      if (arenasError) {
        console.error('Error creating arenas:', arenasError);
        throw arenasError;
      }

      console.log('Arenas created:', arenas);

      // Create some test bookings
      const bookingData = [
        {
          participant_name: 'Sarah Johnson',
          horse_name: 'Thunder Bay',
          email: '<EMAIL>',
          activity_description: 'Dressage Training Level',
          payment_status: 'paid',
          user_id: user.id
        },
        {
          participant_name: 'Michael Chen',
          horse_name: 'Starlight',
          email: '<EMAIL>',
          activity_description: 'Show Jumping Novice',
          payment_status: 'unpaid',
          user_id: user.id
        },
        {
          participant_name: 'Emma Wilson',
          horse_name: 'Midnight Shadow',
          email: '<EMAIL>',
          activity_description: 'Dressage First Level',
          payment_status: 'paid',
          user_id: user.id
        },
        {
          participant_name: 'David Rodriguez',
          horse_name: 'Golden Spirit',
          email: '<EMAIL>',
          activity_description: 'Cross Country Preliminary',
          payment_status: 'paid',
          user_id: user.id
        },
        {
          participant_name: 'Lisa Thompson',
          horse_name: 'Silver Dream',
          email: '<EMAIL>',
          activity_description: 'Show Jumping Training',
          payment_status: 'unpaid',
          user_id: user.id
        }
      ];

      const { error: bookingsError } = await supabase
        .from('bookings')
        .insert(bookingData);

      if (bookingsError) {
        console.error('Error creating bookings:', bookingsError);
        throw bookingsError;
      }

      console.log('Bookings created');

      // Create some test time slots for the first few arenas
      const timeSlotData = [];
      const sampleArenas = arenas.slice(0, 3); // Just use first 3 arenas
      
      sampleArenas.forEach(arena => {
        // Create 8 time slots per arena for testing
        for (let i = 0; i < 8; i++) {
          const startHour = 8 + i; // Start from 8 AM
          const startTime = `2024-06-10T${startHour.toString().padStart(2, '0')}:00:00`;
          const endTime = `2024-06-10T${(startHour + 1).toString().padStart(2, '0')}:00:00`;
          
          timeSlotData.push({
            arena_id: arena.id,
            start_time: startTime,
            end_time: endTime,
            is_booked: Math.random() < 0.3 // 30% chance of being booked
          });
        }
      });

      const { error: timeSlotsError } = await supabase
        .from('time_slots')
        .insert(timeSlotData);

      if (timeSlotsError) {
        console.error('Error creating time slots:', timeSlotsError);
        throw timeSlotsError;
      }

      console.log('Time slots created');

      return { events, arenas };
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Test data created successfully! The events should now appear below.",
      });
      // Refresh the page to show the new data
      window.location.reload();
    },
    onError: (error: any) => {
      console.error('Test data creation error:', error);
      toast({
        title: "Error",
        description: error.message || 'Failed to create test data. Please try again.',
        variant: "destructive",
      });
    },
  });
};
