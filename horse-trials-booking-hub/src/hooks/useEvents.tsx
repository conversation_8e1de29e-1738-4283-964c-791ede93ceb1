import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Event {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  schooling_start_date: string;
  schooling_end_date: string;
  event_type: 'dressage' | 'horse_trials' | 'hunter_jumper';
  created_by?: string;
  created_at?: string;
  is_active?: boolean;
}

export const useEvents = (onlyActive = false) => {
  return useQuery({
    queryKey: ['events'],
    queryFn: async () => {
      let query = supabase
        .from('events')
        .select('*')
        .order('start_date', { ascending: true });

      if (onlyActive) {
        query = query.eq('is_active', true);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return data as Event[];
    },
  });
};

export const useEvent = (eventId?: string) => {
  return useQuery({
    queryKey: ['event', eventId],
    queryFn: async () => {
      if (!eventId) return null;

      const { data, error } = await supabase
        .from('events')
        .select('*')
        .eq('id', eventId)
        .single();

      if (error) {
        throw error;
      }

      return data as Event;
    },
    enabled: !!eventId,
  });
};

export const useCreateEvent = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (eventData: {
      event: Omit<Event, 'id' | 'created_at'>;
      levelIds: string[];
    }) => {
      // First create the event
      const { data: event, error: eventError } = await supabase
        .from('events')
        .insert([eventData.event])
        .select()
        .single();

      if (eventError) {
        throw eventError;
      }

      // Then create the event levels if any are selected
      if (eventData.levelIds && eventData.levelIds.length > 0) {
        const eventLevels = eventData.levelIds.map(levelId => ({
          event_id: event.id,
          level_id: levelId,
          is_enabled: true
        }));

        const { error: levelsError } = await supabase
          .from('event_levels')
          .insert(eventLevels);

        if (levelsError) {
          // If levels creation fails, delete the event
          await supabase
            .from('events')
            .delete()
            .eq('id', event.id);
          throw levelsError;
        }
      }

      return event;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['event-levels'] });
      toast({
        title: "Success",
        description: "Event created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateEvent = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...eventData }: Partial<Event> & { id: string }) => {
      const { data, error } = await supabase
        .from('events')
        .update(eventData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['event', variables.id] });
      toast({
        title: "Success",
        description: "Event updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};
