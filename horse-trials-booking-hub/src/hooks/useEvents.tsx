import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { Database } from '@/integrations/supabase/types';

export type Event = Database['public']['Tables']['events']['Row'];

export interface Organizer {
  id: string;
  full_name: string;
  email: string;
  company_name?: string;
  business_name?: string;
}

export interface EventWithOrganizer extends Event {
  organizer?: Organizer | null;
}

export const useEvents = (onlyActive = false) => {
  return useQuery({
    queryKey: ['events'],
    queryFn: async () => {
      let query = supabase
        .from('events')
        .select('*')
        .order('start_date', { ascending: true });

      if (onlyActive) {
        query = query.eq('is_active', true);
      }

      const { data: eventsRaw, error } = await query;
      // Only keep events that have all required fields
      const events = (eventsRaw ?? []).filter((e: any) =>
        e && typeof e.id === 'string' && typeof e.name === 'string' && typeof e.start_date === 'string' && typeof e.end_date === 'string' && typeof e.event_type === 'string'
      ) as Event[];

      if (error) {
        throw error;
      }

      // Batch query to get all organizer information
      const organizerIds = events
        .filter((event) => event.organizer_id)
        .map((event) => event.organizer_id!);
      const uniqueOrganizerIds = [...new Set(organizerIds)];

      let organizers: Organizer[] = [];
      if (uniqueOrganizerIds.length > 0) {
        const { data: organizersRaw, error: organizersError } = await supabase
          .from('organizers')
          .select('id, full_name, email, company_name, business_name')
          .in('id', uniqueOrganizerIds);
        if (organizersError) {
          throw organizersError;
        }
        // Only keep organizers that have all required fields
        organizers = (organizersRaw ?? []).filter((o: any) =>
          o && typeof o.id === 'string' && typeof o.full_name === 'string' && typeof o.email === 'string'
        ) as Organizer[];
      }

      const organizerMap: Record<string, Organizer> = {};
      organizers.forEach((organizer) => {
        organizerMap[organizer.id] = organizer;
      });

      const eventsWithOrganizers: EventWithOrganizer[] = events.map((event) => ({
        ...event,
        organizer: event.organizer_id ? organizerMap[event.organizer_id] ?? null : null,
      }));

      return eventsWithOrganizers;
    },
    enabled: true,
  });
};

export const useEvent = (eventId?: string) => {
  return useQuery({
    queryKey: ['event', eventId],
    queryFn: async () => {
      if (!eventId) throw new Error('Event ID is required');

      const { data: event, error } = await supabase
        .from('events')
        .select('*')
        .eq('id', eventId)
        .single();

      if (error) {
        throw error;
      }

      // Get organizer information if organizer_id exists
      let organizer: Organizer | null = null;
      if (event && event.organizer_id) {
        const { data: organizerData, error: organizerError } = await supabase
          .from('organizers')
          .select('id, full_name, email, company_name, business_name')
          .eq('id', event.organizer_id)
          .single();

        if (!organizerError && organizerData && typeof organizerData.id === 'string' && typeof organizerData.full_name === 'string' && typeof organizerData.email === 'string') {
          organizer = organizerData as Organizer;
        }
      }

      // Only return if all required event fields exist
      if (!event || typeof event.id !== 'string' || typeof event.name !== 'string' || typeof event.start_date !== 'string' || typeof event.end_date !== 'string' || typeof event.event_type !== 'string') {
        throw new Error('Invalid event data');
      }

      return {
        ...(event as Event),
        organizer,
      } as EventWithOrganizer;
    },
    enabled: !!eventId,
  });
};

export const useCreateEvent = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (eventData: {
      event: Omit<Event, 'id' | 'created_at'>;
      levelIds: string[];
    }) => {
      // First create the event
      const { data: event, error: eventError } = await supabase
        .from('events')
        .insert([eventData.event])
        .select()
        .single();

      if (eventError) {
        throw eventError;
      }

      // Then create the event levels if any are selected
      if (eventData.levelIds && eventData.levelIds.length > 0) {
        const eventLevels = eventData.levelIds.map(levelId => ({
          event_id: event.id,
          level_id: levelId,
          is_enabled: true
        }));

        const { error: levelsError } = await supabase
          .from('event_levels')
          .insert(eventLevels);

        if (levelsError) {
          // If levels creation fails, delete the event
          await supabase
            .from('events')
            .delete()
            .eq('id', event.id);
          throw levelsError;
        }
      }

      // Create default pricing for the event
      const defaultPricing = [
        {
          event_id: event.id,
          activity_type: 'dressage',
          price: 25,
          currency_symbol: '$',
          is_active: true
        },
        {
          event_id: event.id,
          activity_type: 'show_jumping',
          price: 30,
          currency_symbol: '$',
          is_active: true
        },
        {
          event_id: event.id,
          activity_type: 'cross_country',
          price: 45,
          currency_symbol: '$',
          is_active: true
        }
      ];

      const { error: pricingError } = await supabase
        .from('event_pricing')
        .insert(defaultPricing);

      if (pricingError) {
        console.error('Error creating default pricing:', pricingError);
        // Don't fail the event creation if pricing fails, just log it
        // The user can set pricing manually later
      }

      return event;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['event-levels'] });
      queryClient.invalidateQueries({ queryKey: ['event_pricing'] });
      toast({
        title: "Success",
        description: "Event created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateEvent = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...eventData }: Partial<Event> & { id: string }) => {
      // Filter out fields that are not valid database columns
      const validFields = ['name', 'start_date', 'end_date', 'event_type', 'is_active', 'organizer_id'];
      const filteredData = Object.keys(eventData).reduce((acc, key) => {
        if (validFields.includes(key)) {
          acc[key] = eventData[key];
        }
        return acc;
      }, {} as Partial<Event>);

      const { data, error } = await supabase
        .from('events')
        .update(filteredData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['accessible-events'] });
      queryClient.invalidateQueries({ queryKey: ['event', variables.id] });
      toast({
        title: "Success",
        description: "Event updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteEvent = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (eventId: string) => {
      // Special case for testing: allow deletion if env var is set
      const allowDeleteWithBookings = process.env.REACT_APP_ALLOW_EVENT_DELETE_WITH_BOOKINGS === 'true';

      // 0. Check for bookings
      const bookingsResult: any = await supabase
        .from('bookings_with_details_view')
        .select('booking_id')
        .eq('location_event_id', eventId);
      const bookings = bookingsResult.data;
      const bookingsQueryError = bookingsResult.error;

      if (bookingsQueryError) throw bookingsQueryError;
      if (!allowDeleteWithBookings && bookings && bookings.length > 0) {
        throw new Error('Cannot delete event: there are existing bookings for this event.');
      }

      // 0b. Check for payments (if you have a payments table, adjust table/column as needed)
      // const { data: payments, error: paymentsQueryError } = await supabase
      //   .from('payments')
      //   .select('id')
      //   .eq('event_id', eventId);
      //
      // if (paymentsQueryError) throw paymentsQueryError;
      // if (!allowDeleteWithBookings && payments && payments.length > 0) {
      //   throw new Error('Cannot delete event: there are payments associated with this event.');
      // }

      // Delete in the correct order to handle foreign key constraints
      
      // 1. First, get all locations for this event
      const { data: locations, error: locationsQueryError } = await supabase
        .from('locations')
        .select('id')
        .eq('event_id', eventId);

      if (locationsQueryError) {
        console.error('Error querying locations:', locationsQueryError);
        throw new Error(`Failed to query locations: ${locationsQueryError.message}`);
      }

      const locationIds = locations?.map(loc => loc.id) || [];

      if (locationIds.length > 0) {
        // 2. Get all time slots for these locations
        const { data: timeSlots, error: timeSlotsQueryError } = await supabase
          .from('time_slots')
          .select('id')
          .in('location_id', locationIds);

        if (timeSlotsQueryError) {
          console.error('Error querying time slots:', timeSlotsQueryError);
          throw new Error(`Failed to query time slots: ${timeSlotsQueryError.message}`);
        }

        const timeSlotIds = timeSlots?.map(ts => ts.id) || [];

        if (timeSlotIds.length > 0) {
          // 3. Get all booking slots that reference these time slots
          const { data: bookingSlots, error: bookingSlotsQueryError } = await supabase
            .from('booking_slots')
            .select('booking_code')
            .in('time_slot_id', timeSlotIds);

          if (bookingSlotsQueryError) {
            console.error('Error querying booking slots:', bookingSlotsQueryError);
            throw new Error(`Failed to query booking slots: ${bookingSlotsQueryError.message}`);
          }

          const bookingCodes = [...new Set(bookingSlots?.map(bs => bs.booking_code) || [])];

          if (bookingCodes.length > 0) {
            // 4. Delete booking slots
            const { error: bookingSlotsDeleteError } = await supabase
              .from('booking_slots')
              .delete()
              .in('booking_code', bookingCodes);

            if (bookingSlotsDeleteError) {
              console.error('Error deleting booking slots:', bookingSlotsDeleteError);
              throw new Error(`Failed to delete booking slots: ${bookingSlotsDeleteError.message}`);
            }

            // 5. Delete bookings
            const { error: bookingsError } = await supabase
              .from('bookings')
              .delete()
              .in('booking_code', bookingCodes);

            if (bookingsError) {
              console.error('Error deleting bookings:', bookingsError);
              throw new Error(`Failed to delete bookings: ${bookingsError.message}`);
            }
          }

          // 6. Delete time slots
          const { error: timeSlotsError } = await supabase
            .from('time_slots')
            .delete()
            .in('location_id', locationIds);

          if (timeSlotsError) {
            console.error('Error deleting time slots:', timeSlotsError);
            throw new Error(`Failed to delete time slots: ${timeSlotsError.message}`);
          }
        }
      }

      // 7. Delete activities
      const { error: activitiesError } = await supabase
        .from('activities')
        .delete()
        .in('location_id', locationIds);

      if (activitiesError) {
        console.error('Error deleting activities:', activitiesError);
        throw new Error(`Failed to delete activities: ${activitiesError.message}`);
      }

      // 8. Delete the event itself
      const { error: eventDeleteError } = await supabase
        .from('events')
        .delete()
        .eq('id', eventId);

      if (eventDeleteError) {
        console.error('Error deleting event:', eventDeleteError);
        throw new Error(`Failed to delete event: ${eventDeleteError.message}`);
      }

      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['accessible-events'] });
      toast({
        title: "Success",
        description: "Event deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};