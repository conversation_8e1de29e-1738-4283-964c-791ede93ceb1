import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface AllSlotReservation {
  id: string;
  time_slot_id: string;
  user_session_id: string;
  expires_at: string;
  created_at: string;
}

// Hook to get all active reservations (for showing "in cart" status)
export const useAllSlotReservations = () => {
  return useQuery({
    queryKey: ['all_slot_reservations'],
    queryFn: async () => {
      
      // Use the view instead of direct table access to avoid RLS issues
      const { data, error } = await supabase
        .from('slot_reservations_with_event')
        .select('id, time_slot_id, user_session_id, expires_at, created_at')
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching all slot reservations:', error);
        throw error;
      }

      return data as AllSlotReservation[];
    },
    refetchInterval: 15000, // Refresh every 15 seconds
    staleTime: 5000, // Consider data stale after 5 seconds
  });
};
