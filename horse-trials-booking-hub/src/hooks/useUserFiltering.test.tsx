import { renderHook } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { useUserFiltering } from './useUserFiltering';
import { ComprehensiveUser } from '@/hooks/useAllUsers';

describe('useUserFiltering', () => {
  const mockUsers: ComprehensiveUser[] = [
    {
      auth_user_id: 'user-1',
      auth_email: '<EMAIL>',
      full_name: '<PERSON>',
      auth_created_at: '2023-12-31T00:00:00Z',
      user_role: 'user',
      profile_phone: '************',
      user_roles: []
    },
    {
      auth_user_id: 'user-2',
      auth_email: '<EMAIL>',
      full_name: '<PERSON>',
      auth_created_at: '2024-01-01T00:00:00Z',
      user_role: 'organizer',
      profile_phone: null,
      user_roles: []
    },
    {
      auth_user_id: 'user-3',
      auth_email: '<EMAIL>',
      full_name: '<PERSON> Ad<PERSON>',
      auth_created_at: '2024-01-02T00:00:00Z',
      user_role: 'super_admin',
      profile_phone: '************',
      user_roles: []
    }
  ];

  describe('search filtering', () => {
    it('returns all users when search term is empty', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, '', 'all'));
      expect(result.current).toHaveLength(3);
    });

    it('filters by name (case insensitive)', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, 'alice', 'all'));
      expect(result.current).toHaveLength(1);
      expect(result.current[0].full_name).toBe('Alice Smith');
    });

    it('filters by email (case insensitive)', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, '<EMAIL>', 'all'));
      expect(result.current).toHaveLength(1);
      expect(result.current[0].full_name).toBe('Bob Jones');
    });

    it('filters by partial name match', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, 'charlie', 'all'));
      expect(result.current).toHaveLength(1);
      expect(result.current[0].full_name).toBe('Charlie Admin');
    });

    it('returns empty array when no matches found', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, 'nonexistent', 'all'));
      expect(result.current).toHaveLength(0);
    });
  });

  describe('role filtering', () => {
    it('returns all users when role filter is "all"', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, '', 'all'));
      expect(result.current).toHaveLength(3);
    });

    it('filters by super_admin role', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, '', 'super_admin'));
      expect(result.current).toHaveLength(1);
      expect(result.current[0].user_role).toBe('super_admin');
    });

    it('filters by organizer role', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, '', 'organizer'));
      expect(result.current).toHaveLength(1);
      expect(result.current[0].user_role).toBe('organizer');
    });

    it('filters by user role', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, '', 'user'));
      expect(result.current).toHaveLength(1);
      expect(result.current[0].user_role).toBe('user');
    });
  });

  describe('combined filtering', () => {
    it('combines search and role filters', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, 'alice', 'user'));
      expect(result.current).toHaveLength(1);
      expect(result.current[0].full_name).toBe('Alice Smith');
      expect(result.current[0].user_role).toBe('user');
    });

    it('returns empty array when search matches but role does not', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, 'alice', 'super_admin'));
      expect(result.current).toHaveLength(0);
    });

    it('returns empty array when role matches but search does not', () => {
      const { result } = renderHook(() => useUserFiltering(mockUsers, 'nonexistent', 'user'));
      expect(result.current).toHaveLength(0);
    });
  });

  describe('edge cases', () => {
    it('returns empty array when users is undefined', () => {
      const { result } = renderHook(() => useUserFiltering(undefined, '', 'all'));
      expect(result.current).toHaveLength(0);
    });

    it('returns empty array when users is null', () => {
      const { result } = renderHook(() => useUserFiltering(null as any, '', 'all'));
      expect(result.current).toHaveLength(0);
    });

    it('filters out users with missing required properties', () => {
      const usersWithMissingProps = [
        { ...mockUsers[0] },
        { auth_user_id: 'user-4', auth_email: null, full_name: null, auth_created_at: '2024-01-03T00:00:00Z', user_role: 'user', profile_phone: null, user_roles: [] },
        { ...mockUsers[2] }
      ];
      const { result } = renderHook(() => useUserFiltering(usersWithMissingProps as any, '', 'all'));
      expect(result.current).toHaveLength(2);
    });
  });
}); 