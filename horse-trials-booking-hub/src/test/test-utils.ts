import { vi } from 'vitest';
import { QueryClient } from '@tanstack/react-query';

// Mock scrollIntoView for jsdom environment
window.HTMLElement.prototype.scrollIntoView = vi.fn();

console.log('[[[ DEBUG ]]] src/test/test-utils.ts --- FILE EXECUTION STARTED');

export const mockToast = vi.fn();

// --- Mock Data ---
export const MOCK_DRESSAGE_TESTS_DATA = [
  {
    id: 'test1',
    dressage_test_library: {
      label: 'Test 1',
      levels: { name: 'Beginner' },
    },
  },
  {
    id: 'test2',
    dressage_test_library: {
      label: 'Test 2',
      levels: { name: 'Intermediate' },
    },
  },
];

export const MOCK_EVENT_LEVELS_DATA = [
  { id: 'level1', event_id: 'event123', level_id: 'lvl_beginner', levels: { id: 'lvl_beginner', name: 'Beginner', discipline: 'dressage' } },
  { id: 'level2', event_id: 'event123', level_id: 'lvl_intermediate', levels: { id: 'lvl_intermediate', name: 'Intermediate', discipline: 'dressage' } },
];

export const MOCK_LOCATIONS_DATA = [
  { id: 'loc1', event_id: 'event123', name: 'Main Arena', activity_type: 'dressage', fixed_level: null },
  { id: 'loc2', event_id: 'event123', name: 'Jumper Ring', activity_type: 'show_jumping', fixed_level: 'Novice' },
  { id: 'loc3', event_id: 'event123', name: 'Another Main Arena', activity_type: 'dressage', fixed_level: 'Intermediate' },
];

export const MOCK_ACTIVITIES_DATA = [
  { id: 'act1', location_id: 'loc1', activity_type: 'dressage', level: 'Beginner', start_time: '2023-12-15T09:00:00', end_time: '2023-12-15T12:00:00', description: 'Dressage schooling AM' },
  { id: 'act2', location_id: 'loc1', activity_type: 'dressage', level: 'Novice', start_time: '2023-12-15T13:00:00', end_time: '2023-12-15T16:00:00', description: 'Dressage schooling PM' },
  { id: 'act3', location_id: 'loc2', activity_type: 'show_jumping', level: 'Novice', start_time: '2023-12-15T10:00:00', end_time: '2023-12-15T15:00:00', description: 'Show Jumping all day' },
];

export const MOCK_PRICING_SETTINGS = {
  currency_symbol: '$',
  dressage_price: 45,
  show_jumping_price: 35,
  cross_country_price: 50,
};

export const MOCK_USER_SESSION = {
  user: {
    id: 'user123',
    email: '<EMAIL>',
  },
};

export const MOCK_PROFILE_DATA = { full_name: 'Test User' };

// Mock Stripe checkout response
export const MOCK_STRIPE_CHECKOUT_RESPONSE = {
  data: { url: 'https://stripe.com/checkout' },
  error: null,
};

// Mock Stripe checkout error response
export const MOCK_STRIPE_CHECKOUT_ERROR = {
  data: null,
  error: { message: 'Failed to create checkout session' },
};

// --- Mock Props for BookingFlow ---
export const getMockBookingFlowProps = () => ({
  selectedSlots: [
    {
      id: 'slot1',
      time_slot_id: 'ts1',
      activity_id: 'act1',
      location_id: 'loc1',
      activity_type: 'dressage',
      location_name: 'Main Arena',
      time: '9:00 AM',
      expires_at: new Date(Date.now() + 600000).toISOString(), // 10 minutes from now
      event_dressage_test_id: '',
      participant_name: '',
      horse_name: '',
    },
  ],
  event: {
    id: 'event123',
    name: 'Test Event',
    date: '2024-05-01',
  },
  onBack: vi.fn(),
});

// --- Hook Mocks ---
export const mockMutateAsync = vi.fn().mockResolvedValue({ id: 'new-booking-123' });

export const mockUseCreateBooking = () => ({
  mutateAsync: mockMutateAsync,
  isPending: false,
});

vi.mock('@/hooks/useBookings', () => ({
  useCreateBooking: () => ({
    mutateAsync: mockMutateAsync,
    isPending: false,
  }),
  useBookings: () => ({
    data: [],
    isLoading: false,
  }),
}));

vi.mock('@/hooks/useDressageTests', () => ({
  useEventDressageTests: () => ({
    data: MOCK_DRESSAGE_TESTS_DATA,
    isLoading: false,
  }),
}));

vi.mock('@/hooks/useLocations', () => ({
  useLocations: vi.fn(() => ({
    data: MOCK_LOCATIONS_DATA,
    isLoading: false,
    error: null,
  })),
}));

vi.mock('@/hooks/useLevels', () => ({
  useEventLevels: vi.fn(() => ({
    data: MOCK_EVENT_LEVELS_DATA,
    isLoading: false,
    error: null,
  })),
}));

vi.mock('@/hooks/useSlotReservations', () => ({
  getUserSessionId: () => 'test-session-id',
  useSlotReservations: vi.fn(() => ({
    data: [],
    isLoading: false,
    error: null,
  })),
}));

vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: mockToast,
  }),
}));

vi.mock('@supabase/auth-helpers-react', () => ({
  useSession: () => MOCK_USER_SESSION,
}));

vi.mock('@tanstack/react-query', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@tanstack/react-query')>();
  return {
    ...actual,
    useQueryClient: () => ({
      invalidateQueries: vi.fn(),
    }),
    useQuery: vi.fn().mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
    }),
    useMutation: vi.fn().mockReturnValue({
      mutateAsync: vi.fn().mockResolvedValue({}),
      isPending: false,
      error: null,
    }),
  };
});

// Exportable spies for Supabase calls
export const mockProfilesSingle = vi.fn().mockResolvedValue({ data: MOCK_PROFILE_DATA, error: null });
export const mockTimeSlotsUpdateEq = vi.fn().mockResolvedValue({ error: null });
export const mockSlotReservationsDeleteMatch = vi.fn().mockResolvedValue({
  data: null,
  error: null,
});

const mockSupabaseFrom = vi.fn((tableName: string) => {
  if (tableName === 'profiles') {
    return {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: mockProfilesSingle,
    };
  }
  if (tableName === 'time_slots') {
    return {
      update: vi.fn().mockReturnThis(),
      eq: mockTimeSlotsUpdateEq,
    };
  }
  if (tableName === 'slot_reservations') {
    return {
      delete: vi.fn().mockReturnThis(),
      match: mockSlotReservationsDeleteMatch,
    };
  }
  if (tableName === 'activities') {
    return {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: MOCK_ACTIVITIES_DATA, error: null }),
    };
  }
  return {};
});

vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: mockSupabaseFrom,
    functions: {
      invoke: vi.fn().mockResolvedValue(MOCK_STRIPE_CHECKOUT_RESPONSE),
    },
  },
}));

// Mock window.location.href assignment
Object.defineProperty(window, 'location', {
  value: { assign: vi.fn() },
  writable: true,
});

// This file should be imported at the top of your test files.
// e.g., import '../__tests__/test-utils';
// Vitest hoists vi.mock calls, so they are applied before other imports.

// Helper function to create a new QueryClient for each test
export const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
  logger: {
    log: console.log,
    warn: console.warn,
    error: () => {},
  },
});

vi.mock('@/hooks/usePricingSettings', () => ({
  usePricingSettings: () => ({
    data: MOCK_PRICING_SETTINGS,
    isLoading: false,
  }),
  getActivityPrice: (activityType, settings) => {
    if (!settings) return 0;
    switch (activityType) {
      case 'dressage':
        return settings.dressage_price;
      case 'show_jumping':
        return settings.show_jumping_price;
      case 'cross_country':
        return settings.cross_country_price;
      default:
        return 0;
    }
  },
}));