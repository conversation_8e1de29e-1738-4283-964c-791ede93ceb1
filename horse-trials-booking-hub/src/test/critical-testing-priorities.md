# Critical Testing Priorities

## High Priority - Database & Data Integrity Tests

### 1. Booking Update Functionality
**Issue:** We've had multiple problems with booking updates due to schema mismatches
**Tests Needed:**
- [ ] `useUpdateBooking` hook correctly updates bookings table fields
- [ ] `useUpdateBooking` hook correctly updates booking_slots table fields
- [ ] Booking updates work with dressage test selections
- [ ] Booking updates handle null/undefined values gracefully
- [ ] Booking updates preserve existing data when only partial updates are provided

### 2. Database Schema Validation
**Issue:** Column mismatches between code expectations and actual database
**Tests Needed:**
- [ ] All table columns exist as expected
- [ ] View definitions match component expectations
- [ ] Foreign key relationships are correct
- [ ] RLS policies work as intended
- [ ] Triggers don't cause update failures

### 3. Dressage Test Integration
**Issue:** Dressage test dropdowns not showing selected values
**Tests Needed:**
- [ ] View returns correct `event_dressage_test_link_id` field
- [ ] Component correctly maps view data to form fields
- [ ] Dropdown shows selected value when editing bookings
- [ ] Dressage test updates are saved correctly

## Medium Priority - Component Integration Tests

### 4. Admin Bookings Component
**Issue:** Complex component with multiple data sources
**Tests Needed:**
- [ ] Component loads booking data correctly
- [ ] Search and filtering work properly
- [ ] Grouping functionality works as expected
- [ ] Edit dialog opens with correct data
- [ ] Form validation works
- [ ] Update operations complete successfully

### 5. Data Hooks
**Issue:** Hooks need to handle various data states correctly
**Tests Needed:**
- [ ] `useBookings` returns correct data structure
- [ ] `useDressageTests` fetches and formats data correctly
- [ ] Hooks handle loading states
- [ ] Hooks handle error states
- [ ] Hooks handle empty data sets

## Low Priority - UI/UX Tests

### 6. Form Validation
- [ ] Required fields are validated
- [ ] Email formats are validated
- [ ] Error messages display correctly
- [ ] Form submission is prevented with invalid data

### 7. User Experience
- [ ] Loading states are shown appropriately
- [ ] Success/error messages are displayed
- [ ] Navigation works correctly
- [ ] Responsive design works on different screen sizes

## Testing Strategy

### 1. Unit Tests (Vitest)
- Test individual functions and hooks
- Mock Supabase calls
- Test error handling
- Test edge cases

### 2. Integration Tests (Vitest + Testing Library)
- Test component interactions
- Test hook integrations
- Test form submissions
- Test data flow

### 3. E2E Tests (Cypress)
- Test complete user flows
- Test real database interactions
- Test payment flows
- Test admin operations

### 4. Database Tests (SQL)
- Test view definitions
- Test RLS policies
- Test trigger functions
- Test data integrity

## Immediate Action Items

1. **Create database schema validation tests**
2. **Test the booking update functionality thoroughly**
3. **Add tests for dressage test integration**
4. **Create component tests for AdminBookings**
5. **Add error boundary tests**

## Test Data Setup

- Create consistent test data for all scenarios
- Include bookings with and without dressage tests
- Include various payment statuses
- Include different user roles
- Include edge cases (null values, empty strings, etc.) 