import { ComprehensiveUser } from '@/hooks/useAllUsers';

export const getHighestRole = (userRoles: ComprehensiveUser['user_roles']): string => {
  if (!userRoles || userRoles.length === 0) {
    return 'user';
  }
  
  // Check for super_admin first
  if (userRoles.some(role => role.role === 'super_admin')) {
    return 'super_admin';
  }
  
  // Then check for organizer
  if (userRoles.some(role => role.role === 'organizer')) {
    return 'organizer';
  }
  
  // Default to user
  return 'user';
};

export const getRoleBadgeVariant = (user: ComprehensiveUser) => {
  const role = user.user_role;
  switch (role) {
    case 'super_admin':
      return 'destructive';
    case 'organizer':
      return 'outline';
    case 'user':
      return 'secondary';
    default:
      return 'outline';
  }
};

export const getRoleBadgeClassName = (user: ComprehensiveUser) => {
  const role = user.user_role;
  switch (role) {
    case 'super_admin':
      return 'bg-red-50 text-red-700 border-red-200';
    case 'organizer':
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case 'user':
      return 'bg-gray-50 text-gray-700 border-gray-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};

export const getRoleDisplayName = (user: ComprehensiveUser) => {
  const role = user.user_role;
  switch (role) {
    case 'super_admin':
      return 'Super Admin';
    case 'organizer':
      return 'Organizer';
    case 'user':
      return 'User';
    default:
      return 'User';
  }
}; 