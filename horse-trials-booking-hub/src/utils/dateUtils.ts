/**
 * Formats a date string (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss) into MM/DD/YYYY format.
 * @param dateString The date string to format.
 * @returns The formatted date string or an empty string if input is invalid.
 */
export const formatDateForDisplay = (dateString?: string | null): string => {
  if (!dateString) return '';

  // Extract just the date part if there's a time component
  const datePart = dateString.split('T')[0];

  // Check if it's in YYYY-MM-DD format
  const dateRegex = /^(\d{4})-(\d{2})-(\d{2})$/;
  const match = datePart.match(dateRegex);

  if (match) {
    const [_, year, month, day] = match;
    // Convert to MM/DD/YYYY format without leading zeros
    return `${parseInt(month, 10)}/${parseInt(day, 10)}/${year}`;
  }

  // Fallback for unexpected formats, or if it's already formatted differently
  return dateString;
};

export const createDateWithoutTimezone = (dateString: string): Date => {
  const [year, month, day] = dateString.split('-').map(Number);
  return new Date(year, month - 1, day);
};