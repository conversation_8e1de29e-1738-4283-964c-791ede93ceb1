import { describe, it, expect } from 'vitest';
import { getHighestRole, getRoleBadgeVariant, getRoleBadgeClassName, getRoleDisplayName } from './userRoleUtils';
import { ComprehensiveUser } from '@/hooks/useAllUsers';

describe('userRoleUtils', () => {
  const mockUser: ComprehensiveUser = {
    auth_user_id: 'user-1',
    auth_email: '<EMAIL>',
    full_name: 'Test User',
    auth_created_at: '2023-01-01T00:00:00Z',
    user_role: 'user',
    phone: null,
    user_roles: []
  };

  describe('getHighestRole', () => {
    it('returns user when no roles exist', () => {
      const result = getHighestRole([]);
      expect(result).toBe('user');
    });

    it('returns user when user_roles is null', () => {
      const result = getHighestRole(null);
      expect(result).toBe('user');
    });

    it('returns super_admin when super_admin role exists', () => {
      const roles = [
        { role: 'super_admin', user_id: 'user-1' },
        { role: 'organizer', user_id: 'user-1' },
        { role: 'user', user_id: 'user-1' }
      ];
      const result = getHighestRole(roles);
      expect(result).toBe('super_admin');
    });

    it('returns organizer when organizer role exists but no super_admin', () => {
      const roles = [
        { role: 'organizer', user_id: 'user-1' },
        { role: 'user', user_id: 'user-1' }
      ];
      const result = getHighestRole(roles);
      expect(result).toBe('organizer');
    });

    it('returns user when only user role exists', () => {
      const roles = [{ role: 'user', user_id: 'user-1' }];
      const result = getHighestRole(roles);
      expect(result).toBe('user');
    });
  });

  describe('getRoleBadgeVariant', () => {
    it('returns destructive for super_admin', () => {
      const user = { ...mockUser, user_role: 'super_admin' };
      const result = getRoleBadgeVariant(user);
      expect(result).toBe('destructive');
    });

    it('returns outline for organizer', () => {
      const user = { ...mockUser, user_role: 'organizer' };
      const result = getRoleBadgeVariant(user);
      expect(result).toBe('outline');
    });

    it('returns secondary for user', () => {
      const user = { ...mockUser, user_role: 'user' };
      const result = getRoleBadgeVariant(user);
      expect(result).toBe('secondary');
    });

    it('returns outline for unknown role', () => {
      const user = { ...mockUser, user_role: 'unknown' as any };
      const result = getRoleBadgeVariant(user);
      expect(result).toBe('outline');
    });
  });

  describe('getRoleBadgeClassName', () => {
    it('returns red styling for super_admin', () => {
      const user = { ...mockUser, user_role: 'super_admin' };
      const result = getRoleBadgeClassName(user);
      expect(result).toBe('bg-red-50 text-red-700 border-red-200');
    });

    it('returns blue styling for organizer', () => {
      const user = { ...mockUser, user_role: 'organizer' };
      const result = getRoleBadgeClassName(user);
      expect(result).toBe('bg-blue-50 text-blue-700 border-blue-200');
    });

    it('returns gray styling for user', () => {
      const user = { ...mockUser, user_role: 'user' };
      const result = getRoleBadgeClassName(user);
      expect(result).toBe('bg-gray-50 text-gray-700 border-gray-200');
    });

    it('returns gray styling for unknown role', () => {
      const user = { ...mockUser, user_role: 'unknown' as any };
      const result = getRoleBadgeClassName(user);
      expect(result).toBe('bg-gray-50 text-gray-700 border-gray-200');
    });
  });

  describe('getRoleDisplayName', () => {
    it('returns Super Admin for super_admin', () => {
      const user = { ...mockUser, user_role: 'super_admin' };
      const result = getRoleDisplayName(user);
      expect(result).toBe('Super Admin');
    });

    it('returns Organizer for organizer', () => {
      const user = { ...mockUser, user_role: 'organizer' };
      const result = getRoleDisplayName(user);
      expect(result).toBe('Organizer');
    });

    it('returns User for user', () => {
      const user = { ...mockUser, user_role: 'user' };
      const result = getRoleDisplayName(user);
      expect(result).toBe('User');
    });

    it('returns User for unknown role', () => {
      const user = { ...mockUser, user_role: 'unknown' as any };
      const result = getRoleDisplayName(user);
      expect(result).toBe('User');
    });
  });
}); 