export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          activity_type: string
          created_at: string | null
          description: string | null
          end_time: string
          id: string
          level: string | null
          location_id: string | null
          phase: string | null
          slot_duration_minutes: number | null
          start_time: string
        }
        Insert: {
          activity_type: string
          created_at?: string | null
          description?: string | null
          end_time: string
          id?: string
          level?: string | null
          location_id?: string | null
          phase?: string | null
          slot_duration_minutes?: number | null
          start_time: string
        }
        Update: {
          activity_type?: string
          created_at?: string | null
          description?: string | null
          end_time?: string
          id?: string
          level?: string | null
          location_id?: string | null
          phase?: string | null
          slot_duration_minutes?: number | null
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "activities_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["location_id"]
          },
          {
            foreignKeyName: "activities_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "slot_reservations_with_event"
            referencedColumns: ["location_id"]
          },
        ]
      }
      app_settings: {
        Row: {
          created_at: string | null
          id: string
          key: string
          updated_at: string | null
          value: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          key: string
          updated_at?: string | null
          value?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          key?: string
          updated_at?: string | null
          value?: string | null
        }
        Relationships: []
      }
      audit_logs: {
        Row: {
          action: string
          changed_fields: string[] | null
          created_at: string | null
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          reason: string | null
          record_id: string
          session_id: string | null
          table_name: string
          user_agent: string | null
          user_email: string | null
          user_id: string
          user_role: string | null
        }
        Insert: {
          action: string
          changed_fields?: string[] | null
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          reason?: string | null
          record_id: string
          session_id?: string | null
          table_name: string
          user_agent?: string | null
          user_email?: string | null
          user_id: string
          user_role?: string | null
        }
        Update: {
          action?: string
          changed_fields?: string[] | null
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          reason?: string | null
          record_id?: string
          session_id?: string | null
          table_name?: string
          user_agent?: string | null
          user_email?: string | null
          user_id?: string
          user_role?: string | null
        }
        Relationships: []
      }
      booking_slots: {
        Row: {
          activity_description: string | null
          activity_id: string | null
          booking_code: string
          created_at: string
          created_by: string | null
          deleted_at: string | null
          deleted_by: string | null
          event_dressage_test_id: string | null
          horse_name: string
          id: string
          participant_name: string
          time_slot_id: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          activity_description?: string | null
          activity_id?: string | null
          booking_code: string
          created_at?: string
          created_by?: string | null
          deleted_at?: string | null
          deleted_by?: string | null
          event_dressage_test_id?: string | null
          horse_name: string
          id?: string
          participant_name: string
          time_slot_id: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          activity_description?: string | null
          activity_id?: string | null
          booking_code?: string
          created_at?: string
          created_by?: string | null
          deleted_at?: string | null
          deleted_by?: string | null
          event_dressage_test_id?: string | null
          horse_name?: string
          id?: string
          participant_name?: string
          time_slot_id?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "booking_slots_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_slots_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "booking_slots_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "slot_reservations_with_event"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "booking_slots_booking_code_fkey"
            columns: ["booking_code"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["booking_code"]
          },
          {
            foreignKeyName: "booking_slots_booking_code_fkey"
            columns: ["booking_code"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["booking_code"]
          },
          {
            foreignKeyName: "booking_slots_booking_code_fkey"
            columns: ["booking_code"]
            isOneToOne: false
            referencedRelation: "public_bookings_with_details_view"
            referencedColumns: ["booking_code"]
          },
          {
            foreignKeyName: "booking_slots_booking_code_fkey"
            columns: ["booking_code"]
            isOneToOne: false
            referencedRelation: "public_bookings_with_details_view"
            referencedColumns: ["booking_id"]
          },
          {
            foreignKeyName: "booking_slots_event_dressage_test_id_fkey"
            columns: ["event_dressage_test_id"]
            isOneToOne: false
            referencedRelation: "event_dressage_tests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_slots_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["time_slot_id"]
          },
          {
            foreignKeyName: "booking_slots_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "time_slots"
            referencedColumns: ["id"]
          },
        ]
      }
      bookings: {
        Row: {
          booking_code: string
          created_at: string
          created_by: string | null
          deleted_at: string | null
          deleted_by: string | null
          id: string
          payer_email: string
          payer_name: string
          payment_status: string
          stripe_payment_id: string | null
          total_price: number | null
          updated_at: string | null
          updated_by: string | null
          user_id: string | null
        }
        Insert: {
          booking_code?: string
          created_at?: string
          created_by?: string | null
          deleted_at?: string | null
          deleted_by?: string | null
          id?: string
          payer_email: string
          payer_name: string
          payment_status?: string
          stripe_payment_id?: string | null
          total_price?: number | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Update: {
          booking_code?: string
          created_at?: string
          created_by?: string | null
          deleted_at?: string | null
          deleted_by?: string | null
          id?: string
          payer_email?: string
          payer_name?: string
          payment_status?: string
          stripe_payment_id?: string | null
          total_price?: number | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      dressage_test_library: {
        Row: {
          description: string | null
          id: string
          is_active: boolean | null
          label: string
          level_id: string | null
          organization: string | null
        }
        Insert: {
          description?: string | null
          id?: string
          is_active?: boolean | null
          label: string
          level_id?: string | null
          organization?: string | null
        }
        Update: {
          description?: string | null
          id?: string
          is_active?: boolean | null
          label?: string
          level_id?: string | null
          organization?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dressage_test_library_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels_library"
            referencedColumns: ["id"]
          },
        ]
      }
      event_dressage_tests: {
        Row: {
          created_at: string | null
          dressage_test_library_id: string | null
          event_id: string | null
          id: string
          is_active: boolean | null
          is_enabled: boolean | null
          test_id: string | null
        }
        Insert: {
          created_at?: string | null
          dressage_test_library_id?: string | null
          event_id?: string | null
          id?: string
          is_active?: boolean | null
          is_enabled?: boolean | null
          test_id?: string | null
        }
        Update: {
          created_at?: string | null
          dressage_test_library_id?: string | null
          event_id?: string | null
          id?: string
          is_active?: boolean | null
          is_enabled?: boolean | null
          test_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_dressage_tests_dressage_test_library_id_fkey"
            columns: ["dressage_test_library_id"]
            isOneToOne: false
            referencedRelation: "dressage_test_library"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_dressage_tests_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      event_levels: {
        Row: {
          created_at: string | null
          event_id: string | null
          id: string
          is_active: boolean | null
          is_enabled: boolean | null
          level_id: string | null
        }
        Insert: {
          created_at?: string | null
          event_id?: string | null
          id?: string
          is_active?: boolean | null
          is_enabled?: boolean | null
          level_id?: string | null
        }
        Update: {
          created_at?: string | null
          event_id?: string | null
          id?: string
          is_active?: boolean | null
          is_enabled?: boolean | null
          level_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_levels_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_levels_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels_library"
            referencedColumns: ["id"]
          },
        ]
      }
      event_pricing: {
        Row: {
          activity_type: string
          created_at: string | null
          currency_symbol: string | null
          event_id: string
          id: string
          is_active: boolean | null
          level: string | null
          price: number
          updated_at: string | null
        }
        Insert: {
          activity_type: string
          created_at?: string | null
          currency_symbol?: string | null
          event_id: string
          id?: string
          is_active?: boolean | null
          level?: string | null
          price: number
          updated_at?: string | null
        }
        Update: {
          activity_type?: string
          created_at?: string | null
          currency_symbol?: string | null
          event_id?: string
          id?: string
          is_active?: boolean | null
          level?: string | null
          price?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_pricing_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          created_at: string | null
          created_by: string | null
          end_date: string
          event_type: string | null
          id: string
          is_active: boolean | null
          name: string
          organizer_id: string | null
          start_date: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          end_date: string
          event_type?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          organizer_id?: string | null
          start_date: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          end_date?: string
          event_type?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          organizer_id?: string | null
          start_date?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "events_organizer_id_fkey"
            columns: ["organizer_id"]
            isOneToOne: false
            referencedRelation: "organizers"
            referencedColumns: ["id"]
          },
        ]
      }
      levels_library: {
        Row: {
          created_at: string | null
          description: string | null
          discipline: string
          id: string
          is_active: boolean | null
          name: string
          sort_order: number | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          discipline: string
          id?: string
          is_active?: boolean | null
          name: string
          sort_order?: number | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          discipline?: string
          id?: string
          is_active?: boolean | null
          name?: string
          sort_order?: number | null
        }
        Relationships: []
      }
      locations: {
        Row: {
          activity_type: string
          created_at: string | null
          created_by: string | null
          description: string | null
          event_id: string | null
          fixed_level: string | null
          id: string
          is_active: boolean | null
          name: string
        }
        Insert: {
          activity_type: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_id?: string | null
          fixed_level?: string | null
          id?: string
          is_active?: boolean | null
          name: string
        }
        Update: {
          activity_type?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_id?: string | null
          fixed_level?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "locations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      organizers: {
        Row: {
          business_address: string | null
          business_name: string | null
          company_name: string | null
          created_at: string | null
          email: string
          full_name: string
          id: string
          is_verified: boolean | null
          phone: string | null
          stripe_account_id: string | null
          website: string | null
        }
        Insert: {
          business_address?: string | null
          business_name?: string | null
          company_name?: string | null
          created_at?: string | null
          email: string
          full_name: string
          id?: string
          is_verified?: boolean | null
          phone?: string | null
          stripe_account_id?: string | null
          website?: string | null
        }
        Update: {
          business_address?: string | null
          business_name?: string | null
          company_name?: string | null
          created_at?: string | null
          email?: string
          full_name?: string
          id?: string
          is_verified?: boolean | null
          phone?: string | null
          stripe_account_id?: string | null
          website?: string | null
        }
        Relationships: []
      }
      pending_bookings: {
        Row: {
          created_at: string
          id: string
          payload: Json
        }
        Insert: {
          created_at?: string
          id?: string
          payload: Json
        }
        Update: {
          created_at?: string
          id?: string
          payload?: Json
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          phone: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id: string
          phone?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          phone?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      slot_reservations: {
        Row: {
          created_at: string | null
          expires_at: string
          id: string
          time_slot_id: string
          user_session_id: string
        }
        Insert: {
          created_at?: string | null
          expires_at: string
          id?: string
          time_slot_id: string
          user_session_id: string
        }
        Update: {
          created_at?: string | null
          expires_at?: string
          id?: string
          time_slot_id?: string
          user_session_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "slot_reservations_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["time_slot_id"]
          },
          {
            foreignKeyName: "slot_reservations_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "time_slots"
            referencedColumns: ["id"]
          },
        ]
      }
      time_slots: {
        Row: {
          activity_id: string | null
          created_at: string | null
          created_by: string | null
          deleted_at: string | null
          deleted_by: string | null
          end_time: string
          id: string
          is_bookable: boolean | null
          is_booked: boolean | null
          level: string | null
          location_id: string | null
          start_time: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          activity_id?: string | null
          created_at?: string | null
          created_by?: string | null
          deleted_at?: string | null
          deleted_by?: string | null
          end_time: string
          id?: string
          is_bookable?: boolean | null
          is_booked?: boolean | null
          level?: string | null
          location_id?: string | null
          start_time: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          activity_id?: string | null
          created_at?: string | null
          created_by?: string | null
          deleted_at?: string | null
          deleted_by?: string | null
          end_time?: string
          id?: string
          is_bookable?: boolean | null
          is_booked?: boolean | null
          level?: string | null
          location_id?: string | null
          start_time?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "slot_reservations_with_event"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["location_id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "slot_reservations_with_event"
            referencedColumns: ["location_id"]
          },
        ]
      }
      user_roles: {
        Row: {
          created_at: string | null
          id: string
          role: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          role: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          role?: string
          user_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      bookings_with_details_view: {
        Row: {
          activity_description: string | null
          activity_id: string | null
          activity_level: string | null
          activity_slot_duration_minutes: number | null
          activity_specific_description: string | null
          activity_specific_type: string | null
          booking_code: string | null
          booking_created_at: string | null
          booking_id: string | null
          booking_slot_activity_id: string | null
          booking_slot_id: string | null
          dressage_level_discipline: string | null
          dressage_level_id: string | null
          dressage_level_name: string | null
          dressage_test_description: string | null
          dressage_test_id: string | null
          dressage_test_label: string | null
          event_dressage_test_link_id: string | null
          horse_name: string | null
          location_activity_type: string | null
          location_event_id: string | null
          location_id: string | null
          location_name: string | null
          participant_name: string | null
          payer_email: string | null
          payer_name: string | null
          payment_status: string | null
          slot_created_at: string | null
          stripe_payment_id: string | null
          time_slot_activity_fk: string | null
          time_slot_end_time: string | null
          time_slot_id: string | null
          time_slot_is_booked: boolean | null
          time_slot_level: string | null
          time_slot_location_fk: string | null
          time_slot_start_time: string | null
          total_price: number | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "booking_slots_activity_id_fkey"
            columns: ["booking_slot_activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_slots_activity_id_fkey"
            columns: ["booking_slot_activity_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "booking_slots_activity_id_fkey"
            columns: ["booking_slot_activity_id"]
            isOneToOne: false
            referencedRelation: "slot_reservations_with_event"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "booking_slots_event_dressage_test_id_fkey"
            columns: ["event_dressage_test_link_id"]
            isOneToOne: false
            referencedRelation: "event_dressage_tests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "dressage_test_library_level_id_fkey"
            columns: ["dressage_level_id"]
            isOneToOne: false
            referencedRelation: "levels_library"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "locations_event_id_fkey"
            columns: ["location_event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["time_slot_activity_fk"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["time_slot_activity_fk"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["time_slot_activity_fk"]
            isOneToOne: false
            referencedRelation: "slot_reservations_with_event"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["time_slot_location_fk"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["location_id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["time_slot_location_fk"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["time_slot_location_fk"]
            isOneToOne: false
            referencedRelation: "slot_reservations_with_event"
            referencedColumns: ["location_id"]
          },
        ]
      }
      public_bookings_with_details_view: {
        Row: {
          activity_description: string | null
          activity_level: string | null
          activity_specific_type: string | null
          booking_code: string | null
          booking_id: string | null
          dressage_level_name: string | null
          dressage_test_label: string | null
          horse_name: string | null
          location_event_id: string | null
          location_name: string | null
          participant_name: string | null
          time_slot_end_time: string | null
          time_slot_level: string | null
          time_slot_start_time: string | null
        }
        Relationships: [
          {
            foreignKeyName: "locations_event_id_fkey"
            columns: ["location_event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      slot_reservations_with_event: {
        Row: {
          activity_id: string | null
          activity_level: string | null
          activity_type: string | null
          created_at: string | null
          end_time: string | null
          event_end_date: string | null
          event_id: string | null
          event_name: string | null
          event_start_date: string | null
          expires_at: string | null
          id: string | null
          is_booked: boolean | null
          location_activity_type: string | null
          location_id: string | null
          location_name: string | null
          start_time: string | null
          time_slot_id: string | null
          time_slot_level: string | null
          user_session_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "locations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "slot_reservations_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["time_slot_id"]
          },
          {
            foreignKeyName: "slot_reservations_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "time_slots"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      delete_user_completely: {
        Args: { user_id: string }
        Returns: boolean
      }
      delete_user_safe: {
        Args: { user_id_param: string }
        Returns: Json
      }
      get_all_users_with_roles: {
        Args: Record<PropertyKey, never>
        Returns: {
          auth_user_id: string
          auth_email: string
          full_name: string
          user_role: string
          role_display_name: string
          account_status: string
          profile_status: string
          role_status: string
          auth_created_at: string
          last_sign_in_at: string
          phone: string
        }[]
      }
      get_audit_context: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          user_role: string
          user_email: string
          ip_address: unknown
          user_agent: string
          session_id: string
        }[]
      }
      get_audit_trail: {
        Args: { p_table_name: string; p_record_id: string; p_limit?: number }
        Returns: {
          id: string
          table_name: string
          record_id: string
          action: string
          old_values: Json
          new_values: Json
          changed_fields: string[]
          user_id: string
          user_role: string
          user_email: string
          ip_address: unknown
          user_agent: string
          reason: string
          created_at: string
          session_id: string
        }[]
      }
      get_current_organizer_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_event_currency_symbol: {
        Args: { p_event_id: string }
        Returns: string
      }
      get_event_pricing: {
        Args: { p_event_id: string; p_activity_type: string; p_level?: string }
        Returns: number
      }
      get_user_audit_trail: {
        Args: { p_user_id: string; p_limit?: number }
        Returns: {
          id: string
          table_name: string
          record_id: string
          action: string
          old_values: Json
          new_values: Json
          changed_fields: string[]
          user_id: string
          user_role: string
          user_email: string
          ip_address: unknown
          user_agent: string
          reason: string
          created_at: string
          session_id: string
        }[]
      }
      get_user_by_email: {
        Args: { user_email: string }
        Returns: {
          auth_user_id: string
          auth_email: string
          full_name: string
          user_role: string
          role_display_name: string
          account_status: string
          profile_status: string
          role_status: string
          auth_created_at: string
          last_sign_in_at: string
          phone: string
        }[]
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      set_audit_reason: {
        Args: { reason_text: string }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
