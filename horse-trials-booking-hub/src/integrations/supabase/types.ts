export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          activity_type: string | null
          created_at: string | null
          description: string | null
          end_time: string
          id: string
          level: string | null
          location_id: string | null
          slot_duration_minutes: number | null
          start_time: string
        }
        Insert: {
          activity_type?: string | null
          created_at?: string | null
          description?: string | null
          end_time: string
          id?: string
          level?: string | null
          location_id?: string | null
          slot_duration_minutes?: number | null
          start_time: string
        }
        Update: {
          activity_type?: string | null
          created_at?: string | null
          description?: string | null
          end_time?: string
          id?: string
          level?: string | null
          location_id?: string | null
          slot_duration_minutes?: number | null
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "activities_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["location_id"]
          },
          {
            foreignKeyName: "activities_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      app_settings: {
        Row: {
          created_at: string | null
          id: string
          key: string
          updated_at: string | null
          value: Json
        }
        Insert: {
          created_at?: string | null
          id?: string
          key: string
          updated_at?: string | null
          value: Json
        }
        Update: {
          created_at?: string | null
          id?: string
          key?: string
          updated_at?: string | null
          value?: Json
        }
        Relationships: []
      }
      arenas_backup: {
        Row: {
          activity_type: string
          created_at: string | null
          created_by: string | null
          description: string | null
          event_id: string | null
          fixed_level: string | null
          id: string
          is_active: boolean | null
          name: string
          slot_duration_minutes: number
        }
        Insert: {
          activity_type: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_id?: string | null
          fixed_level?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          slot_duration_minutes: number
        }
        Update: {
          activity_type?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_id?: string | null
          fixed_level?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          slot_duration_minutes?: number
        }
        Relationships: [
          {
            foreignKeyName: "arenas_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "arenas_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      bookings: {
        Row: {
          activity_date: string | null
          activity_description: string | null
          booking_code: string | null
          created_at: string | null
          event_dressage_test_id: string | null
          horse_name: string
          id: string
          participant_name: string
          payer_email: string | null
          payer_name: string | null
          payment_status: string | null
          time_slot_id: string | null
          user_id: string | null
        }
        Insert: {
          activity_date?: string | null
          activity_description?: string | null
          booking_code?: string | null
          created_at?: string | null
          event_dressage_test_id?: string | null
          horse_name: string
          id?: string
          participant_name: string
          payer_email?: string | null
          payer_name?: string | null
          payment_status?: string | null
          time_slot_id?: string | null
          user_id?: string | null
        }
        Update: {
          activity_date?: string | null
          activity_description?: string | null
          booking_code?: string | null
          created_at?: string | null
          event_dressage_test_id?: string | null
          horse_name?: string
          id?: string
          participant_name?: string
          payer_email?: string | null
          payer_name?: string | null
          payment_status?: string | null
          time_slot_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bookings_event_dressage_test_id_fkey"
            columns: ["event_dressage_test_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["event_dressage_test_link_id"]
          },
          {
            foreignKeyName: "bookings_event_dressage_test_id_fkey"
            columns: ["event_dressage_test_id"]
            isOneToOne: false
            referencedRelation: "event_dressage_tests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["time_slot_id"]
          },
          {
            foreignKeyName: "bookings_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "time_slots"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      dressage_test_library: {
        Row: {
          description: string | null
          id: string
          is_active: boolean | null
          label: string
          level_id: string | null
          organization: string | null
        }
        Insert: {
          description?: string | null
          id?: string
          is_active?: boolean | null
          label: string
          level_id?: string | null
          organization?: string | null
        }
        Update: {
          description?: string | null
          id?: string
          is_active?: boolean | null
          label?: string
          level_id?: string | null
          organization?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dressage_test_library_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["dressage_level_id"]
          },
          {
            foreignKeyName: "dressage_test_library_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels_library"
            referencedColumns: ["id"]
          },
        ]
      }
      event_dressage_tests: {
        Row: {
          created_at: string | null
          event_id: string | null
          id: string
          is_enabled: boolean | null
          test_id: string | null
        }
        Insert: {
          created_at?: string | null
          event_id?: string | null
          id?: string
          is_enabled?: boolean | null
          test_id?: string | null
        }
        Update: {
          created_at?: string | null
          event_id?: string | null
          id?: string
          is_enabled?: boolean | null
          test_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_dressage_tests_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_dressage_tests_test_id_fkey"
            columns: ["test_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["dressage_test_id"]
          },
          {
            foreignKeyName: "event_dressage_tests_test_id_fkey"
            columns: ["test_id"]
            isOneToOne: false
            referencedRelation: "dressage_test_library"
            referencedColumns: ["id"]
          },
        ]
      }
      event_levels: {
        Row: {
          event_id: string | null
          id: string
          is_enabled: boolean | null
          level_id: string | null
        }
        Insert: {
          event_id?: string | null
          id?: string
          is_enabled?: boolean | null
          level_id?: string | null
        }
        Update: {
          event_id?: string | null
          id?: string
          is_enabled?: boolean | null
          level_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_levels_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_levels_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["dressage_level_id"]
          },
          {
            foreignKeyName: "event_levels_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels_library"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          created_at: string | null
          created_by: string | null
          end_date: string | null
          id: string
          is_active: boolean | null
          name: string
          schooling_end_date: string | null
          schooling_start_date: string | null
          start_date: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          end_date?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          schooling_end_date?: string | null
          schooling_start_date?: string | null
          start_date?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          end_date?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          schooling_end_date?: string | null
          schooling_start_date?: string | null
          start_date?: string | null
        }
        Relationships: []
      }
      levels_library: {
        Row: {
          description: string | null
          discipline: string | null
          id: string
          is_active: boolean | null
          name: string
          sort_order: number | null
        }
        Insert: {
          description?: string | null
          discipline?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          sort_order?: number | null
        }
        Update: {
          description?: string | null
          discipline?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          sort_order?: number | null
        }
        Relationships: []
      }
      locations: {
        Row: {
          activity_type: string
          created_at: string | null
          created_by: string | null
          description: string | null
          event_id: string | null
          fixed_level: string | null
          id: string
          is_active: boolean | null
          name: string
        }
        Insert: {
          activity_type: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_id?: string | null
          fixed_level?: string | null
          id?: string
          is_active?: boolean | null
          name: string
        }
        Update: {
          activity_type?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_id?: string | null
          fixed_level?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "locations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "locations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          booking_id: string | null
          created_at: string | null
          id: string
          payment_status: string | null
          stripe_payment_id: string | null
          total_amount: number | null
          user_id: string | null
        }
        Insert: {
          booking_id?: string | null
          created_at?: string | null
          id?: string
          payment_status?: string | null
          stripe_payment_id?: string | null
          total_amount?: number | null
          user_id?: string | null
        }
        Update: {
          booking_id?: string | null
          created_at?: string | null
          id?: string
          payment_status?: string | null
          stripe_payment_id?: string | null
          total_amount?: number | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payments_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["booking_id"]
          },
          {
            foreignKeyName: "payments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string
          email: string
          full_name: string | null
          id: string
          phone: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          full_name?: string | null
          id: string
          phone?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          full_name?: string | null
          id?: string
          phone?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      slot_reservations: {
        Row: {
          created_at: string
          expires_at: string
          id: string
          reserved_at: string
          time_slot_id: string
          user_session_id: string
        }
        Insert: {
          created_at?: string
          expires_at?: string
          id?: string
          reserved_at?: string
          time_slot_id: string
          user_session_id: string
        }
        Update: {
          created_at?: string
          expires_at?: string
          id?: string
          reserved_at?: string
          time_slot_id?: string
          user_session_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "slot_reservations_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["time_slot_id"]
          },
          {
            foreignKeyName: "slot_reservations_time_slot_id_fkey"
            columns: ["time_slot_id"]
            isOneToOne: false
            referencedRelation: "time_slots"
            referencedColumns: ["id"]
          },
        ]
      }
      time_slots: {
        Row: {
          activity_id: string | null
          created_at: string | null
          end_time: string
          id: string
          is_booked: boolean | null
          level: string | null
          location_id: string | null
          start_time: string
        }
        Insert: {
          activity_id?: string | null
          created_at?: string | null
          end_time: string
          id?: string
          is_booked?: boolean | null
          level?: string | null
          location_id?: string | null
          start_time: string
        }
        Update: {
          activity_id?: string | null
          created_at?: string | null
          end_time?: string
          id?: string
          is_booked?: boolean | null
          level?: string | null
          location_id?: string | null
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["location_id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          created_at: string
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          phone: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          full_name?: string | null
          id?: string
          phone?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          full_name?: string | null
          id?: string
          phone?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      bookings_with_details_view: {
        Row: {
          activity_date: string | null
          activity_description: string | null
          activity_id: string | null
          activity_level: string | null
          activity_slot_duration_minutes: number | null
          activity_specific_description: string | null
          activity_specific_type: string | null
          booking_code: string | null
          booking_created_at: string | null
          booking_event_dressage_test_fk: string | null
          booking_id: string | null
          dressage_level_discipline: string | null
          dressage_level_id: string | null
          dressage_level_name: string | null
          dressage_test_description: string | null
          dressage_test_id: string | null
          dressage_test_label: string | null
          event_dressage_test_link_id: string | null
          horse_name: string | null
          location_activity_type: string | null
          location_event_id: string | null
          location_id: string | null
          location_name: string | null
          participant_name: string | null
          payer_email: string | null
          payer_name: string | null
          payment_status: string | null
          time_slot_activity_fk: string | null
          time_slot_end_time: string | null
          time_slot_id: string | null
          time_slot_is_booked: boolean | null
          time_slot_level: string | null
          time_slot_location_fk: string | null
          time_slot_start_time: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bookings_event_dressage_test_id_fkey"
            columns: ["booking_event_dressage_test_fk"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["event_dressage_test_link_id"]
          },
          {
            foreignKeyName: "bookings_event_dressage_test_id_fkey"
            columns: ["booking_event_dressage_test_fk"]
            isOneToOne: false
            referencedRelation: "event_dressage_tests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "locations_event_id_fkey"
            columns: ["location_event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["time_slot_activity_fk"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slots_activity_id_fkey"
            columns: ["time_slot_activity_fk"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["activity_id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["time_slot_location_fk"]
            isOneToOne: false
            referencedRelation: "bookings_with_details_view"
            referencedColumns: ["location_id"]
          },
          {
            foreignKeyName: "time_slots_location_id_fkey"
            columns: ["time_slot_location_fk"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      cleanup_expired_reservations: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      has_role: {
        Args: {
          _user_id: string
          _role: Database["public"]["Enums"]["app_role"]
        }
        Returns: boolean
      }
      update_pricing_settings: {
        Args: {
          p_dressage_price: number
          p_show_jumping_price: number
          p_cross_country_price: number
          p_currency_symbol: string
          p_cart_retention_minutes: number
        }
        Returns: Json
      }
    }
    Enums: {
      app_role: "admin" | "organizer" | "user"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      app_role: ["admin", "organizer", "user"],
    },
  },
} as const
