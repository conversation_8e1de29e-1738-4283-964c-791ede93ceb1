import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthWrapper } from "@/components/AuthWrapper";
import { SessionContextProvider } from '@supabase/auth-helpers-react';
import { supabase } from './integrations/supabase/client';
import IndexPage from "./pages/IndexPage";
import NotFoundPage from "./pages/NotFoundPage";
import AdminLoginPage from "./pages/AdminLoginPage";
import AdminDashboardPage from "./pages/AdminDashboardPage";
import OrganizerDashboardPage from "./pages/OrganizerDashboardPage";
import SuperAdminDashboardPage from "./pages/SuperAdminDashboardPage";
import { EventManagementPage } from "./pages/EventManagementPage";
import LocationManagementPage from "./pages/LocationManagementPage";
import EventsListPage from "./pages/EventsListPage"; 
import CreateEventPage from "./pages/CreateEventPage"; 
import AdminSettingsPage from "./pages/AdminSettingsPage";
import LevelsManagementPage from "./pages/LevelsManagementPage";
import DressageTestsManagementPage from "./pages/DressageTestsManagementPage";
import UsersManagementPage from "./pages/UsersManagementPage";
import OrganizersManagementPage from "./pages/OrganizersManagementPage";
import DatabaseManagementPage from "./pages/DatabaseManagementPage";
import AboutPage from "./pages/AboutPage"; 
import AdminBookingsPage from "./pages/AdminBookingsPage"; // This is the correct wrapper page component
import BookingSuccessPage from "./pages/BookingSuccessPage";
import ProfilePage from "./pages/ProfilePage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <SessionContextProvider supabaseClient={supabase}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={
              <AuthWrapper requireAuth={false}>
                <IndexPage />
              </AuthWrapper>
            } />

            {/* Admin Routes */}
            <Route path="/admin/login" element={<AdminLoginPage />} />
            
            {/* Dashboard Routes - Role-based routing */}
            <Route path="/admin/dashboard" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin', 'organizer']}>
                <AdminDashboardPage />
              </AuthWrapper>
            } />
            <Route path="/admin/organizer-dashboard" element={
              <AuthWrapper requireAuth={true} requiredRoles={['organizer']}>
                <OrganizerDashboardPage />
              </AuthWrapper>
            } />
            <Route path="/admin/super-admin-dashboard" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin']}>
                <SuperAdminDashboardPage />
              </AuthWrapper>
            } />
            
            {/* Event Management Routes */}
            <Route path="/admin/events" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin', 'organizer']}>
                <EventsListPage />
              </AuthWrapper>
            } />
            <Route path="/admin/events/create" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin', 'organizer']}>
                <CreateEventPage />
              </AuthWrapper>
            } />
            <Route path="/admin/events/:eventId" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin', 'organizer']}>
                <EventManagementPage />
              </AuthWrapper>
            } />
            <Route path="/admin/events/:eventId/locations" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin', 'organizer']}>
                <LocationManagementPage />
              </AuthWrapper>
            } />
            
            {/* Settings Routes */}
            <Route path="/admin/settings" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin']}> {/* Settings for super_admin only */}
                <AdminSettingsPage />
              </AuthWrapper>
            } />
            
            {/* App-wide Management Routes */}
            <Route path="/admin/levels" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin']}>
                <LevelsManagementPage />
              </AuthWrapper>
            } />
            <Route path="/admin/dressage-tests" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin']}>
                <DressageTestsManagementPage />
              </AuthWrapper>
            } />
            <Route path="/admin/users" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin']}>
                <UsersManagementPage />
              </AuthWrapper>
            } />
            <Route path="/admin/organizers" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin']}>
                <OrganizersManagementPage />
              </AuthWrapper>
            } />
            <Route path="/admin/database" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin']}>
                <DatabaseManagementPage />
              </AuthWrapper>
            } />
            
            {/* Other Routes */}
            <Route path="/about" element={<AboutPage />} />
            <Route path="/booking-success" element={<BookingSuccessPage />} />
            <Route path="/profile" element={
              <AuthWrapper requireAuth={true}>
                <ProfilePage />
              </AuthWrapper>
            } />
            <Route path="/admin/profile" element={
              <AuthWrapper requireAuth={true}>
                <ProfilePage />
              </AuthWrapper>
            } />
            <Route path="/admin/events/:eventId/bookings" element={
              <AuthWrapper requireAuth={true} requiredRoles={['super_admin', 'organizer']}>
                <AdminBookingsPage /> {/* Use the correct wrapper page component */}
              </AuthWrapper>
            } />

            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </SessionContextProvider>
  </QueryClientProvider >
);

export default App;
