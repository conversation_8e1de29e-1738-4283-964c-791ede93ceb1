import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/hooks/useAuth";
import { AuthWrapper } from "@/components/AuthWrapper";
import IndexPage from "./pages/IndexPage";
import NotFoundPage from "./pages/NotFoundPage";
import AdminLoginPage from "./pages/AdminLoginPage";
import AdminDashboardPage from "./pages/AdminDashboardPage";
import EventManagementPage from "./pages/EventManagementPage";
import LocationManagementPage from "./pages/LocationManagementPage";
import EventsListPage from "./pages/EventsListPage"; 
import CreateEventPage from "./pages/CreateEventPage"; 
import AdminSettingsPage from "./pages/AdminSettingsPage";
import AboutPage from "./pages/AboutPage"; 
import AdminBookingsPage from "./pages/AdminBookingsPage"; // This is the correct wrapper page component
import BookingSuccessPage from "./pages/BookingSuccessPage";
import ProfilePage from "./pages/ProfilePage";

import { SessionContextProvider } from '@supabase/auth-helpers-react';
import { supabase } from './integrations/supabase/client'; // adjust path if needed

const queryClient = new QueryClient();

const App = () => (
  

  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <SessionContextProvider supabaseClient={supabase}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={
              <AuthWrapper requireAuth={false}>
                <IndexPage />
              </AuthWrapper>
            } />

            {/* Admin Routes */}
            <Route path="/admin/login" element={<AdminLoginPage />} />
            <Route path="/admin/dashboard" element={
              <AuthWrapper requireAuth={true} requiredRoles={['admin', 'organizer']}>
                <AdminDashboardPage />
              </AuthWrapper>
            } />
            <Route path="/admin/events" element={
              <AuthWrapper requireAuth={true} requiredRoles={['admin', 'organizer']}>
                <EventsListPage />
              </AuthWrapper>
            } />
            <Route path="/admin/events/create" element={
              <AuthWrapper requireAuth={true} requiredRoles={['admin', 'organizer']}>
                <CreateEventPage />
              </AuthWrapper>
            } />
            <Route path="/admin/events/:eventId" element={
              <AuthWrapper requireAuth={true} requiredRoles={['admin', 'organizer']}>
                <EventManagementPage />
              </AuthWrapper>
            } />
            <Route path="/admin/events/:eventId/locations" element={
              <AuthWrapper requireAuth={true} requiredRoles={['admin', 'organizer']}>
                <LocationManagementPage />
              </AuthWrapper>
            } />
            <Route path="/admin/settings" element={
              <AuthWrapper requireAuth={true} requiredRoles={['admin']}> {/* Example: Settings only for 'admin' */}
                <AdminSettingsPage />
              </AuthWrapper>
            } />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/booking-success" element={<BookingSuccessPage />} />
            <Route path="/profile" element={
              <AuthWrapper requireAuth={true}>
                <ProfilePage />
              </AuthWrapper>
            } />
            <Route path="/admin/events/:eventId/bookings" element={
              <AuthWrapper requireAuth={true} requiredRoles={['admin', 'organizer']}>
                <AdminBookingsPage /> {/* Use the correct wrapper page component */}
              </AuthWrapper>
            } />

            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </SessionContextProvider>
  </AuthProvider>
  </QueryClientProvider >
);

export default App;
