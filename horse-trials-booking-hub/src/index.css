@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 40 20% 95%;
    --sidebar-foreground: 40 10% 10%;

    --sidebar-primary: 90 20% 30%;
    --sidebar-primary-foreground: 0 0% 100%;

    --sidebar-accent: 40 20% 80%;
    --sidebar-accent-foreground: 40 10% 20%;

    --sidebar-border: 40 20% 80%;
    --sidebar-ring: 90 20% 30%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-cream text-text-primary font-inter;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-playfair tracking-tight;
  }
}

@layer components {
  .card-hover {
    @apply transition-all duration-200 ease-out;
  }
  .card-hover:hover, .card-hover:focus-within {
    @apply shadow-lg border-gold transform -translate-y-0.5 scale-[1.015];
  }
  
  .btn-primary {
    @apply bg-primary text-cream font-semibold px-6 py-3 rounded-full hover:bg-primary-light transition-colors shadow-md focus:outline-none focus:ring-2 focus:ring-gold focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-gold text-primary font-semibold px-6 py-3 rounded-full hover:bg-gold-hover transition-colors shadow-md focus:outline-none focus:ring-2 focus:ring-gold focus:ring-offset-2;
  }
  
  .card-base {
    @apply bg-white rounded-xl shadow-md border border-cream-light p-6;
  }
}