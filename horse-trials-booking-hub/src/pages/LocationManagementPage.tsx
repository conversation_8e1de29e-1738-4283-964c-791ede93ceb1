import React from 'react';
import { LocationManagement as LocationManagementComponent } from '@/components/admin/LocationManagement';
import { useNavigate, useParams } from 'react-router-dom';
import { Header } from '@/components/Header';
import { useEvents } from '@/hooks/useEvents';

const LocationManagementPage = () => {
  const navigate = useNavigate();
  const { eventId } = useParams<{ eventId: string }>();
  const { data: events } = useEvents();
  // Auth and role checks are now handled by AuthWrapper in App.tsx
  
  // Redirect if eventId is invalid
  if (!eventId) {
    navigate('/admin/dashboard'); // Use navigate for consistency
    return null; // Return null after navigation
  }
  
  // Find the event name
  const event = events?.find(e => e.id === eventId);
  const eventName = event?.name || 'Event';
  
  const handleBack = () => {
    // Navigate back to the specific event management page
    // instead of the general dashboard.
    navigate(`/admin/events/${eventId}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header 
        onBackToEvents={handleBack}
        showBackButton={true}
      />
      <main className="container mx-auto px-4 py-8">
        <LocationManagementComponent 
          eventId={eventId}
          eventName={eventName}
          onBack={handleBack}
        />
      </main>
    </div>
  );
};

export default LocationManagementPage;
