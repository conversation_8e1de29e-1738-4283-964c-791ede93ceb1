import React, { useState, useEffect } from 'react';
import { EventList } from '@/components/EventList';
import { EventDetails } from '@/components/EventDetails';
import { BookingFlow } from '@/components/BookingFlow';
import { PublicBookings } from '@/components/PublicBookings';
import { Header } from '@/components/Header';
import { CartWidget } from '@/components/CartWidget';
import { FloatingCartIcon } from '@/components/FloatingCartIcon';
import { Event } from '@/hooks/useEvents';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useEvents } from '@/hooks/useEvents';
import { getErrorMessage } from '@/types/errors';

interface Slot {
  activity_type?: string;
  time?: string;
  location_name?: string;
  participant_name?: string;
  horse_name?: string;
  [key: string]: unknown;
}

const IndexPage = () => {
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [currentView, setCurrentView] = useState('events'); // 'events', 'details', 'booking', 'public'
  const [selectedSlots, setSelectedSlots] = useState<Slot[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [initialSelectedDate, setInitialSelectedDate] = useState<string | undefined>();
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user); // Only enable if user is authenticated
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { data: events } = useEvents();

  // Check if user is admin or organizer
  const isAdmin = userRole === 'super_admin' || userRole === 'organizer';

  // Handle event URL parameter
  useEffect(() => {
    const eventId = searchParams.get('event');
    const date = searchParams.get('date');
    if (eventId && events && !selectedEvent) {
      const event = events.find(e => e.id === eventId);
      if (event) {
        setSelectedEvent(event);
        setCurrentView('details');
        // If a date is provided, store it to pass to EventDetails
        if (date) {
          setInitialSelectedDate(date);
        }
      }
    }
  }, [searchParams, events, selectedEvent]);

  const handleEventSelect = (event: Event) => {
    setSelectedEvent(event);
    setCurrentView('details');
  };

  const handleBooking = (slots: Slot[]) => {
    setSelectedSlots(slots);
    setCurrentView('booking');
  };

  const handleCartBooking = (reservedSlots: Slot[]) => {
    // Ensure all required fields are present in the slots
    const enhancedSlots = reservedSlots.map(slot => ({
      ...slot,
      // Provide default values for potentially missing fields
      activity_type: slot.activity_type || 'booking',
      time: slot.time || 'Unknown time',
      location_name: slot.location_name || 'Location',
      // Ensure participant_name is empty so it gets defaulted to payer name
      participant_name: slot.participant_name || '',
      horse_name: slot.horse_name || '',
      // Add any other required fields with defaults
    }));
    
    setSelectedSlots(enhancedSlots);
    setCurrentView('booking');
    setIsCartOpen(false);
  };

  const handleBackToEvents = () => {
    setSelectedEvent(null);
    setSelectedSlots([]);
    setCurrentView('events');
  };

  const handleViewPublicBookings = () => {
    setCurrentView('public');
  };

  const handleAdminLogin = () => {
    // Use the new routing system instead of state-based navigation
    if (user && isAdmin) {
      if (userRole === 'super_admin') {
        navigate('/admin/super-admin-dashboard');
      } else if (userRole === 'organizer') {
        navigate('/admin/organizer-dashboard');
      } else {
        navigate('/admin/dashboard');
      }
    } else {
      navigate('/admin/login');
    }
  };

  // Hero section for events view
  if (currentView === 'events') {
    return (
      <div className="min-h-screen bg-cream flex flex-col" data-testid="events-view">
        {/* Version indicator */}
        <div className="fixed top-2 right-2 bg-blue-500 text-white px-3 py-1 rounded text-sm z-50" data-testid="version-indicator">
          Latest Code ✓
        </div>
        
        {/* Header for navigation and user access */}
        <Header 
          onAdminLogin={handleAdminLogin}
          isAdmin={isAdmin}
          userRole={userRole}
        />
        
        {/* Hero Section */}
        <section className="flex flex-col items-center justify-center text-center px-6 pt-8 pb-12 bg-primary relative" data-testid="hero-section">
          <div className="max-w-3xl mx-auto w-full">
            <div className="max-w-3xl mx-auto w-full flex flex-col items-center">
              <img src="/NextRiderUpLogo.png" alt="Next Rider Up logo" className="w-52 h-52 mb-2 mx-auto" data-testid="hero-logo" />
            </div>
            <p className="text-cream-dark mt-2 mb-8 max-w-xl mx-auto" data-testid="hero-description">
              Next Rider Up helps you book training rides the way they should be—organized, rider-friendly, and built for the show ring.
            </p>
            {/* Decorative Shape */}
            <svg className="absolute bottom-0 left-0 w-full" aria-hidden="true" viewBox="0 0 1440 80" fill="none" xmlns="http://www.w3.org/2000/svg" data-testid="hero-decoration">
              <path fill="#f6edd9" d="M0,80 C480,0 960,160 1440,80 L1440,80 L0,80 Z"></path>
            </svg>
          </div>
        </section>

        {/* Events Section */}
        <section id="events-section" className="flex-1 bg-cream px-6 py-12" data-testid="events-section">
          <div className="max-w-6xl mx-auto">
            <h2 className="font-playfair text-2xl md:text-3xl text-primary mb-8 text-center tracking-tight" data-testid="events-title">
              Upcoming Events
            </h2>
            <EventList onEventSelect={handleEventSelect} />
          </div>
        </section>
      </div>
    );
  }

  // Other views with header
  return (
    <div className="min-h-screen bg-cream" data-testid={`${currentView}-view`}>
      {/* Version indicator */}
      <div className="fixed top-2 right-2 bg-blue-500 text-white px-3 py-1 rounded text-sm z-50" data-testid="version-indicator">
        Latest Code ✓
      </div>
      
      <Header 
        onBackToEvents={handleBackToEvents}
        onViewPublicBookings={handleViewPublicBookings}
        onAdminLogin={handleAdminLogin}
        showBackButton={currentView !== 'events'}
        showPublicBookings={selectedEvent && currentView === 'details'}
        isAdmin={isAdmin}
        userRole={userRole}
      />
      
      <main className="container mx-auto px-4 py-8" data-testid="main-content">
        {currentView === 'details' && selectedEvent && (
          <EventDetails 
            event={selectedEvent} 
            onBooking={handleBooking}
            initialSelectedDate={initialSelectedDate}
          />
        )}
        
        {currentView === 'booking' && (
          <BookingFlow 
            selectedSlots={selectedSlots}
            event={selectedEvent}
            onBack={() => setCurrentView('details')}
          />
        )}
        
        {currentView === 'public' && selectedEvent && (
          <PublicBookings 
            event={selectedEvent}
            onBack={() => setCurrentView('details')}
          />
        )}
      </main>
      
      {/* Always show floating cart icon when there are items */}
      <FloatingCartIcon 
        onClick={() => setIsCartOpen(true)} 
        eventId={currentView === 'details' ? selectedEvent?.id : undefined}
      />
      
      {/* Cart modal/sheet */}
      {isCartOpen && (
        <CartWidget 
          onProceedToBooking={handleCartBooking} 
          isOpen={isCartOpen}
          onClose={() => setIsCartOpen(false)}
          eventId={currentView === 'details' ? selectedEvent?.id : undefined}
        />
      )}
    </div>
  );
};

export default IndexPage;
