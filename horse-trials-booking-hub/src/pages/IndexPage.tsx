import React, { useState, useEffect } from 'react';
import { EventList } from '@/components/EventList';
import { EventDetails } from '@/components/EventDetails';
import { BookingFlow } from '@/components/BookingFlow';
import { PublicBookings } from '@/components/PublicBookings';
import { Header } from '@/components/Header';
import { CartWidget } from '@/components/CartWidget';
import { FloatingCartIcon } from '@/components/FloatingCartIcon';
import { Event } from '@/hooks/useEvents';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useEvents } from '@/hooks/useEvents';

const IndexPage = () => {
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [currentView, setCurrentView] = useState('events'); // 'events', 'details', 'booking', 'public'
  const [selectedSlots, setSelectedSlots] = useState([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [initialSelectedDate, setInitialSelectedDate] = useState<string | undefined>();
  const { user, userRole } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { data: events } = useEvents();

  // Check if user is admin or organizer
  const isAdmin = userRole === 'admin' || userRole === 'organizer';

  // Handle event URL parameter
  useEffect(() => {
    const eventId = searchParams.get('event');
    const date = searchParams.get('date');
    if (eventId && events && !selectedEvent) {
      const event = events.find(e => e.id === eventId);
      if (event) {
        setSelectedEvent(event);
        setCurrentView('details');
        // If a date is provided, store it to pass to EventDetails
        if (date) {
          setInitialSelectedDate(date);
        }
      }
    }
  }, [searchParams, events, selectedEvent]);

  const handleEventSelect = (event: Event) => {
    setSelectedEvent(event);
    setCurrentView('details');
  };

  const handleBooking = (slots: any) => {
    setSelectedSlots(slots);
    setCurrentView('booking');
  };

  const handleCartBooking = (reservedSlots: any[]) => {
    // Ensure all required fields are present in the slots
    const enhancedSlots = reservedSlots.map(slot => ({
      ...slot,
      // Provide default values for potentially missing fields
      activity_type: slot.activity_type || 'booking',
      time: slot.time || 'Unknown time',
      location_name: slot.location_name || 'Location',
      // Ensure participant_name is empty so it gets defaulted to payer name
      participant_name: slot.participant_name || '',
      horse_name: slot.horse_name || '',
      // Add any other required fields with defaults
    }));
    
    setSelectedSlots(enhancedSlots);
    setCurrentView('booking');
    setIsCartOpen(false);
  };

  const handleBackToEvents = () => {
    setSelectedEvent(null);
    setSelectedSlots([]);
    setCurrentView('events');
  };

  const handleViewPublicBookings = () => {
    setCurrentView('public');
  };

  const handleAdminLogin = () => {
    // Use the new routing system instead of state-based navigation
    if (user && isAdmin) {
      navigate('/admin/dashboard');
    } else {
      navigate('/admin/login');
    }
  };

  // Hero section for events view
  if (currentView === 'events') {
    return (
      <div className="min-h-screen bg-cream flex flex-col">
        {/* Version indicator */}
        <div className="fixed top-2 right-2 bg-blue-500 text-white px-3 py-1 rounded text-sm z-50">
          Latest Code ✓
        </div>
        
        {/* Header for navigation and user access */}
        <Header 
          onAdminLogin={handleAdminLogin}
          isAdmin={isAdmin}
        />
        
        {/* Hero Section */}
        <section className="flex flex-col items-center justify-center text-center px-6 pt-8 pb-12 bg-primary relative">
          <div className="max-w-3xl mx-auto w-full">
            <div className="max-w-3xl mx-auto w-full flex flex-col items-center">
              <img src="/NextRiderUpLogo.png" alt="Next Rider Up logo" className="w-52 h-52 mb-2 mx-auto" />
            </div>
            <p className="text-cream-dark mt-2 mb-8 max-w-xl mx-auto">
              Next Rider Up helps you book schooling rides the way they should be—organized, rider-friendly, and built for the show ring.
            </p>
            {/* Decorative Shape */}
            <svg className="absolute bottom-0 left-0 w-full" aria-hidden="true" viewBox="0 0 1440 80" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill="#f6edd9" d="M0,80 C480,0 960,160 1440,80 L1440,80 L0,80 Z"></path>
            </svg>
          </div>
        </section>

        {/* Events Section */}
        <section id="events-section" className="flex-1 bg-cream px-6 py-12">
          <div className="max-w-6xl mx-auto">
            <h2 className="font-playfair text-2xl md:text-3xl text-primary mb-8 text-center tracking-tight">
              Upcoming Events
            </h2>
            <EventList onEventSelect={handleEventSelect} />
          </div>
        </section>
      </div>
    );
  }

  // Other views with header
  return (
    <div className="min-h-screen bg-cream">
      {/* Version indicator */}
      <div className="fixed top-2 right-2 bg-blue-500 text-white px-3 py-1 rounded text-sm z-50">
        Latest Code ✓
      </div>
      
      <Header 
        onBackToEvents={handleBackToEvents}
        onViewPublicBookings={handleViewPublicBookings}
        onAdminLogin={handleAdminLogin}
        showBackButton={currentView !== 'events'}
        showPublicBookings={selectedEvent && currentView === 'details'}
        isAdmin={isAdmin}
      />
      
      <main className="container mx-auto px-4 py-8">
        {currentView === 'details' && selectedEvent && (
          <EventDetails 
            event={selectedEvent} 
            onBooking={handleBooking}
            initialSelectedDate={initialSelectedDate}
          />
        )}
        
        {currentView === 'booking' && (
          <BookingFlow 
            selectedSlots={selectedSlots}
            event={selectedEvent}
            onBack={() => setCurrentView('details')}
          />
        )}
        
        {currentView === 'public' && selectedEvent && (
          <PublicBookings 
            event={selectedEvent}
            onBack={() => setCurrentView('details')}
          />
        )}
      </main>
      
      {/* Always show floating cart icon when there are items */}
      <FloatingCartIcon onClick={() => setIsCartOpen(true)} />
      
      {/* Cart modal/sheet */}
      {isCartOpen && (
        <CartWidget 
          onProceedToBooking={handleCartBooking} 
          isOpen={isCartOpen}
          onClose={() => setIsCartOpen(false)}
        />
      )}
    </div>
  );
};

export default IndexPage;
