import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter, Routes, Route, useNavigate } from 'react-router-dom';
import { vi } from 'vitest';
import CreateEventPage from './CreateEventPage';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { useCreateEvent } from '@/hooks/useEvents';
import { useLevels } from '@/hooks/useLevels';
import { useOrganizers } from '@/hooks/useOrganizers';
import { useToast } from '@/hooks/use-toast';

// Mock dependencies
vi.mock('@supabase/auth-helpers-react');
vi.mock('@/hooks/useUserRoles');
vi.mock('@/hooks/useEvents');
vi.mock('@/hooks/useLevels');
vi.mock('@/hooks/useOrganizers');
vi.mock('@/hooks/use-toast');
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
  };
});
vi.mock('@/components/Header', () => ({
  Header: ({ onBackToEvents, showBackButton, isAdmin, userRole }: any) => (
    <div data-testid="header">
      <button onClick={onBackToEvents} data-testid="back-button">Back</button>
      <span data-testid="user-role">{userRole}</span>
      <span data-testid="is-admin">{String(isAdmin)}</span>
    </div>
  ),
}));

const mockUseSession = vi.mocked(useSession);
const mockUseUserRole = vi.mocked(useUserRole);
const mockUseCreateEvent = vi.mocked(useCreateEvent);
const mockUseLevels = vi.mocked(useLevels);
const mockUseOrganizers = vi.mocked(useOrganizers);
const mockUseToast = vi.mocked(useToast);
const mockNavigate = vi.mocked(useNavigate);

const mockLevels = [
  { id: 'level1', name: 'Intro', discipline: 'dressage' },
  { id: 'level2', name: 'Prelim', discipline: 'eventing' },
  { id: 'level3', name: 'Novice', discipline: 'eventing' },
  { id: 'level4', name: 'Beginner', discipline: 'show_jumping' },
];

const mockOrganizers = [
  { id: 'org1', full_name: 'John Doe', email: '<EMAIL>' },
  { id: 'org2', full_name: 'Jane Smith', email: '<EMAIL>' },
];

const createTestQueryClient = () => new QueryClient({
  defaultOptions: { queries: { retry: false }, mutations: { retry: false } },
});

const renderCreateEventPage = () => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter initialEntries={['/admin/create-event']}>
        <Routes>
          <Route path="/admin/create-event" element={<CreateEventPage />} />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('CreateEventPage', () => {
  const mockToast = vi.fn();
  const mockNavigateFn = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mocks
    mockUseSession.mockReturnValue({
      user: { id: 'user1', email: '<EMAIL>' },
      session: null,
    });
    
    mockUseUserRole.mockReturnValue({ userRole: 'organizer', isLoading: false });
    
    mockUseCreateEvent.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      error: null,
    });
    
    mockUseLevels.mockReturnValue({
      data: mockLevels,
      isLoading: false,
      error: null,
    });
    
    mockUseOrganizers.mockReturnValue({
      data: mockOrganizers,
      isLoading: false,
      error: null,
    });
    
    mockUseToast.mockReturnValue({ toast: mockToast });
    mockNavigate.mockReturnValue(mockNavigateFn);
  });

  describe('Authentication and Authorization', () => {
    it('redirects to login if not authenticated', () => {
      mockUseSession.mockReturnValue({
        user: null,
        session: null,
      });

      renderCreateEventPage();
      
      // The component should render a Navigate component
      // We can't easily test the redirect in this setup, but we can ensure it doesn't crash
    });

    it('redirects to login if user is not admin or organizer', () => {
      mockUseUserRole.mockReturnValue({ userRole: 'rider', isLoading: false });

      renderCreateEventPage();
      
      // The component should render a Navigate component
      // We can't easily test the redirect in this setup, but we can ensure it doesn't crash
    });

    it('renders for organizer users', () => {
      renderCreateEventPage();
      
      expect(screen.getByText('Create New Event')).toBeInTheDocument();
      expect(screen.getByTestId('is-admin')).toHaveTextContent('true');
    });

    it('renders for super admin users', () => {
      mockUseUserRole.mockReturnValue({ userRole: 'super_admin', isLoading: false });

      renderCreateEventPage();
      
      expect(screen.getByText('Create New Event')).toBeInTheDocument();
      expect(screen.getByTestId('is-admin')).toHaveTextContent('true');
    });
  });

  describe('Form Rendering', () => {
    it('renders all form fields', () => {
      renderCreateEventPage();
      
      expect(screen.getByLabelText('Event Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Event Type')).toBeInTheDocument();
      expect(screen.getByText('Event Organizer')).toBeInTheDocument();
      expect(screen.getByLabelText('Start Date')).toBeInTheDocument();
      expect(screen.getByLabelText('End Date')).toBeInTheDocument();
      expect(screen.getByText('Event Levels')).toBeInTheDocument();
    });

    it('shows organizer selection for super admins', () => {
      mockUseUserRole.mockReturnValue({
        userRole: 'super_admin',
        isLoading: false,
      });

      renderCreateEventPage();
      
      expect(screen.getByText('Event Organizer')).toBeInTheDocument();
      expect(screen.getByRole('combobox', { name: 'Event Organizer' })).toBeInTheDocument();
    });

    it('shows current user as organizer for non-super admins', () => {
      renderCreateEventPage();
      
      expect(screen.getByText('Event Organizer')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL> (You)')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('disables the create button when event name is blank', async () => {
      const user = userEvent.setup();
      renderCreateEventPage();
      
      // Wait for the component to be ready
      await waitFor(() => {
        expect(screen.getByLabelText('Event Name')).toBeInTheDocument();
      });
      
      // Don't fill in the name field - leave it empty
      // Fill in other required fields to ensure the validation is for the name
      await user.type(screen.getByLabelText('Start Date'), '2024-01-01');
      await user.type(screen.getByLabelText('End Date'), '2024-01-02');
      
      const createButton = screen.getByRole('button', { name: /create event/i });
      expect(createButton).toBeDisabled();
    });

    it('shows error when super admin tries to create event without selecting organizer', async () => {
      mockUseUserRole.mockReturnValue({
        userRole: 'super_admin',
        isLoading: false,
      });

      const user = userEvent.setup();
      renderCreateEventPage();
      
      // Fill in required fields
      await user.type(screen.getByLabelText('Event Name'), 'Test Event');
      await user.type(screen.getByLabelText('Start Date'), '2024-01-01');
      await user.type(screen.getByLabelText('End Date'), '2024-01-02');
      
      const createButton = screen.getByRole('button', { name: /create event/i });
      await user.click(createButton);
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Please select an organizer for this event',
          variant: 'destructive',
        });
      });
    });
  });

  describe('Form Submission', () => {
    it('submits form successfully with valid data', async () => {
      const mockMutate = vi.fn();
      mockUseCreateEvent.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
      } as any);

      const user = userEvent.setup();
      renderCreateEventPage();
      
      // Wait for the component to be ready and organizer to be set
      await waitFor(() => {
        expect(screen.getByLabelText('Event Name')).toBeInTheDocument();
      });
      
      // Fill in form
      await user.type(screen.getByLabelText('Event Name'), 'Test Event');
      await user.type(screen.getByLabelText('Start Date'), '2024-01-01');
      await user.type(screen.getByLabelText('End Date'), '2024-01-02');
      
      const createButton = screen.getByRole('button', { name: /create event/i });
      await user.click(createButton);
      
      // Wait for the mutation to be called
      await waitFor(() => {
        expect(mockMutate).toHaveBeenCalledWith({
          event: {
            name: 'Test Event',
            event_type: 'horse_trials',
            organizer_id: 'org1', // The component sets this based on the current user
            start_date: '2024-01-01',
            end_date: '2024-01-02',
          },
          levelIds: [],
        }, expect.any(Object));
      });
    });

    it('navigates to events list on successful creation', async () => {
      const mockMutate = vi.fn();
      mockUseCreateEvent.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
      } as any);

      const user = userEvent.setup();
      renderCreateEventPage();
      
      // Wait for the component to be ready
      await waitFor(() => {
        expect(screen.getByLabelText('Event Name')).toBeInTheDocument();
      });
      
      // Fill in form
      await user.type(screen.getByLabelText('Event Name'), 'Test Event');
      await user.type(screen.getByLabelText('Start Date'), '2024-01-01');
      await user.type(screen.getByLabelText('End Date'), '2024-01-02');
      
      const createButton = screen.getByRole('button', { name: /create event/i });
      await user.click(createButton);
      
      expect(mockMutate).toHaveBeenCalled();
    });
  });

  describe('Level Selection', () => {
    it('filters levels by discipline', async () => {
      renderCreateEventPage();
      
      // Check that discipline filter is present
      expect(screen.getByText('Filter by Discipline')).toBeInTheDocument();
      
      // Check that level selection is present
      expect(screen.getByText('Add Level')).toBeInTheDocument();
    });

    it('adds and removes levels', async () => {
      renderCreateEventPage();
      
      // Check that level selection is present
      expect(screen.getByText('Add Level')).toBeInTheDocument();
      
      // Check that no levels are selected initially (no "Selected Levels" section)
      expect(screen.queryByText('Selected Levels')).not.toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('navigates back when cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderCreateEventPage();
      
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);
      
      expect(mockNavigateFn).toHaveBeenCalledWith('/admin/events');
    });

    it('navigates back when header back button is clicked', async () => {
      const user = userEvent.setup();
      renderCreateEventPage();
      
      const backButton = screen.getByTestId('back-button');
      await user.click(backButton);
      
      expect(mockNavigateFn).toHaveBeenCalledWith('/admin/events');
    });
  });

  describe('Loading States', () => {
    it('shows loading state when creating event', () => {
      mockUseCreateEvent.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      renderCreateEventPage();
      
      expect(screen.getByText('Creating...')).toBeInTheDocument();
    });
  });
}); 