import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MemoryRouter, useNavigate } from 'react-router-dom';
import UsersManagementPage from './UsersManagementPage';

// Mock hooks and components
vi.mock('@supabase/auth-helpers-react', () => ({ useSession: vi.fn() }));
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
  };
});
vi.mock('@/components/Header', () => ({ Header: (props: any) => <div data-testid="header" {...props} /> }));
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className, ...props }: any) => <div data-testid="card" className={className} {...props}>{children}</div>,
  CardContent: ({ children, className }: any) => <div data-testid="card-content" className={className}>{children}</div>,
  CardHeader: ({ children }: any) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children, className }: any) => <div data-testid="card-title" className={className}>{children}</div>,
}));
vi.mock('@/components/ui/button', () => ({ Button: ({ children, onClick, disabled, ...props }: any) => <button onClick={onClick} disabled={disabled} {...props}>{children}</button> }));
vi.mock('@/components/ui/input', () => ({ Input: ({ onChange, value, ...props }: any) => <input onChange={onChange} value={value} {...props} /> }));
vi.mock('@/components/ui/label', () => ({ Label: ({ children, ...props }: any) => <label {...props}>{children}</label> }));
vi.mock('@/components/ui/badge', () => ({ Badge: ({ children, variant, className, ...props }: any) => <span data-testid="badge" data-variant={variant} className={className} {...props}>{children}</span> }));
vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange, ...props }: any) => <select value={value} onChange={(e) => onValueChange?.(e.target.value)} {...props}>{children}</select>,
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value, ...props }: any) => <option value={value} {...props}>{children}</option>,
  SelectTrigger: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  SelectValue: () => <div />,
}));
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open, onOpenChange, ...props }: any) => <div data-testid="dialog" data-open={open} {...props}>{children}</div>,
  DialogContent: ({ children, ...props }: any) => <div data-testid="dialog-content" {...props}>{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children, ...props }: any) => <div data-testid="dialog-title" {...props}>{children}</div>,
  DialogTrigger: ({ children }: any) => <div data-testid="dialog-trigger">{children}</div>,
}));
vi.mock('lucide-react', () => ({
  ArrowLeft: () => <span>ArrowLeft</span>,
  User: () => <span>UserIcon</span>,
  Shield: () => <span>ShieldIcon</span>,
  Mail: () => <span>MailIcon</span>,
  Calendar: () => <span>CalendarIcon</span>,
  Search: () => <span>SearchIcon</span>,
  Plus: () => <span>PlusIcon</span>,
  Edit: () => <span>EditIcon</span>,
  Save: () => <span>SaveIcon</span>,
  X: () => <span>XIcon</span>,
  Trash2: () => <span>Trash2Icon</span>,
}));

vi.mock('@/hooks/use-toast', () => ({ useToast: vi.fn() }));
vi.mock('@/hooks/useAllUsers', () => ({
  useAllUsersComprehensive: vi.fn(),
  useDeleteUser: vi.fn(),
  useUpdateUserProfile: vi.fn(),
}));
vi.mock('@/hooks/useUserRoles', () => ({
  useUpdateUserRole: vi.fn(),
  useCreateUser: vi.fn(),
  useUserRole: vi.fn(),
}));
vi.mock('@/integrations/supabase/client', () => ({ supabase: {}, supabaseAdmin: {} }));
vi.mock('@/types/errors', () => ({ getErrorMessage: (e: any) => e?.message || 'Error' }));

import { useSession } from '@supabase/auth-helpers-react';
import { useAllUsersComprehensive, useDeleteUser, useUpdateUserProfile } from '@/hooks/useAllUsers';
import { useUpdateUserRole, useCreateUser, useUserRole } from '@/hooks/useUserRoles';
import { useToast } from '@/hooks/use-toast';

const mockUseSession = vi.mocked(useSession);
const mockUseAllUsersComprehensive = vi.mocked(useAllUsersComprehensive);
const mockUseUserRole = vi.mocked(useUserRole);
const mockUseUpdateUserRole = vi.mocked(useUpdateUserRole);
const mockUseCreateUser = vi.mocked(useCreateUser);
const mockUseDeleteUser = vi.mocked(useDeleteUser);
const mockUseUpdateUserProfile = vi.mocked(useUpdateUserProfile);
const mockUseToast = vi.mocked(useToast);
const mockNavigate = vi.mocked(useNavigate);

const createQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>{component}</MemoryRouter>
    </QueryClientProvider>
  );
};

const mockUsers = [
  {
    auth_user_id: 'user-1',
    full_name: 'Alice Smith',
    auth_email: '<EMAIL>',
    phone: '************',
    user_role: 'user',
    auth_created_at: '2024-01-01T00:00:00Z',
  },
  {
    auth_user_id: 'user-2',
    full_name: 'Bob Jones',
    auth_email: '<EMAIL>',
    phone: null,
    user_role: 'organizer',
    auth_created_at: '2024-01-02T00:00:00Z',
  },
  {
    auth_user_id: 'user-3',
    full_name: 'Charlie Admin',
    auth_email: '<EMAIL>',
    phone: '************',
    user_role: 'super_admin',
    auth_created_at: '2024-01-03T00:00:00Z',
  },
];

describe('UsersManagementPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mocks
    mockUseSession.mockReturnValue({ user: { id: 'user-1' } });
    mockUseAllUsersComprehensive.mockReturnValue({ data: mockUsers, isLoading: false });
    mockUseUserRole.mockReturnValue({ userRole: 'super_admin', isLoading: false });
    mockUseUpdateUserRole.mockReturnValue({ mutate: vi.fn(), isPending: false });
    mockUseCreateUser.mockReturnValue({ mutate: vi.fn(), isPending: false });
    mockUseDeleteUser.mockReturnValue({ mutate: vi.fn(), isPending: false });
    mockUseUpdateUserProfile.mockReturnValue({ mutate: vi.fn(), isPending: false });
    mockUseToast.mockReturnValue({ toast: vi.fn() });
    mockNavigate.mockReturnValue(vi.fn());
  });

  describe('Authentication and Access', () => {
    it('renders login prompt if not logged in', () => {
      mockUseSession.mockReturnValue(null);
      renderWithProviders(<UsersManagementPage />);
      expect(screen.getByTestId('login-prompt')).toBeInTheDocument();
    });

    it('renders header and main UI if logged in', () => {
      renderWithProviders(<UsersManagementPage />);
      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('page-title')).toHaveTextContent('Users Management');
      expect(screen.getByTestId('page-description')).toHaveTextContent('Manage user accounts and roles across the platform');
    });
  });

  describe('Loading and Empty States', () => {
    it('shows loading state when users are loading', () => {
      mockUseAllUsersComprehensive.mockReturnValue({ data: undefined, isLoading: true });
      renderWithProviders(<UsersManagementPage />);
      expect(screen.getByTestId('loading-users')).toBeInTheDocument();
    });

    it('shows empty state when no users exist', () => {
      mockUseAllUsersComprehensive.mockReturnValue({ data: [], isLoading: false });
      renderWithProviders(<UsersManagementPage />);
      expect(screen.getByTestId('no-users-found')).toBeInTheDocument();
    });
  });

  describe('User List Rendering', () => {
    it('renders all users with correct information', () => {
      renderWithProviders(<UsersManagementPage />);
      
      expect(screen.getByTestId('user-name-user-1')).toHaveTextContent('Alice Smith');
      expect(screen.getByTestId('user-email-user-1')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('user-phone-user-1')).toHaveTextContent('Phone: ************');
      
      expect(screen.getByTestId('user-name-user-2')).toHaveTextContent('Bob Jones');
      expect(screen.getByTestId('user-email-user-2')).toHaveTextContent('<EMAIL>');
      
      expect(screen.getByTestId('user-name-user-3')).toHaveTextContent('Charlie Admin');
      expect(screen.getByTestId('user-email-user-3')).toHaveTextContent('<EMAIL>');
    });

    it('displays correct user count in title', () => {
      renderWithProviders(<UsersManagementPage />);
      expect(screen.getByTestId('card-title')).toHaveTextContent('Users (3)');
    });

    it('renders role badges with correct styling', () => {
      renderWithProviders(<UsersManagementPage />);
      
      // Check that role badges exist for each user
      expect(screen.getByTestId('user-role-badge-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('user-role-badge-user-2')).toBeInTheDocument();
      expect(screen.getByTestId('user-role-badge-user-3')).toBeInTheDocument();
      
      // Check role display names using getAllByText for multiple instances
      expect(screen.getAllByText('User')).toHaveLength(3); // Two in selects, one in badge
      expect(screen.getAllByText('Organizer')).toHaveLength(3); // Two in selects, one in badge
      expect(screen.getAllByText('Super Admin')).toHaveLength(3); // Two in selects, one in badge
    });

    it('displays join dates correctly', () => {
      renderWithProviders(<UsersManagementPage />);
      
      expect(screen.getByTestId('user-join-date-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('user-join-date-user-2')).toBeInTheDocument();
      expect(screen.getByTestId('user-join-date-user-3')).toBeInTheDocument();
    });
  });

  describe('Search and Filter Functionality', () => {
    it('renders search input with correct placeholder', () => {
      renderWithProviders(<UsersManagementPage />);
      
      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toBeInTheDocument();
      expect(searchInput).toHaveAttribute('placeholder', 'Search by name or email...');
    });

    it('renders role filter with all options', () => {
      renderWithProviders(<UsersManagementPage />);
      
      const roleFilter = screen.getByTestId('role-filter-select');
      expect(roleFilter).toBeInTheDocument();
      
      // Check that all role options are available using getAllByText for multiple instances
      expect(screen.getByText('All Roles')).toBeInTheDocument();
      expect(screen.getAllByText('Super Admin')).toHaveLength(3); // Two in selects, one in badge
      expect(screen.getAllByText('Organizer')).toHaveLength(3); // Two in selects, one in badge
      expect(screen.getAllByText('User')).toHaveLength(3); // Two in selects, one in badge
    });
  });

  describe('User Actions', () => {
    it('renders edit user buttons for each user', () => {
      renderWithProviders(<UsersManagementPage />);
      expect(screen.getByTestId('edit-user-btn-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('edit-user-btn-user-2')).toBeInTheDocument();
      expect(screen.getByTestId('edit-user-btn-user-3')).toBeInTheDocument();
    });

    it('renders delete buttons for each user', () => {
      renderWithProviders(<UsersManagementPage />);
      
      expect(screen.getByTestId('delete-user-btn-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('delete-user-btn-user-2')).toBeInTheDocument();
      expect(screen.getByTestId('delete-user-btn-user-3')).toBeInTheDocument();
    });

    it('shows create user button', () => {
      renderWithProviders(<UsersManagementPage />);
      
      expect(screen.getByTestId('create-user-btn')).toBeInTheDocument();
    });
  });

  describe('Dialog States', () => {
    it('renders create user dialog with correct elements', () => {
      renderWithProviders(<UsersManagementPage />);
      
      expect(screen.getByTestId('create-user-dialog')).toBeInTheDocument();
      expect(screen.getByTestId('create-user-dialog-title')).toHaveTextContent('Create New User');
      expect(screen.getByTestId('create-user-email-input')).toBeInTheDocument();
      expect(screen.getByTestId('create-user-name-input')).toBeInTheDocument();
      expect(screen.getByTestId('create-user-role-select')).toBeInTheDocument();
    });

    it('renders delete confirmation dialog with correct elements', async () => {
      const user = userEvent.setup();
      renderWithProviders(<UsersManagementPage />);
      // Simulate clicking the delete button for the first user
      await user.click(screen.getByTestId('delete-user-btn-user-1'));
      
      expect(screen.getByTestId('delete-user-dialog')).toBeInTheDocument();
      expect(screen.getByTestId('delete-user-dialog-title')).toHaveTextContent('Confirm Delete');
      expect(screen.getByTestId('confirm-delete-btn')).toBeInTheDocument();
      expect(screen.getByTestId('cancel-delete-btn')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('shows loading state during role update', () => {
      mockUseUpdateUserRole.mockReturnValue({ mutate: vi.fn(), isPending: true });
      
      renderWithProviders(<UsersManagementPage />);
      
      // The component should handle loading state appropriately
      expect(screen.getByTestId('header')).toBeInTheDocument();
    });

    it('shows loading state during user creation', () => {
      mockUseCreateUser.mockReturnValue({ mutate: vi.fn(), isPending: true });
      
      renderWithProviders(<UsersManagementPage />);
      
      // The component should handle loading state appropriately
      expect(screen.getByTestId('header')).toBeInTheDocument();
    });

    it('shows loading state during user deletion', () => {
      mockUseDeleteUser.mockReturnValue({ mutate: vi.fn(), isPending: true });
      
      renderWithProviders(<UsersManagementPage />);
      
      // The component should handle loading state appropriately
      expect(screen.getByTestId('header')).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('renders back to dashboard button', () => {
      renderWithProviders(<UsersManagementPage />);
      
      const backButton = screen.getByTestId('back-to-dashboard-btn');
      expect(backButton).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles missing user data gracefully', () => {
      mockUseAllUsersComprehensive.mockReturnValue({ data: undefined, isLoading: false });
      
      renderWithProviders(<UsersManagementPage />);
      
      // Should show empty state or loading
      expect(screen.getByTestId('header')).toBeInTheDocument();
    });

    it('handles empty user array', () => {
      mockUseAllUsersComprehensive.mockReturnValue({ data: [], isLoading: false });
      
      renderWithProviders(<UsersManagementPage />);
      
      expect(screen.getByTestId('no-users-found')).toBeInTheDocument();
    });
  });
}); 