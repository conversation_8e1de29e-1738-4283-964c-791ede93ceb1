import React, { useState, useEffect } from 'react';
import { AdminLogin as AdminLoginComponent } from '@/components/AdminLogin';
import { useNavigate } from 'react-router-dom';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { getErrorMessage } from '@/types/errors';

const AdminLoginPage = () => {
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Effect to handle navigation after successful authentication and role fetch
  useEffect(() => {
    if (!isLoading && user && userRole) {
      console.log('User authenticated with role:', userRole);
      if (userRole === 'super_admin' || userRole === 'organizer') {
        navigate('/admin/dashboard');
      } else {
        toast({
          title: "Access Denied",
          description: "You don't have permission to access the admin area.",
          variant: "destructive"
        });
      }
    }
  }, [user, userRole, isLoading, navigate, toast]);

  const handleLoginSuccess = async (email: string, password: string) => {
    try {
      console.log('Attempting to sign in...');
      setIsLoading(true);
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      }
      // Don't navigate here - let the useEffect handle it after role is fetched
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <main className="container mx-auto px-4 py-8">
        <AdminLoginComponent 
          onLogin={handleLoginSuccess}
          onBack={handleBack}
        />
      </main>
    </div>
  );
};

export default AdminLoginPage;
