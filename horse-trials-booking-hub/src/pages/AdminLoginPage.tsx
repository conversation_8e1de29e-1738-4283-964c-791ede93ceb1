
import React, { useEffect } from 'react';
import { AdminLogin as AdminLoginComponent } from '@/components/AdminLogin';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/components/ui/use-toast';

const AdminLoginPage = () => {
  const navigate = useNavigate();
  const { signIn, userRole, user, loading } = useAuth();
  const { toast } = useToast();

  // Effect to handle navigation after successful authentication and role fetch
  useEffect(() => {
    if (!loading && user && userRole) {
      console.log('User authenticated with role:', userRole);
      if (userRole === 'admin' || userRole === 'organizer') {
        navigate('/admin/dashboard');
      } else {
        toast({
          title: "Access Denied",
          description: "You don't have permission to access the admin area.",
          variant: "destructive"
        });
      }
    }
  }, [user, userRole, loading, navigate, toast]);

  const handleLoginSuccess = async (email: string, password: string) => {
    try {
      console.log('Attempting to sign in...');
      await signIn(email, password);
      // Don't navigate here - let the useEffect handle it after role is fetched
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  const handleBack = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <main className="container mx-auto px-4 py-8">
        <AdminLoginComponent 
          onLogin={handleLoginSuccess}
          onBack={handleBack}
        />
      </main>
    </div>
  );
};

export default AdminLoginPage;
