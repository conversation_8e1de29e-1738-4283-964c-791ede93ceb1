import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';
import SuperAdminDashboardPage from './SuperAdminDashboardPage';
import { createTestQueryClient } from '@/test/test-utils';

// Mock the hooks
const mockUseAccessibleEvents = vi.fn();
const mockUseUserRole = vi.fn();
const mockUseSession = vi.fn();
const mockNavigate = vi.fn();

vi.mock('@/hooks/useUserRoles', () => ({
  useAccessibleEvents: () => mockUseAccessibleEvents(),
  useUserRole: () => mockUseUserRole(),
}));

vi.mock('@supabase/auth-helpers-react', () => ({
  useSession: () => mockUseSession(),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock the pages that will be navigated to
vi.mock('./UsersManagementPage', () => ({
  default: () => <div data-testid="users-management-page">Users Management Page</div>,
}));

vi.mock('./LevelsManagementPage', () => ({
  default: () => <div data-testid="levels-management-page">Levels Management Page</div>,
}));

vi.mock('./DressageTestsManagementPage', () => ({
  default: () => <div data-testid="dressage-tests-management-page">Dressage Tests Management Page</div>,
}));

vi.mock('./OrganizersManagementPage', () => ({
  default: () => <div data-testid="organizers-management-page">Organizers Management Page</div>,
}));

vi.mock('./CreateEventPage', () => ({
  default: () => <div data-testid="create-event-page">Create Event Page</div>,
}));

vi.mock('./AdminDashboardPage', () => ({
  default: () => <div data-testid="admin-dashboard-page">Admin Dashboard Page</div>,
}));

// Mock Radix Select to avoid pointer-events and accessibility issues
vi.mock('@/components/ui/select', () => {
  const React = require('react');
  return {
    Select: ({ children, value, onValueChange, 'data-testid': testId }) => (
      <select
        value={value}
        onChange={e => onValueChange?.(e.target.value)}
        data-testid={testId}
      >
        {children}
      </select>
    ),
    SelectContent: ({ children }) => <>{children}</>,
    SelectItem: ({ children, value }) => <option value={value}>{children}</option>,
    SelectTrigger: ({ children, ...props }) => <div {...props}>{children}</div>,
    SelectValue: ({ placeholder }) => <span>{placeholder}</span>,
  };
});

const renderWithRouter = () => {
  return render(
    <QueryClientProvider client={createTestQueryClient()}>
      <MemoryRouter initialEntries={['/super-admin']}>
        <Routes>
          <Route path="/super-admin" element={<SuperAdminDashboardPage />} />
          <Route path="/admin/users" element={<div data-testid="users-management-page">Users Management Page</div>} />
          <Route path="/admin/levels" element={<div data-testid="levels-management-page">Levels Management Page</div>} />
          <Route path="/admin/dressage-tests" element={<div data-testid="dressage-tests-management-page">Dressage Tests Management Page</div>} />
          <Route path="/admin/organizers" element={<div data-testid="organizers-management-page">Organizers Management Page</div>} />
          <Route path="/admin/events/create" element={<div data-testid="create-event-page">Create Event Page</div>} />
          <Route path="/admin/events/:eventId" element={<div data-testid="event-detail-page">Event Detail Page</div>} />
          <Route path="/" element={<div data-testid="home-page">Home Page</div>} />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('SuperAdminDashboardPage', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
    
    // Default mocks
    mockUseSession.mockReturnValue({
      user: { 
        id: 'user1', 
        email: '<EMAIL>',
        user_metadata: {
          full_name: 'Admin User'
        }
      },
    });
    mockUseUserRole.mockReturnValue({
      userRole: 'super_admin',
    });
    mockUseAccessibleEvents.mockReturnValue({
      data: [],
      isLoading: false,
    });
  });

  it('shows loading state when checking permissions', () => {
    mockUseUserRole.mockReturnValue({
      userRole: null,
    });

    renderWithRouter();

    expect(screen.getByText('Checking permissions...')).toBeInTheDocument();
    expect(screen.getByTestId('loading-shield-icon')).toBeInTheDocument();
  });

  it('shows access denied for non-super admin users', () => {
    mockUseUserRole.mockReturnValue({
      userRole: 'organizer',
    });

    renderWithRouter();

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
    expect(screen.getByText("You don't have permission to access the Super Admin Dashboard.")).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Back to Home/ })).toBeInTheDocument();
  });

  it('shows access denied for regular users', () => {
    mockUseUserRole.mockReturnValue({
      userRole: 'user',
    });

    renderWithRouter();

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
    expect(screen.getByText("You don't have permission to access the Super Admin Dashboard.")).toBeInTheDocument();
  });

  it('displays stats cards correctly', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
      {
        id: '2',
        name: 'Event 2',
        start_date: '2024-01-03',
        end_date: '2024-01-04',
        is_active: false,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
      {
        id: '3',
        name: 'Event 3',
        start_date: '2024-01-05',
        end_date: '2024-01-06',
        is_active: true,
        organizer: {
          id: 'org2',
          full_name: 'Jane Smith',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    // Check stats cards
    expect(screen.getByText('Total Events')).toBeInTheDocument();
    expect(screen.getByText('Active Events')).toBeInTheDocument();
    expect(screen.getByText('Organizers')).toBeInTheDocument();
    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    
    // Check the specific numbers in the stats cards
    const totalEventsCard = screen.getByText('Total Events').closest('div')?.parentElement;
    const activeEventsCard = screen.getByText('Active Events').closest('div')?.parentElement;
    const organizersCard = screen.getByText('Organizers').closest('div')?.parentElement;
    
    expect(totalEventsCard).toHaveTextContent('3'); // Total events
    expect(activeEventsCard).toHaveTextContent('2'); // Active events (2 out of 3)
    expect(organizersCard).toHaveTextContent('2'); // Unique organizers (John Doe and Jane Smith)
  });

  it('displays events list with correct information', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Active Event',
        start_date: '2024-06-01',
        end_date: '2024-06-03',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
      {
        id: '2',
        name: 'Inactive Event',
        start_date: '2024-12-15',
        end_date: '2024-12-16',
        is_active: false,
        organizer: {
          id: 'org2',
          full_name: 'Jane Smith',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    // Check event names
    expect(screen.getByText('Active Event')).toBeInTheDocument();
    expect(screen.getByText('Inactive Event')).toBeInTheDocument();

    // Check dates using within() on individual event cards
    const eventCard1 = screen.getByTestId('event-card-1');
    const eventCard2 = screen.getByTestId('event-card-2');
    
    expect(within(eventCard1).getByText(/6\/1\/2024/)).toBeInTheDocument();
    expect(within(eventCard1).getByText(/6\/3\/2024/)).toBeInTheDocument();
    expect(within(eventCard2).getByText(/12\/15\/2024/)).toBeInTheDocument();
    expect(within(eventCard2).getByText(/12\/16\/2024/)).toBeInTheDocument();

    // Check organizer information
    expect(within(eventCard1).getByText(/John Doe/)).toBeInTheDocument();
    expect(within(eventCard2).getByText(/Jane Smith/)).toBeInTheDocument();

    // Check status badges
    expect(within(eventCard1).getByText('Active')).toBeInTheDocument();
    expect(within(eventCard2).getByText('Inactive')).toBeInTheDocument();
  });

  it('shows loading state for events', () => {
    mockUseAccessibleEvents.mockReturnValue({
      data: [],
      isLoading: true,
    });

    renderWithRouter();

    expect(screen.getByText('Loading events...')).toBeInTheDocument();
  });

  it('shows empty state when no events exist', () => {
    mockUseAccessibleEvents.mockReturnValue({
      data: [],
      isLoading: false,
    });

    renderWithRouter();

    expect(screen.getByText('No events found')).toBeInTheDocument();
    expect(screen.getByText('Create the first event to get started.')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Create First Event/ })).toBeInTheDocument();
  });

  it('shows filtered empty state when filters are applied', async () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Test Event',
        start_date: '2024-06-01',
        end_date: '2024-06-03',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    const searchInput = screen.getByPlaceholderText('Search by event name or organizer...');
    await user.type(searchInput, 'NonExistentEvent');

    // Use within() on the empty state container
    const emptyState = screen.getByTestId('empty-events-state');
    expect(within(emptyState).getByText('No events found')).toBeInTheDocument();
    expect(within(emptyState).getByText('Try adjusting your filters.')).toBeInTheDocument();
  });

  it('filters events by search term', async () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Summer Horse Trials',
        start_date: '2024-06-01',
        end_date: '2024-06-03',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
      {
        id: '2',
        name: 'Winter Dressage',
        start_date: '2024-12-15',
        end_date: '2024-12-16',
        is_active: true,
        organizer: {
          id: 'org2',
          full_name: 'Jane Smith',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    const searchInput = screen.getByPlaceholderText('Search by event name or organizer...');
    await user.type(searchInput, 'Summer');

    // Use within() on the events list container
    const eventsList = screen.getByTestId('events-list');
    expect(within(eventsList).getByText('Summer Horse Trials')).toBeInTheDocument();
    expect(within(eventsList).queryByText('Winter Dressage')).not.toBeInTheDocument();
  });

  it('filters events by status', async () => {
    const mockEvents = [
      { id: '1', name: 'Event 1', is_active: true, organizer: { id: 'org1', name: 'Org 1' } },
      { id: '2', name: 'Event 2', is_active: false, organizer: { id: 'org2', name: 'Org 2' } },
    ];
    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });
    renderWithRouter();

    // Use test id for status filter
    const statusSelect = screen.getByTestId('status-filter');
    expect(statusSelect).toBeInTheDocument();
    await user.selectOptions(statusSelect, 'active');
    expect(screen.getByText('Event 1')).toBeInTheDocument();
    expect(screen.queryByText('Event 2')).not.toBeInTheDocument();
  });

  it('filters events by organizer', async () => {
    const mockEvents = [
      { id: '1', name: 'Event 1', is_active: true, organizer: { id: 'org1', name: 'Org 1' } },
      { id: '2', name: 'Event 2', is_active: true, organizer: { id: 'org2', name: 'Org 2' } },
    ];
    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });
    renderWithRouter();

    // Use test id for organizer filter
    const organizerSelect = screen.getByTestId('organizer-filter');
    expect(organizerSelect).toBeInTheDocument();
    await user.selectOptions(organizerSelect, 'org2');
    expect(screen.getByText('Event 2')).toBeInTheDocument();
    expect(screen.queryByText('Event 1')).not.toBeInTheDocument();
  });

  it('navigates to event details when event is clicked', async () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Summer Horse Trials',
        start_date: '2024-06-01',
        end_date: '2024-06-03',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    const eventButton = screen.getByRole('button', { name: /Summer Horse Trials/ });
    await user.click(eventButton);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/events/1');
  });

  it('navigates to create event page when create button is clicked', async () => {
    renderWithRouter();

    const createButtons = screen.getAllByRole('button', { name: /Create Event/ });
    await user.click(createButtons[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/events/create');
  });

  it('navigates to users management when manage users button is clicked', async () => {
    renderWithRouter();

    const manageUsersButtons = screen.getAllByRole('button', { name: /Manage Users/ });
    await user.click(manageUsersButtons[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/users');
  });

  it('navigates to levels management when manage levels button is clicked', async () => {
    renderWithRouter();

    const manageLevelsButtons = screen.getAllByRole('button', { name: /Manage Levels/ });
    await user.click(manageLevelsButtons[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/levels');
  });

  it('navigates to dressage tests management when manage tests button is clicked', async () => {
    renderWithRouter();

    const manageTestsButtons = screen.getAllByRole('button', { name: /Manage Tests/ });
    await user.click(manageTestsButtons[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/dressage-tests');
  });

  it('navigates to organizers management when manage organizers button is clicked', async () => {
    renderWithRouter();

    const manageOrganizersButtons = screen.getAllByRole('button', { name: /Manage Organizers/ });
    await user.click(manageOrganizersButtons[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/organizers');
  });

  it('navigates back to home when back button is clicked', async () => {
    renderWithRouter();

    const backButton = screen.getByTestId('back-button');
    await user.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('shows correct user role in header', () => {
    renderWithRouter();

    expect(screen.getByText('Super Admin Dashboard')).toBeInTheDocument();
  });

  it('displays app-wide settings section', () => {
    renderWithRouter();

    expect(screen.getByText('App-Wide Settings')).toBeInTheDocument();
    const manageLevelsButtons = screen.getAllByRole('button', { name: /Manage Levels/ });
    expect(manageLevelsButtons).toHaveLength(2);
    
    const manageTestsButtons = screen.getAllByRole('button', { name: /Manage Tests/ });
    expect(manageTestsButtons).toHaveLength(1);
    
    const manageUsersButtons = screen.getAllByRole('button', { name: /Manage Users/ });
    expect(manageUsersButtons).toHaveLength(2);
  });

  it('handles events with missing organizer information gracefully', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-06-01',
        end_date: '2024-06-03',
        is_active: true,
        organizer: null, // No organizer
      },
      {
        id: '2',
        name: 'Event 2',
        start_date: '2024-12-15',
        end_date: '2024-12-16',
        is_active: false,
        organizer: {
          id: 'org1',
          full_name: null, // No full name
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    // Should still display the events
    expect(screen.getByText('Event 1')).toBeInTheDocument();
    expect(screen.getByText('Event 2')).toBeInTheDocument();
    
    // Should show email when full name is not available
    expect(screen.getByText('Organizer: <EMAIL>')).toBeInTheDocument();
    
    // Should not show organizer info for events without organizer
    expect(screen.queryByText('Organizer:')).not.toBeInTheDocument();
  });

  it('updates event count when filters are applied', async () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-06-01',
        end_date: '2024-06-03',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
      {
        id: '2',
        name: 'Event 2',
        start_date: '2024-12-15',
        end_date: '2024-12-16',
        is_active: false,
        organizer: {
          id: 'org2',
          full_name: 'Jane Smith',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    // Initially shows all events
    const eventsList = screen.getByTestId('events-list');
    expect(within(eventsList).getByText(/All Events \(2\)/)).toBeInTheDocument();

    // Filter to show only active events
    const statusSelect = screen.getByTestId('status-filter');
    await user.selectOptions(statusSelect, 'active');

    // Should update the count
    expect(within(eventsList).getByText(/All Events \(1\)/)).toBeInTheDocument();
  });
}); 