import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';
import SuperAdminDashboardPage from './SuperAdminDashboardPage';
import { createTestQueryClient } from '@/test/test-utils';

// Mock the hooks
const mockUseAccessibleEvents = vi.fn();
const mockUseUserRole = vi.fn();
const mockUseSession = vi.fn();
const mockNavigate = vi.fn();

vi.mock('@/hooks/useUserRoles', () => ({
  useAccessibleEvents: () => mockUseAccessibleEvents(),
  useUserRole: () => mockUseUserRole(),
}));

vi.mock('@supabase/auth-helpers-react', () => ({
  useSession: () => mockUseSession(),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock the pages that will be navigated to
vi.mock('./UsersManagementPage', () => ({
  default: () => <div data-testid="users-management-page">Users Management Page</div>,
}));

vi.mock('./LevelsManagementPage', () => ({
  default: () => <div data-testid="levels-management-page">Levels Management Page</div>,
}));

vi.mock('./DressageTestsManagementPage', () => ({
  default: () => <div data-testid="dressage-tests-management-page">Dressage Tests Management Page</div>,
}));

vi.mock('./OrganizersManagementPage', () => ({
  default: () => <div data-testid="organizers-management-page">Organizers Management Page</div>,
}));



// Mock Radix Select to avoid pointer-events and accessibility issues
vi.mock('@/components/ui/select', () => {
  const React = require('react');
  return {
    Select: ({ children, value, onValueChange, 'data-testid': testId }) => (
      <select
        value={value}
        onChange={e => onValueChange?.(e.target.value)}
        data-testid={testId}
      >
        {children}
      </select>
    ),
    SelectContent: ({ children }) => <>{children}</>,
    SelectItem: ({ children, value }) => <option value={value}>{children}</option>,
    SelectTrigger: ({ children, ...props }) => <div {...props}>{children}</div>,
    SelectValue: ({ placeholder }) => <span>{placeholder}</span>,
  };
});

const renderWithRouter = () => {
  return render(
    <QueryClientProvider client={createTestQueryClient()}>
      <MemoryRouter initialEntries={['/super-admin']}>
        <Routes>
          <Route path="/super-admin" element={<SuperAdminDashboardPage />} />
          <Route path="/admin/users" element={<div data-testid="users-management-page">Users Management Page</div>} />
          <Route path="/admin/levels" element={<div data-testid="levels-management-page">Levels Management Page</div>} />
          <Route path="/admin/dressage-tests" element={<div data-testid="dressage-tests-management-page">Dressage Tests Management Page</div>} />
          <Route path="/admin/organizers" element={<div data-testid="organizers-management-page">Organizers Management Page</div>} />
          <Route path="/" element={<div data-testid="home-page">Home Page</div>} />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('SuperAdminDashboardPage', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
    
    // Default mocks
    mockUseSession.mockReturnValue({
      user: { 
        id: 'user1', 
        email: '<EMAIL>',
        user_metadata: {
          full_name: 'Admin User'
        }
      },
    });
    mockUseUserRole.mockReturnValue({
      userRole: 'super_admin',
    });
    mockUseAccessibleEvents.mockReturnValue({
      data: [],
      isLoading: false,
    });
  });

  it('shows loading state when checking permissions', () => {
    mockUseUserRole.mockReturnValue({
      userRole: null,
    });

    renderWithRouter();

    expect(screen.getByText('Checking permissions...')).toBeInTheDocument();
    expect(screen.getByTestId('loading-shield-icon')).toBeInTheDocument();
  });

  it('shows access denied for non-super admin users', () => {
    mockUseUserRole.mockReturnValue({
      userRole: 'organizer',
    });

    renderWithRouter();

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
    expect(screen.getByText("You don't have permission to access the Super Admin Dashboard.")).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Back to Home/ })).toBeInTheDocument();
  });

  it('shows access denied for regular users', () => {
    mockUseUserRole.mockReturnValue({
      userRole: 'user',
    });

    renderWithRouter();

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
    expect(screen.getByText("You don't have permission to access the Super Admin Dashboard.")).toBeInTheDocument();
  });





  it('navigates to users management when manage users button is clicked', async () => {
    renderWithRouter();

    const manageUsersButtons = screen.getAllByRole('button', { name: /Manage Users/ });
    await user.click(manageUsersButtons[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/users');
  });

  it('navigates to levels management when manage levels button is clicked', async () => {
    renderWithRouter();

    const manageLevelsButtons = screen.getAllByRole('button', { name: /Manage Levels/ });
    await user.click(manageLevelsButtons[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/levels');
  });

  it('navigates to dressage tests management when manage tests button is clicked', async () => {
    renderWithRouter();

    const manageTestsButtons = screen.getAllByRole('button', { name: /Manage Tests/ });
    await user.click(manageTestsButtons[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/dressage-tests');
  });

  it('navigates to organizers management when manage organizers button is clicked', async () => {
    renderWithRouter();

    const manageOrganizersButtons = screen.getAllByRole('button', { name: /Manage Organizers/ });
    await user.click(manageOrganizersButtons[0]);

    expect(mockNavigate).toHaveBeenCalledWith('/admin/organizers');
  });

  it('navigates back to home when back button is clicked', async () => {
    renderWithRouter();

    const backButton = screen.getByTestId('back-button');
    await user.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('shows correct user role in header', () => {
    renderWithRouter();

    expect(screen.getByText('Super Admin Dashboard')).toBeInTheDocument();
  });

  it('displays app-wide settings section', () => {
    renderWithRouter();

    expect(screen.getByText('App-Wide Settings')).toBeInTheDocument();
    const manageLevelsButtons = screen.getAllByRole('button', { name: /Manage Levels/ });
    expect(manageLevelsButtons).toHaveLength(2);

    const manageTestsButtons = screen.getAllByRole('button', { name: /Manage Tests/ });
    expect(manageTestsButtons).toHaveLength(1);

    const manageUsersButtons = screen.getAllByRole('button', { name: /Manage Users/ });
    expect(manageUsersButtons).toHaveLength(2);
  });
}); 