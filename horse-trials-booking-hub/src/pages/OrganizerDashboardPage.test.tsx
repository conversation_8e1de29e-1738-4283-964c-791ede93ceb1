import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';
import OrganizerDashboardPage from './OrganizerDashboardPage';
import { createTestQueryClient } from '@/test/test-utils';

// Mock the hooks
const mockUseAccessibleEvents = vi.fn();
const mockUseUserRole = vi.fn();
const mockNavigate = vi.fn();

vi.mock('@/hooks/useUserRoles', () => ({
  useAccessibleEvents: () => mockUseAccessibleEvents(),
  useUserRole: () => mockUseUserRole(),
}));

// Mock components
vi.mock('@/components/Header', () => ({
  Header: ({ onBackToEvents, showBackButton, userRole }: any) => (
    <header data-testid="header">
      <h1>Organizer Dashboard Header</h1>
      {showBackButton && <button data-testid="back-button" onClick={onBackToEvents}>Back</button>}
      <span data-testid="user-role">{userRole}</span>
    </header>
  ),
}));

const renderWithRouter = () => {
  return render(
    <QueryClientProvider client={createTestQueryClient()}>
      <MemoryRouter initialEntries={["/admin/organizer-dashboard"]}>
        <Routes>
          <Route path="/admin/organizer-dashboard" element={<OrganizerDashboardPage />} />
          <Route path="/admin/profile" element={<div data-testid="profile-page">Profile Settings</div>} />
          <Route path="/" element={<div data-testid="home-page">Home</div>} />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('OrganizerDashboardPage', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
    
    // Default mocks
    mockUseAccessibleEvents.mockReturnValue({
      data: [],
      isLoading: false,
    });
    
    mockUseUserRole.mockReturnValue({
      userRole: 'organizer',
    });
  });



  it('navigates to profile settings when profile button is clicked', async () => {
    renderWithRouter();

    const profileButton = screen.getByRole('button', { name: /Profile Settings/ });
    await user.click(profileButton);

    expect(screen.getByTestId('profile-page')).toBeInTheDocument();
  });

  it('navigates back to home when back button is clicked', async () => {
    renderWithRouter();

    const backButton = screen.getByTestId('back-button');
    await user.click(backButton);

    expect(screen.getByTestId('home-page')).toBeInTheDocument();
  });

  it('shows correct user role in header', () => {
    mockUseUserRole.mockReturnValue({
      userRole: 'organizer',
    });

    renderWithRouter();

    expect(screen.getByTestId('user-role')).toHaveTextContent('organizer');
  });
}); 