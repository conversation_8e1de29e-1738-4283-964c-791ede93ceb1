import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';
import OrganizerDashboardPage from './OrganizerDashboardPage';
import { createTestQueryClient } from '@/test/test-utils';

// Mock the hooks
const mockUseAccessibleEvents = vi.fn();
const mockUseUserRole = vi.fn();
const mockNavigate = vi.fn();

vi.mock('@/hooks/useUserRoles', () => ({
  useAccessibleEvents: () => mockUseAccessibleEvents(),
  useUserRole: () => mockUseUserRole(),
}));

// Mock components
vi.mock('@/components/Header', () => ({
  Header: ({ onBackToEvents, showBackButton, userRole }: any) => (
    <header data-testid="header">
      <h1>Organizer Dashboard Header</h1>
      {showBackButton && <button data-testid="back-button" onClick={onBackToEvents}>Back</button>}
      <span data-testid="user-role">{userRole}</span>
    </header>
  ),
}));

const renderWithRouter = () => {
  return render(
    <QueryClientProvider client={createTestQueryClient()}>
      <MemoryRouter initialEntries={["/admin/organizer-dashboard"]}>
        <Routes>
          <Route path="/admin/organizer-dashboard" element={<OrganizerDashboardPage />} />
          <Route path="/admin/events/:eventId" element={<div data-testid="event-details-page">Event Details</div>} />
          <Route path="/admin/events/create" element={<div data-testid="create-event-page">Create Event</div>} />
          <Route path="/admin/profile" element={<div data-testid="profile-page">Profile Settings</div>} />
          <Route path="/" element={<div data-testid="home-page">Home</div>} />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('OrganizerDashboardPage', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
    
    // Default mocks
    mockUseAccessibleEvents.mockReturnValue({
      data: [],
      isLoading: false,
    });
    
    mockUseUserRole.mockReturnValue({
      userRole: 'organizer',
    });
  });

  it('shows loading state', () => {
    mockUseAccessibleEvents.mockReturnValue({
      data: undefined,
      isLoading: true,
    });

    renderWithRouter();
    expect(screen.getByText('Loading your events...')).toBeInTheDocument();
  });

  it('shows empty state with create event button', () => {
    mockUseAccessibleEvents.mockReturnValue({
      data: [],
      isLoading: false,
    });

    renderWithRouter();
    
    expect(screen.getByText('No events yet')).toBeInTheDocument();
    expect(screen.getByText("Create your first event to get started.")).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Create Your First Event/ })).toBeInTheDocument();
  });

  it('displays stats cards correctly', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer_id: 'org1',
      },
      {
        id: '2',
        name: 'Event 2',
        start_date: '2024-01-03',
        end_date: '2024-01-04',
        is_active: false,
        organizer_id: 'org1',
      },
      {
        id: '3',
        name: 'Event 3',
        start_date: '2024-01-05',
        end_date: '2024-01-06',
        is_active: true,
        organizer_id: 'org1',
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    // Check stats cards - use more specific selectors
    expect(screen.getByText('Total Events')).toBeInTheDocument();
    expect(screen.getByText('Active Events')).toBeInTheDocument();
    
    // Check the specific numbers in the stats cards
    const totalEventsCard = screen.getByText('Total Events').closest('div')?.parentElement;
    const activeEventsCard = screen.getByText('Active Events').closest('div')?.parentElement;
    
    expect(totalEventsCard).toHaveTextContent('3'); // Total events
    expect(activeEventsCard).toHaveTextContent('2'); // Active events (2 out of 3)
    
    // Check for Quick Actions in the stats section (not the quick actions section below)
    const statsSection = screen.getByText('Total Events').closest('div')?.parentElement?.parentElement;
    expect(statsSection).toHaveTextContent('Quick Actions');
  });

  it('displays events list with correct information', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer_id: 'org1',
      },
      {
        id: '2',
        name: 'Event 2',
        start_date: '2024-01-03',
        end_date: '2024-01-04',
        is_active: false,
        organizer_id: 'org1',
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    expect(screen.getByText('My Events')).toBeInTheDocument();
    expect(screen.getByText('Event 1')).toBeInTheDocument();
    expect(screen.getByText('Event 2')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Inactive')).toBeInTheDocument();
  });

  it('navigates to event details when event is clicked', async () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer_id: 'org1',
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    const eventButton = screen.getByRole('button', { name: /Event 1/ });
    await user.click(eventButton);

    expect(screen.getByTestId('event-details-page')).toBeInTheDocument();
  });

  it('navigates to create event page when create button is clicked', async () => {
    renderWithRouter();

    // Use getAllByRole to get all create buttons and click the first one
    const createButtons = screen.getAllByRole('button', { name: /Create New Event/ });
    await user.click(createButtons[0]);

    expect(screen.getByTestId('create-event-page')).toBeInTheDocument();
  });

  it('navigates to profile settings when profile button is clicked', async () => {
    renderWithRouter();

    const profileButton = screen.getByRole('button', { name: /Profile Settings/ });
    await user.click(profileButton);

    expect(screen.getByTestId('profile-page')).toBeInTheDocument();
  });

  it('navigates back to home when back button is clicked', async () => {
    renderWithRouter();

    const backButton = screen.getByTestId('back-button');
    await user.click(backButton);

    expect(screen.getByTestId('home-page')).toBeInTheDocument();
  });

  it('shows correct user role in header', () => {
    mockUseUserRole.mockReturnValue({
      userRole: 'organizer',
    });

    renderWithRouter();

    expect(screen.getByTestId('user-role')).toHaveTextContent('organizer');
  });

  it('filters events to only show organizer events', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'My Event',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer_id: 'org1', // This event should be shown
      },
      {
        id: '2',
        name: 'Other Event',
        start_date: '2024-01-03',
        end_date: '2024-01-04',
        is_active: true,
        organizer_id: null, // This event should not be shown
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    // Should only show the event with organizer_id
    expect(screen.getByText('My Event')).toBeInTheDocument();
    expect(screen.queryByText('Other Event')).not.toBeInTheDocument();
    
    // Check the total events count in the stats card specifically
    const totalEventsCard = screen.getByText('Total Events').closest('div')?.parentElement;
    expect(totalEventsCard).toHaveTextContent('1'); // Total events count should be 1
  });

  it('displays multiple create event buttons and they all work', async () => {
    renderWithRouter();

    const createButtons = screen.getAllByRole('button', { name: /Create New Event/ });
    expect(createButtons).toHaveLength(2); // One in stats card, one in quick actions (empty state doesn't show when there are events)

    // Click the first create button
    await user.click(createButtons[0]);
    expect(screen.getByTestId('create-event-page')).toBeInTheDocument();
  });

  it('shows correct date formatting for events', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer_id: 'org1',
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    // Check that dates are displayed (the exact format depends on formatDateForDisplay)
    expect(screen.getByText(/1\/1\/2024/)).toBeInTheDocument();
    expect(screen.getByText(/1\/2\/2024/)).toBeInTheDocument();
  });

  it('handles events with different active states correctly', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Active Event',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer_id: 'org1',
      },
      {
        id: '2',
        name: 'Inactive Event',
        start_date: '2024-01-03',
        end_date: '2024-01-04',
        is_active: false,
        organizer_id: 'org1',
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    // Check that both active and inactive badges are shown
    const activeBadge = screen.getByText('Active');
    const inactiveBadge = screen.getByText('Inactive');
    
    expect(activeBadge).toBeInTheDocument();
    expect(inactiveBadge).toBeInTheDocument();
    
    // Check that active events count is correct
    expect(screen.getByText('1')).toBeInTheDocument(); // Active events count
  });
}); 