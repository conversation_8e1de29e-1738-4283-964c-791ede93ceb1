import React from 'react';
import { Header } from '@/components/Header';
import { useNavigate } from 'react-router-dom';

const AboutPage = () => {
  const navigate = useNavigate();
  return (
    <>
      <Header showBackButton onBackToEvents={() => navigate('/')} />
      <section className="min-h-screen bg-cream flex flex-col items-center justify-center py-16 px-4">
        <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg p-8 border border-cream-light">
          <h2 className="font-playfair text-3xl md:text-4xl font-bold text-primary mb-6 text-center">Why We Built It</h2>
          <p className="text-lg text-text-secondary leading-relaxed mb-4">
            We looked—twice. There was no platform built for managing ride times across dressage tests, jumping rounds, and cross-country training days.
          </p>
          <p className="text-lg text-text-secondary leading-relaxed mb-4">
            At one event alone, over 400 rounds were scheduled in a single day, raising nearly $10,000—all while using a tool that wasn't made for horse shows.
          </p>
          <p className="text-lg text-text-secondary leading-relaxed">
            NextRiderUp was born from that frustration—and a deep love for making events smoother, safer, and more rider-friendly. Built by riders, for riders.
          </p>
        </div>
      </section>
    </>
  );
};

export default AboutPage;