import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSession } from '@supabase/auth-helpers-react';
import { useDeleteEvent } from '@/hooks/useEvents';
import { useAccessibleEvents, useIsSuperAdmin, useUserRole } from '@/hooks/useUserRoles';
import { useOrganizers } from '@/hooks/useOrganizers';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Plus, ChevronRight, Trash, User, Users, ArrowLeft, Search, Filter } from 'lucide-react';
import { useUpdateEvent } from '@/hooks/useEvents';
import { formatDateForDisplay } from '@/utils/dateUtils';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { OrganizerInfo } from '@/components/OrganizerInfo';
import { getErrorMessage } from '@/types/errors';
import { useQueryClient } from '@tanstack/react-query';

interface Organizer {
  id: string;
  full_name?: string;
  email: string;
}

interface Event {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  organizer?: Organizer;
}

const EventsListPage = () => {
  const navigate = useNavigate();
  const session = useSession();
  const user = session?.user;
  const { data: events, isLoading } = useAccessibleEvents();
  const { data: isSuperAdmin } = useIsSuperAdmin();
  const { userRole } = useUserRole();
  const { data: allOrganizers } = useOrganizers();
  const updateEvent = useUpdateEvent();
  const deleteEvent = useDeleteEvent();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [eventToDelete, setEventToDelete] = useState<Event | null>(null);
  const queryClient = useQueryClient();

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [organizerFilter, setOrganizerFilter] = useState<string>('all');

  // Get current user's organizer ID if they're an organizer
  const currentUserOrganizerId = useMemo(() => {
    if (userRole === 'organizer' && user && allOrganizers) {
      // Find the organizer record for the current user
      const currentUserOrganizer = allOrganizers.find(org => org.email === user.email);
      return currentUserOrganizer?.id || null;
    }
    return null;
  }, [userRole, user, allOrganizers]);

  // Set default organizer filter for organizers
  React.useEffect(() => {
    if (userRole === 'organizer' && currentUserOrganizerId) {
      setOrganizerFilter(currentUserOrganizerId);
    } else if (userRole === 'super_admin') {
      setOrganizerFilter('all');
    }
  }, [userRole, currentUserOrganizerId]);

  // Get unique organizers for filter from events
  const eventOrganizers = useMemo(() => {
    if (!events) return [];
    const organizerMap = new Map();
    events.forEach(event => {
      if (event.organizer) {
        organizerMap.set(event.organizer.id, event.organizer.full_name || event.organizer.email);
      }
    });
    return Array.from(organizerMap.entries()).map(([id, name]) => ({ id, name }));
  }, [events]);

  // Filter events
  const filteredEvents = useMemo(() => {
    if (!events) return [];
    
    return events.filter(event => {
      // Search term filter
      const matchesSearch = !searchTerm || 
        event.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Status filter
      const matchesStatus = statusFilter === 'all' || 
        (statusFilter === 'active' && event.is_active) ||
        (statusFilter === 'inactive' && !event.is_active);
      
      // Organizer filter
      const matchesOrganizer = organizerFilter === 'all' || 
        event.organizer?.id === organizerFilter;
      
      return matchesSearch && matchesStatus && matchesOrganizer;
    });
  }, [events, searchTerm, statusFilter, organizerFilter]);

  // Group filtered events by organizer
  const groupedEvents = useMemo(() => {
    if (!filteredEvents) return {};
    
    const groups: Record<string, { organizer: Organizer; events: Event[] }> = {};
    
    filteredEvents.forEach(event => {
      const organizerId = event.organizer?.id || 'no-organizer';
      
      // Get the best available name for the organizer
      let organizerName = 'No Organizer';
      if (event.organizer) {
        organizerName = event.organizer.full_name || event.organizer.email || 'Unknown Organizer';
      }
      
      if (!groups[organizerId]) {
        groups[organizerId] = {
          organizer: event.organizer || { id: 'no-organizer', full_name: 'No Organizer', email: 'No Organizer' },
          events: []
        };
      }
      
      groups[organizerId].events.push(event);
    });
    
    // Sort organizers by name
    return Object.fromEntries(
      Object.entries(groups).sort(([, a], [, b]) => {
        const nameA = a.organizer.full_name || a.organizer.email || '';
        const nameB = b.organizer.full_name || b.organizer.email || '';
        return nameA.localeCompare(nameB);
      })
    );
  }, [filteredEvents]);

  const handleEventSelect = (eventId: string) => {
    navigate(`/admin/events/${eventId}`);
  };

  const handleCreateEvent = () => {
    navigate('/admin/events/create');
  };

  const handleBackToDashboard = () => {
    // Navigate to appropriate dashboard based on user role
    if (userRole === 'super_admin') {
      navigate('/admin/super-admin-dashboard');
    } else if (userRole === 'organizer') {
      navigate('/admin/organizer-dashboard');
    } else {
      navigate('/admin/dashboard');
    }
  };

  const handleDeleteClick = (event: Event) => {
    setEventToDelete(event);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (!eventToDelete) return;
    deleteEvent.mutate(eventToDelete.id, {
      onSuccess: () => {
        setDeleteDialogOpen(false);
        setEventToDelete(null);
        queryClient.invalidateQueries({ queryKey: ['events'] });
      }
    });
  };

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setEventToDelete(null);
  };

  React.useEffect(() => {
    if (events && events.length > 0) {
      console.log('===== EVENTS LIST PAGE - DATE DEBUG =====');
      events.forEach(event => {
        console.log(`Event: ${event.name}`);
        console.log(`Raw start_date: "${event.start_date}"`);
        console.log(`Raw end_date: "${event.end_date}"`);
        console.log(`Formatted start_date: "${formatDateForDisplay(event.start_date)}"`);
        console.log(`Formatted end_date: "${formatDateForDisplay(event.end_date)}"`);
        console.log('---');
      });
    }
  }, [events]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header
        onBackToEvents={handleBackToDashboard}
        showBackButton={true}
        userRole={userRole}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          {/* Header with back button */}
          <div className="flex items-center mb-6">
            <Button
              variant="ghost"
              onClick={handleBackToDashboard}
              className="mr-4 text-green-700 hover:bg-green-50"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Button>
            <h1 className="text-2xl font-bold text-green-800 flex-1">Events</h1>
            <Button
              onClick={handleCreateEvent}
              className="bg-green-600 hover:bg-green-700"
            >
              <Plus className="h-4 w-4 mr-2" /> Create Event
            </Button>
          </div>

          {/* Filters Section */}
          <Card className="border-gray-200 mb-6">
            <CardHeader>
              <CardTitle className="text-lg text-gray-800 flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                Event Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Search Events</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by event name..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Status</label>
                  <Select value={statusFilter} onValueChange={setStatusFilter} data-testid="status-filter">
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Events</SelectItem>
                      <SelectItem value="active">Active Only</SelectItem>
                      <SelectItem value="inactive">Inactive Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Organizer</label>
                  {userRole === 'organizer' && currentUserOrganizerId ? (
                    <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700">
                      {allOrganizers?.find(org => org.id === currentUserOrganizerId)?.full_name || 
                       allOrganizers?.find(org => org.id === currentUserOrganizerId)?.email || 
                       user?.email} (You)
                    </div>
                  ) : (
                    <Select 
                      value={organizerFilter} 
                      onValueChange={setOrganizerFilter} 
                      data-testid="organizer-filter"
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Filter by organizer">
                          {organizerFilter === 'all' 
                            ? 'All Organizers' 
                            : eventOrganizers.find(o => o.id === organizerFilter)?.name || 'Select Organizer'
                          }
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Organizers</SelectItem>
                        {eventOrganizers.map(organizer => (
                          <SelectItem key={organizer.id} value={organizer.id}>
                            {organizer.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {isLoading ? (
            <Card>
              <CardContent className="p-6">
                <div className="text-center">Loading events...</div>
              </CardContent>
            </Card>
          ) : !filteredEvents || filteredEvents.length === 0 ? (
            <Card className="bg-gray-50 border-gray-200">
              <CardContent className="text-center py-12">
                <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm || statusFilter !== 'all' || organizerFilter !== 'all' ? 'No events found' : 'No events yet'}
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || statusFilter !== 'all' || organizerFilter !== 'all'
                    ? 'Try adjusting your filters.' 
                    : 'Create your first event to get started.'}
                </p>
                {!searchTerm && statusFilter === 'all' && organizerFilter === 'all' && (
                  <Button
                    onClick={handleCreateEvent}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Plus className="h-4 w-4 mr-2" /> Create Your First Event
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {Object.entries(groupedEvents).map(([organizerId, { organizer, events: organizerEvents }]) => (
                <Card key={organizerId} className="border-blue-200">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg text-blue-800 flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      {organizer.full_name || organizer.email || 'No Organizer'}
                      <Badge variant="outline" className="ml-2 text-blue-600">
                        {organizerEvents.length} event{organizerEvents.length !== 1 ? 's' : ''}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      {organizerEvents.map((event) => (
                        <Card key={event.id} className="border-green-200 hover:shadow-md transition-shadow">
                          <CardContent className="p-0">
                            <div className="flex justify-between items-center p-4 w-full">
                              <Button
                                variant="ghost"
                                className="flex-1 p-0 h-auto block text-left"
                                onClick={() => handleEventSelect(event.id)}
                              >
                                <div className="flex justify-between items-center w-full">
                                  <div className="space-y-1">
                                    <h3 className="text-lg font-semibold text-green-800">{event.name}</h3>
                                    <div className="flex items-center text-sm text-gray-600">
                                      <Calendar className="h-4 w-4 mr-2 text-green-600" />
                                      <span>
                                        {formatDateForDisplay(event.start_date)} - {formatDateForDisplay(event.end_date)}
                                      </span>
                                    </div>
                                    {/* Organizer Info */}
                                    {isSuperAdmin && event.organizer && (
                                      <div className="mt-2">
                                        <OrganizerInfo 
                                          organizer={event.organizer} 
                                          showLabel={false}
                                          className="text-xs"
                                        />
                                      </div>
                                    )}
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <Badge
                                      variant="outline"
                                      className={`border ${event.is_active
                                        ? 'text-green-600 border-green-300'
                                        : 'text-gray-400 border-gray-300'
                                        }`}
                                    >
                                      {event.is_active ? 'Active' : 'Inactive'}
                                    </Badge>
                                    <input
                                      type="checkbox"
                                      checked={event.is_active}
                                      onChange={(e) =>
                                        updateEvent.mutate({ id: event.id, is_active: e.target.checked })
                                      }
                                      className="ml-2"
                                      title="Toggle active status"
                                    />
                                    <ChevronRight className="h-5 w-5 text-gray-400" />
                                  </div>
                                </div>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="ml-2 border-red-500 text-red-700 hover:bg-red-50"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteClick(event);
                                }}
                                disabled={deleteEvent.isPending}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
      {deleteDialogOpen && (
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Event</AlertDialogTitle>
              <AlertDialogDescription>
                <div className="space-y-4">
                  <p className="text-red-600 font-medium">
                    Are you sure you want to delete "{eventToDelete?.name}"?
                  </p>
                  <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <p className="text-sm text-red-800 font-medium mb-2">This action will permanently delete:</p>
                    <ul className="text-sm text-red-700 space-y-1">
                      <li>• The event itself</li>
                      <li>• All locations and arenas</li>
                      <li>• All activities and time slots</li>
                      <li>• All bookings and reservations</li>
                      <li>• All event levels and dressage tests</li>
                    </ul>
                    <p className="text-sm text-red-800 font-medium mt-3">
                      This action cannot be undone!
                    </p>
                  </div>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={handleCancelDelete}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirmDelete}
                className="bg-red-600 hover:bg-red-700 text-white"
                disabled={deleteEvent.isPending}
                data-testid="delete-event-confirm-button"
              >
                {deleteEvent.isPending ? 'Deleting...' : 'Delete Event'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
};

export default EventsListPage;
