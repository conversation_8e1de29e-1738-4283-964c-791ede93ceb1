import React from 'react';
    import { useNavigate } from 'react-router-dom';
import { useEvents } from '@/hooks/useEvents';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Plus, ChevronRight } from 'lucide-react';
import { useUpdateEvent } from '@/hooks/useEvents';
import { formatDateForDisplay } from '@/utils/dateUtils';

const EventsListPage = () => {
  const navigate = useNavigate();
  const { data: events, isLoading } = useEvents();
  const updateEvent = useUpdateEvent();


  const handleEventSelect = (eventId: string) => {
    navigate(`/admin/events/${eventId}`);
  };

  const handleCreateEvent = () => {
    navigate('/admin/events/create');
  };

  const handleBackToDashboard = () => {
    navigate('/admin/dashboard');
  };

  React.useEffect(() => {
    if (events && events.length > 0) {
      console.log('===== EVENTS LIST PAGE - DATE DEBUG =====');
      events.forEach(event => {
        console.log(`Event: ${event.name}`);
        console.log(`Raw start_date: "${event.start_date}"`);
        console.log(`Raw end_date: "${event.end_date}"`);
        console.log(`Formatted start_date: "${formatDateForDisplay(event.start_date)}"`);
        console.log(`Formatted end_date: "${formatDateForDisplay(event.end_date)}"`);
        console.log('---');
      });
    }
  }, [events]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header
        onBackToEvents={handleBackToDashboard}
        showBackButton={true}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-green-800">Events</h1>
            <Button
              onClick={handleCreateEvent}
              className="bg-green-600 hover:bg-green-700"
            >
              <Plus className="h-4 w-4 mr-2" /> Create Event
            </Button>
          </div>

          {isLoading ? (
            <Card>
              <CardContent className="p-6">
                <div className="text-center">Loading events...</div>
              </CardContent>
            </Card>
          ) : !events || events.length === 0 ? (
            <Card className="bg-gray-50 border-gray-200">
              <CardContent className="text-center py-12">
                <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No events yet</h3>
                <p className="text-gray-600 mb-4">Create your first event to get started.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {events.map((event) => (
                <Card key={event.id} className="border-green-200 hover:shadow-md transition-shadow">
                  <CardContent className="p-0">
                    <Button
                      variant="ghost"
                      className="w-full p-0 h-auto block text-left"
                      onClick={() => handleEventSelect(event.id)}
                    >
                      <div className="flex justify-between items-center p-4 w-full">
                        <div className="space-y-1">
                          <h3 className="text-lg font-semibold text-green-800">{event.name}</h3>
                          <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="h-4 w-4 mr-2 text-green-600" />
                            <span>
                              {formatDateForDisplay(event.start_date)} - {formatDateForDisplay(event.end_date)}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant="outline"
                            className={`border ${event.is_active
                              ? 'text-green-600 border-green-300'
                              : 'text-gray-400 border-gray-300'
                              }`}
                          >
                            {event.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                          <input
                            type="checkbox"
                            checked={event.is_active}
                            onChange={(e) =>
                              updateEvent.mutate({ ...event, is_active: e.target.checked })
                            }
                            className="ml-2"
                            title="Toggle active status"
                          />
                          <ChevronRight className="h-5 w-5 text-gray-400" />
                        </div>
                      </div>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default EventsListPage;
