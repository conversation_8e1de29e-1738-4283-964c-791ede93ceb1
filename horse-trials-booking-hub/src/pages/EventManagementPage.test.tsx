import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { vi } from 'vitest';
import { EventManagementPage } from './EventManagementPage';
import { useEvent } from '@/hooks/useEvents';

// Mock the hooks
vi.mock('@/hooks/useEvents');
vi.mock('@/components/admin/EventDetail', () => ({
  EventDetail: ({ event, eventId }: { event: any; eventId: string }) => (
    <div data-testid="event-detail">
      <h3>Event Detail Component</h3>
      <p>Event: {event?.name}</p>
      <p>Event ID: {eventId}</p>
    </div>
  ),
}));

const mockEvent = {
  id: 'event-123',
  name: 'Test Event',
  start_date: '2024-06-15',
  end_date: '2024-06-16',
  event_type: 'horse_trials',
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockUseEvent = vi.mocked(useEvent);

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderEventManagementPage = (eventId = 'event-123') => {
  const queryClient = createTestQueryClient();
  
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter initialEntries={[`/admin/events/${eventId}`]}>
        <Routes>
          <Route path="/admin/events/:eventId" element={<EventManagementPage />} />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('EventManagementPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Loading and Error States', () => {
    it('shows loading state when event is loading', () => {
      mockUseEvent.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      });

      renderEventManagementPage();
      
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('shows error state when event fails to load', () => {
      mockUseEvent.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error('Failed to load event'),
      });

      renderEventManagementPage();
      
      expect(screen.getByText('Event not found')).toBeInTheDocument();
      expect(screen.getByText('Back to Events')).toBeInTheDocument();
    });

    it('shows not found when event does not exist', () => {
      mockUseEvent.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
      });

      renderEventManagementPage();
      
      expect(screen.getByText('Event not found')).toBeInTheDocument();
      expect(screen.getByText('Back to Events')).toBeInTheDocument();
    });
  });

  describe('Successful Rendering', () => {
    it('renders EventDetail component when event loads successfully', () => {
      mockUseEvent.mockReturnValue({
        data: mockEvent,
        isLoading: false,
        error: null,
      });

      renderEventManagementPage();
      
      expect(screen.getByTestId('event-detail')).toBeInTheDocument();
      expect(screen.getByText(/Test Event/)).toBeInTheDocument();
      expect(screen.getByText(/event-123/)).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('redirects to events list when no eventId is provided', () => {
      // This test would require more complex setup to test navigation
      // For now, we'll just ensure the component handles the case gracefully
      mockUseEvent.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      });

      renderEventManagementPage();
      
      // The component should handle this case and redirect
      // We can't easily test the redirect in this setup, but we can ensure it doesn't crash
    });
  });
}); 