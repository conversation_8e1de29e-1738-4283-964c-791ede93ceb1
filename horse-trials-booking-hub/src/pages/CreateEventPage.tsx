import React, { useState } from 'react';
import { useNavigate, Navigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { useCreateEvent } from '@/hooks/useEvents';
import { useLevels } from '@/hooks/useLevels';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { X } from 'lucide-react';

const CreateEventPage = () => {
  const navigate = useNavigate();
  const { user, userRole } = useAuth();
  const createEvent = useCreateEvent();
  const { toast } = useToast();
  const { data: allLevels } = useLevels();
  
  const [newEvent, setNewEvent] = useState({
    name: '',
    start_date: '',
    end_date: '',
    schooling_start_date: '',
    schooling_end_date: '',
    event_type: 'horse_trials' as const
  });
  
  const [selectedLevels, setSelectedLevels] = useState<string[]>([]);
  const [selectedDiscipline, setSelectedDiscipline] = useState('all');
  
  // Check if user is admin or organizer
  const isAdmin = userRole === 'admin' || userRole === 'organizer';
  
  // Redirect to login if not authenticated or not admin
  if (!user || !isAdmin) {
    return <Navigate to="/admin/login" />;
  }

  // Filter levels by discipline
  const filteredLevels = allLevels?.filter(level => 
    selectedDiscipline === 'all' || level.discipline === selectedDiscipline
  ) || [];

  // Get available levels (not already selected)
  const availableLevels = filteredLevels.filter(level => 
    !selectedLevels.includes(level.id)
  );

  // Get selected level objects
  const selectedLevelObjects = allLevels?.filter(level => 
    selectedLevels.includes(level.id)
  ) || [];

  const handleAddLevel = (levelId: string) => {
    if (!selectedLevels.includes(levelId)) {
      setSelectedLevels([...selectedLevels, levelId]);
    }
  };

  const handleRemoveLevel = (levelId: string) => {
    setSelectedLevels(selectedLevels.filter(id => id !== levelId));
  };
  
  const handleCreateEvent = () => {
    if (!newEvent.name) {
      toast({
        title: "Error",
        description: "Event name is required",
        variant: "destructive",
      });
      return;
    }
    
    createEvent.mutate({
      event: newEvent,
      levelIds: selectedLevels
    }, {
      onSuccess: (data) => {
        toast({
          title: "Success",
          description: "Event created successfully",
        });
        // Navigate to the events list
        navigate('/admin/events');
      }
    });
  };
  
  const handleCancel = () => {
    navigate('/admin/events');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header 
        onBackToEvents={handleCancel}
        showBackButton={true}
        isAdmin={isAdmin}
      />
      <main className="container mx-auto px-4 py-4 sm:py-8">
        <div className="max-w-2xl mx-auto">
          <Card className="border-green-200">
            <CardHeader className="pb-4 sm:pb-6">
              <CardTitle className="text-lg sm:text-xl text-green-800">Create New Event</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 sm:space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">Event Name</Label>
                <Input
                  id="name"
                  value={newEvent.name}
                  onChange={(e) => setNewEvent({...newEvent, name: e.target.value})}
                  placeholder="Enter event name"
                  className="w-full"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="event-type" className="text-sm font-medium">Event Type</Label>
                <Select
                  value={newEvent.event_type}
                  onValueChange={(value: 'dressage' | 'horse_trials' | 'hunter_jumper') => 
                    setNewEvent({...newEvent, event_type: value})
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select event type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="horse_trials">Horse Trials</SelectItem>
                    <SelectItem value="dressage">Dressage</SelectItem>
                    <SelectItem value="hunter_jumper">Hunter/Jumper</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-gray-700 border-b pb-2">Competition Dates</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start_date" className="text-sm">Start Date</Label>
                    <Input
                      id="start_date"
                      type="date"
                      value={newEvent.start_date}
                      onChange={(e) => setNewEvent({...newEvent, start_date: e.target.value})}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end_date" className="text-sm">End Date</Label>
                    <Input
                      id="end_date"
                      type="date"
                      value={newEvent.end_date}
                      onChange={(e) => setNewEvent({...newEvent, end_date: e.target.value})}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-gray-700 border-b pb-2">Schooling Dates</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="schooling_start_date" className="text-sm">Start Date</Label>
                    <Input
                      id="schooling_start_date"
                      type="date"
                      value={newEvent.schooling_start_date}
                      onChange={(e) => setNewEvent({...newEvent, schooling_start_date: e.target.value})}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="schooling_end_date" className="text-sm">End Date</Label>
                    <Input
                      id="schooling_end_date"
                      type="date"
                      value={newEvent.schooling_end_date}
                      onChange={(e) => setNewEvent({...newEvent, schooling_end_date: e.target.value})}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium text-gray-700 border-b pb-2">Event Levels</h3>
                
                {/* Discipline Filter */}
                <div className="space-y-2">
                  <Label htmlFor="discipline-filter" className="text-sm">Filter by Discipline</Label>
                  <Select value={selectedDiscipline} onValueChange={setSelectedDiscipline}>
                    <SelectTrigger>
                      <SelectValue placeholder="All disciplines" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All disciplines</SelectItem>
                      <SelectItem value="dressage">Dressage</SelectItem>
                      <SelectItem value="eventing">Eventing</SelectItem>
                      <SelectItem value="show_jumping">Show Jumping</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Add Level Dropdown */}
                <div className="space-y-2">
                  <Label htmlFor="level-select" className="text-sm">Add Level</Label>
                  <Select onValueChange={handleAddLevel}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a level to add" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableLevels.map((level) => (
                        <SelectItem key={level.id} value={level.id}>
                          {level.name} ({level.discipline})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Selected Levels */}
                {selectedLevelObjects.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm">Selected Levels</Label>
                    <div className="flex flex-wrap gap-2">
                      {selectedLevelObjects.map((level) => (
                        <Badge 
                          key={level.id} 
                          variant="outline" 
                          className="text-green-700 border-green-300 flex items-center gap-1"
                        >
                          {level.name} ({level.discipline})
                          <button
                            onClick={() => handleRemoveLevel(level.id)}
                            className="ml-1 hover:text-red-600"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 border-t">
                <Button 
                  variant="outline"
                  onClick={handleCancel}
                  className="w-full sm:w-auto order-2 sm:order-1"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleCreateEvent}
                  disabled={!newEvent.name || createEvent.isPending}
                  className="bg-green-600 hover:bg-green-700 w-full sm:w-auto order-1 sm:order-2"
                >
                  {createEvent.isPending ? 'Creating...' : 'Create Event'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default CreateEventPage;
