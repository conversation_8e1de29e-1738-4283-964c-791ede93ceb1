import React from 'react';
import { AdminDashboard as AdminDashboardComponent } from '@/components/AdminDashboard';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Header } from '@/components/Header';
import { Navigate } from 'react-router-dom';

const AdminDashboardPage = () => {
  const navigate = useNavigate();
  const { user, userRole } = useAuth();
  
  // Check if user is admin or organizer
  const isAdmin = userRole === 'admin' || userRole === 'organizer';
  
  // Redirect to login if not authenticated or not admin
  if (!user || !isAdmin) {
    return <Navigate to="/admin/login" />;
  }
  
  const handleEventManage = () => {
    navigate('/admin/events');
  };
  
  const handleCreateEvent = () => {
    navigate('/admin/events/create');
  };
  
  const handleSettingsManage = () => {
    navigate('/admin/settings');
  };
  
  const handleBackToHome = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header 
        onBackToEvents={handleBackToHome}
        showBackButton={true}
        isAdmin={isAdmin}
      />
      <main className="container mx-auto px-4 py-8">
        <AdminDashboardComponent 
          onEventManage={handleEventManage}
          onCreateEvent={handleCreateEvent}
          onSettingsManage={handleSettingsManage}
        />
      </main>
    </div>
  );
};

export default AdminDashboardPage;
