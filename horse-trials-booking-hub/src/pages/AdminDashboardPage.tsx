import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { Navigate } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, MapPin, Users, Settings, Database, Layers, FileText, Plus } from 'lucide-react';
import { Link } from 'react-router-dom';

const AdminDashboardPage = () => {
  const navigate = useNavigate();
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  
  // Show loading while determining role
  if (!userRole) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50 flex items-center justify-center" data-testid="loading-dashboard">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-green-600 mx-auto mb-4" data-testid="loading-spinner" />
          <p className="text-gray-600" data-testid="loading-text">Loading dashboard...</p>
        </div>
      </div>
    );
  }
  
  // Check if user is super_admin or organizer
  const isAdmin = userRole === 'super_admin' || userRole === 'organizer';
  
  // Route to appropriate dashboard based on role
  useEffect(() => {
    if (userRole === 'super_admin') {
      navigate('/admin/super-admin-dashboard', { replace: true });
    } else if (userRole === 'organizer') {
      navigate('/admin/organizer-dashboard', { replace: true });
    }
  }, [userRole, navigate]);
  
  // Redirect to login if not authenticated or not admin
  if (!user || !isAdmin) {
    return <Navigate to="/admin/login" data-testid="redirect-to-login" />;
  }

  // Show loading while redirecting
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50 flex items-center justify-center" data-testid="redirecting-dashboard">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin text-green-600 mx-auto mb-4" data-testid="redirecting-spinner" />
        <p className="text-gray-600" data-testid="redirecting-text">Redirecting to your dashboard...</p>
      </div>
    </div>
  );
};

export default AdminDashboardPage;
