import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { vi } from 'vitest';
import BookingSuccessPage from './BookingSuccessPage';

// Mocks
vi.mock('@/components/Header', () => ({ Header: () => <div data-testid="header" /> }));
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {},
  getEdgeFunctionUrl: () => 'https://mock-edge-function-url',
}));
vi.mock('@/hooks/useSlotReservations', () => ({
  getUserSessionId: () => 'mock-session-id',
}));
vi.mock('@/types/errors', () => ({ getErrorMessage: (e: any) => e?.message || 'Unknown error' }));

// Helpers
const createTestQueryClient = () => new QueryClient({
  defaultOptions: { queries: { retry: false }, mutations: { retry: false } },
});

const renderWithRouter = (route: string) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter initialEntries={[route]}>
        <Routes>
          <Route path="/success" element={<BookingSuccessPage />} />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('BookingSuccessPage', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('shows loading spinner initially', async () => {
    // Mock fetch to never resolve
    vi.spyOn(global, 'fetch').mockImplementation(() => new Promise(() => {}) as any);
    renderWithRouter('/success?session_id=abc123&event_id=evt1&date=2024-07-01');
    expect(screen.getByText(/Confirming your booking/i)).toBeInTheDocument();
  });

  it('shows success state with booking details', async () => {
    vi.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: async () => ({ success: true, booking: { id: 'booking-1', total_price: 99.5 } }),
    } as any);
    await act(async () => {
      renderWithRouter('/success?session_id=abc123&event_id=evt1&date=2024-07-01');
    });
    await waitFor(() => {
      expect(screen.getByText(/Payment Successful/i)).toBeInTheDocument();
      expect(screen.getByText(/booking-1/)).toBeInTheDocument();
      expect(screen.getByText('$99.5')).toBeInTheDocument();
      expect(screen.getByText(/abc123/)).toBeInTheDocument();
    });
  });

  it('shows success state without booking details if no session_id', async () => {
    await act(async () => {
      renderWithRouter('/success?event_id=evt1&date=2024-07-01');
    });
    await waitFor(() => {
      expect(screen.getByText(/Payment Successful/i)).toBeInTheDocument();
      expect(screen.queryByText(/Booking ID:/)).not.toBeInTheDocument();
      expect(screen.queryByText(/Amount:/)).not.toBeInTheDocument();
    });
  });

  it('handles error from edge function gracefully', async () => {
    vi.spyOn(global, 'fetch').mockResolvedValue({
      ok: false,
      json: async () => ({ error: 'Something went wrong' }),
    } as any);
    await act(async () => {
      renderWithRouter('/success?session_id=abc123&event_id=evt1&date=2024-07-01');
    });
    await waitFor(() => {
      expect(screen.getByText(/Payment Successful/i)).toBeInTheDocument();
      // Should not show error to user, but still renders success
    });
  });

  it('navigates to event or bookings when buttons are clicked', async () => {
    vi.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: async () => ({ success: true, booking: { id: 'booking-1', total_price: 99.5 } }),
    } as any);
    renderWithRouter('/success?session_id=abc123&event_id=evt1&date=2024-07-01');
    await waitFor(() => {
      expect(screen.getByText(/Return to Event/i)).toBeInTheDocument();
      expect(screen.getByText(/View All Bookings/i)).toBeInTheDocument();
    });
    // Simulate button clicks
    act(() => {
      screen.getByText(/Return to Event/i).click();
      screen.getByText(/View All Bookings/i).click();
    });
  });
}); 