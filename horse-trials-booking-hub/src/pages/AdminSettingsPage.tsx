import React from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { useAuth } from '@/hooks/useAuth';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { ActivityPricingSettings } from '@/components/admin/ActivityPricingSettings';

const AdminSettingsPage = () => {
  const navigate = useNavigate();
  const { user, userRole } = useAuth();
  
  // Check if user is admin
  const isAdmin = userRole === 'admin' || userRole === 'organizer';
  
  // Redirect to login if not authenticated or not admin
  if (!user || !isAdmin) {
    return <Navigate to="/admin/login" />;
  }
  
  const handleBackToDashboard = () => {
    navigate('/admin/dashboard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header 
        onBackToEvents={handleBackToDashboard}
        showBackButton={true}
        isAdmin={isAdmin}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Button 
                variant="ghost" 
                onClick={handleBackToDashboard}
                className="text-green-700 hover:bg-green-50 mb-2"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              <h2 className="text-2xl font-bold text-green-800">System Settings</h2>
            </div>
          </div>
          
          <Tabs defaultValue="pricing" className="space-y-4">
            <TabsList>
              <TabsTrigger value="pricing">Activity Pricing</TabsTrigger>
              {/* Add more settings tabs here as needed */}
            </TabsList>
            <TabsContent value="pricing">
              <ActivityPricingSettings />
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
};

export default AdminSettingsPage;