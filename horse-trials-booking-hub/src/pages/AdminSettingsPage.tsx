import React from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Settings, Target, FileText, Users, Database } from 'lucide-react';

const AdminSettingsPage = () => {
  const navigate = useNavigate();
  const { user, userRole } = useAuth();
  
  // Check if user is super admin
  const isSuperAdmin = userRole === 'super_admin';
  
  // Redirect to login if not authenticated or not super admin
  if (!user || !isSuperAdmin) {
    return <Navigate to="/admin/login" />;
  }
  
  const handleBackToDashboard = () => {
    navigate('/admin/super-admin-dashboard');
  };

  const settingsOptions = [
    {
      title: 'Levels Management',
      description: 'Manage competition levels available across all events',
      icon: Target,
      href: '/admin/levels',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Dressage Tests Management',
      description: 'Manage dressage tests available across all events',
      icon: FileText,
      href: '/admin/dressage-tests',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      title: 'Users Management',
      description: 'Manage user accounts and roles across the platform',
      icon: Users,
      href: '/admin/users',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      title: 'Database Management',
      description: 'View database statistics and system information',
      icon: Database,
      href: '/admin/database',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header 
        onBackToEvents={handleBackToDashboard}
        showBackButton={true}
        isAdmin={isSuperAdmin}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <Button 
                variant="ghost" 
                onClick={handleBackToDashboard}
                className="text-green-700 hover:bg-green-50 mb-2"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              <h2 className="text-3xl font-bold text-green-800">System Settings</h2>
              <p className="text-gray-600 mt-1">Manage app-wide settings and configurations</p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {settingsOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <Card 
                  key={option.title}
                  className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${option.borderColor} hover:scale-105`}
                  onClick={() => navigate(option.href)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className={`p-3 rounded-lg ${option.bgColor}`}>
                        <IconComponent className={`h-6 w-6 ${option.color}`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">
                          {option.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">
                          {option.description}
                        </p>
                        <Button 
                          variant="outline" 
                          size="sm"
                          className={`${option.color} ${option.borderColor} hover:${option.bgColor}`}
                        >
                          Manage
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Additional Information */}
          <Card className="mt-8 border-amber-200">
            <CardHeader>
              <CardTitle className="text-amber-800 flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                About System Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm text-gray-600">
                <p>
                  <strong>Levels Management:</strong> Create and manage competition levels that can be used across all events. 
                  These levels define the difficulty and requirements for different competition categories.
                </p>
                <p>
                  <strong>Dressage Tests Management:</strong> Add and organize dressage tests by level. 
                  These tests will be available for selection when creating dressage activities in events.
                </p>
                <p>
                  <strong>Users Management:</strong> View all registered users, manage their roles, and control access permissions. 
                  Only super admins can modify user roles.
                </p>
                <p>
                  <strong>Database Management:</strong> Monitor system statistics, view database information, and access system logs.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default AdminSettingsPage;