import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useEvent, useUpdateEvent } from '@/hooks/useEvents';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Edit, ArrowLeft } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { LocationManagement } from '@/components/admin/LocationManagement';
import { EventLevelsManagement } from '@/components/admin/EventLevelsManagement';
import { EventDressageTestsManagement } from '@/components/admin/EventDressageTestsManagement';
import { AdminBookings } from '@/components/admin/AdminBookings';
import { formatDateForDisplay } from '@/utils/dateUtils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const EventManagementPage = () => {
  const navigate = useNavigate();
  const { eventId } = useParams<{ eventId: string }>();
  const { data: event, isLoading } = useEvent(eventId);
  const updateEvent = useUpdateEvent();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [editEvent, setEditEvent] = useState({
    name: '',
    start_date: '',
    end_date: '',
    schooling_start_date: '',
    schooling_end_date: '',
    event_type: 'horse_trials' as const,
    is_active: true
  });

  // Redirect if eventId is invalid
  if (!eventId) {
    navigate('/admin/events'); // Use navigate for consistency
    return null; // Return null after navigation
  }

  // Populate form when event data is loaded
  React.useEffect(() => {
    if (event) {
      setEditEvent({
        name: event.name,
        start_date: event.start_date,
        end_date: event.end_date,
        schooling_start_date: event.schooling_start_date,
        schooling_end_date: event.schooling_end_date,
        event_type: event.event_type,
        is_active: event.is_active
      });
      
      // Set the first schooling date as the default selected date
      if (event.schooling_start_date && !selectedDate) {
        setSelectedDate(event.schooling_start_date);
      }
    }
  }, [event, selectedDate]);

  React.useEffect(() => {
    if (event) {
      console.log('🔴🔴🔴 EVENT MANAGEMENT PAGE - EVENT DATA 🔴🔴🔴');
      console.log('Event:', event);
      console.log('Raw dates:');
      console.log(`- start_date: "${event.start_date}"`);
      console.log(`- end_date: "${event.end_date}"`);
      console.log(`- schooling_start_date: "${event.schooling_start_date}"`);
      console.log(`- schooling_end_date: "${event.schooling_end_date}"`);

      console.log('Formatted dates:');
      console.log(`- start_date: "${formatDateForDisplay(event.start_date)}"`);
      console.log(`- end_date: "${formatDateForDisplay(event.end_date)}"`);
      console.log(`- schooling_start_date: "${formatDateForDisplay(event.schooling_start_date)}"`);
      console.log(`- schooling_end_date: "${formatDateForDisplay(event.schooling_end_date)}"`);
    }
  }, [event]);

  // Generate available dates for the event
  const getAvailableDates = () => {
    if (!event) return [];
    
    const dates = [];
    const startDate = new Date(event.schooling_start_date);
    const endDate = new Date(event.schooling_end_date);
    
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      dates.push(d.toISOString().split('T')[0]);
    }
    
    return dates;
  };

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    if (event) {
      setEditEvent({
        name: event.name,
        start_date: event.start_date,
        end_date: event.end_date,
            schooling_start_date: event.schooling_start_date,
            schooling_end_date: event.schooling_end_date,
            event_type: event.event_type,
            is_active: event.is_active
      });
    }
  };

  const handleUpdateEvent = () => {
    if (!editEvent.name) {
      toast({
        title: "Error",
        description: "Event name is required",
        variant: "destructive",
      });
      return;
    }

    updateEvent.mutate({
      id: eventId,
      ...editEvent
    }, {
      onSuccess: () => {
        setIsEditing(false);
      }
    });
  };

  const handleBackToEvents = () => {
    navigate('/admin/events');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
      </div>
    );
  }

  if (!event) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
        <Header
          onBackToEvents={handleBackToEvents}
          showBackButton={true}
        />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600">Event not found</h2>
            <Button
              onClick={handleBackToEvents}
              className="mt-4"
            >
              Back to Events
            </Button>
          </div>
        </main>
      </div>
    );
  }

  const availableDates = getAvailableDates();

  // Debug logging
  console.log('[EventManagementPage] selectedDate:', selectedDate);
  console.log('[EventManagementPage] availableDates:', availableDates);
  console.log('[EventManagementPage] event.schooling_start_date:', event?.schooling_start_date);
  console.log('[EventManagementPage] Available dates formatted:');
  availableDates.forEach((date, index) => {
    console.log(`  ${index}: ${date} -> ${formatDateForDisplay(date)}`);
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header
        onBackToEvents={handleBackToEvents}
        showBackButton={true}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          {/* Event Header */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <div className="flex items-center mb-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="mr-2 p-0 h-8 w-8"
                  onClick={handleBackToEvents}
                >
                  <ArrowLeft className="h-5 w-5" />
                </Button>
                <h1 className="text-2xl font-bold text-green-800">{event.name}</h1>
                <Badge
                  variant="outline"
                  className={`ml-3 border ${event.is_active
                    ? 'text-green-600 border-green-300'
                    : 'text-gray-400 border-gray-300'
                    }`}
                >
                  {event.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div className="flex items-center text-sm text-gray-600 ml-10">
                <Calendar className="h-4 w-4 mr-2 text-green-600" />
                <span>
                  {formatDateForDisplay(event.start_date)} - {formatDateForDisplay(event.end_date)}
                </span>
              </div>
            </div>
            <div className="mt-4 md:mt-0 ml-10 md:ml-0">
              {!isEditing && (
                <Button
                  onClick={handleEditClick}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  <Edit className="h-4 w-4 mr-2" /> Edit Event
                </Button>
              )}
            </div>
          </div>

          {/* Management Tabs */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Event / Locations & Activities</TabsTrigger>
              <TabsTrigger value="bookings">Bookings</TabsTrigger>
              <TabsTrigger value="levels">Levels & Tests</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-4">
              {isEditing ? (
                <Card className="border-amber-200">
                  <CardHeader>
                    <CardTitle className="text-amber-800">Edit Event Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="name">Event Name</Label>
                      <Input
                        id="name"
                        value={editEvent.name}
                        onChange={(e) => setEditEvent({ ...editEvent, name: e.target.value })}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="start_date">Start Date</Label>
                        <Input
                          id="start_date"
                          type="date"
                          value={editEvent.start_date}
                          onChange={(e) => setEditEvent({ ...editEvent, start_date: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="end_date">End Date</Label>
                        <Input
                          id="end_date"
                          type="date"
                          value={editEvent.end_date}
                          onChange={(e) => setEditEvent({ ...editEvent, end_date: e.target.value })}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="schooling_start_date">Schooling Start Date</Label>
                        <Input
                          id="schooling_start_date"
                          type="date"
                          value={editEvent.schooling_start_date}
                          onChange={(e) => setEditEvent({ ...editEvent, schooling_start_date: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="schooling_end_date">Schooling End Date</Label>
                        <Input
                          id="schooling_end_date"
                          type="date"
                          value={editEvent.schooling_end_date}
                          onChange={(e) => setEditEvent({ ...editEvent, schooling_end_date: e.target.value })}
                        />
                      </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" onClick={handleCancelEdit}>
                        Cancel
                      </Button>
                      <Button
                        onClick={handleUpdateEvent}
                        className="bg-gold text-primary hover:bg-gold-hover"
                        disabled={updateEvent.isPending}
                      >
                        {updateEvent.isPending ? 'Updating...' : 'Update Event'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-8">
                  <Card>
                    <CardContent className="p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="font-medium text-gray-700">Event Information</h3>
                          <div className="bg-gray-50 p-3 rounded space-y-2">
                            <div>
                              <span className="font-medium">Name:</span> {event.name}
                            </div>
                            <div>
                              <span className="font-medium">Type:</span> {event.event_type}
                            </div>
                            <div>
                              <span className="font-medium">Competition:</span> {formatDateForDisplay(event.start_date)} - {formatDateForDisplay(event.end_date)}
                            </div>
                            <div>
                              <span className="font-medium">Schooling:</span> {formatDateForDisplay(event.schooling_start_date)} - {formatDateForDisplay(event.schooling_end_date)}
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-700">Status</h3>
                          <div className="bg-gray-50 p-3 rounded flex items-center">
                            <Badge
                              variant="outline"
                              className={`border ${event.is_active ? 'text-green-600 border-green-300 bg-green-50' : 'text-gray-500 border-gray-300 bg-gray-100'}`}
                            >
                              {event.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Locations & Activities Section */}
                  <div>
                    <div className="mb-6">
                      <h2 className="text-2xl font-bold text-green-800 mb-2">Locations & Activities</h2>
                      <p className="text-gray-600">Manage locations and schedule activities for each schooling date</p>
                    </div>

                    {/* Date Selector */}
                    <Card className="mb-6 border-green-200">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-green-800 flex items-center">
                          <Calendar className="h-5 w-5 mr-2" />
                          Working on Schooling Date
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center space-x-4">
                          <div className="flex-1">
                            <Label htmlFor="date-selector">Select Schooling Date to Work On</Label>
                            <Select value={selectedDate} onValueChange={(value) => {
                              console.log('[EventManagementPage] Date selector changed from:', selectedDate, 'to:', value);
                              setSelectedDate(value);
                            }}>
                              <SelectTrigger>
                                <SelectValue placeholder="Choose a schooling date" />
                              </SelectTrigger>
                              <SelectContent>
                                {availableDates.map((date) => (
                                  <SelectItem key={date} value={date}>
                                    {formatDateForDisplay(date)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          {selectedDate && (
                            <div className="text-sm text-gray-600">
                              Managing: <span className="font-medium">{formatDateForDisplay(selectedDate)}</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    <LocationManagement
                      eventId={eventId}
                      eventName={event.name}
                      selectedDate={selectedDate}
                      onBack={handleBackToEvents}
                    />
                    {/* Debug element to show what date is being passed */}
                    <div style={{
                      backgroundColor: 'orange',
                      color: 'black',
                      padding: '10px',
                      margin: '10px',
                      textAlign: 'center',
                      border: '2px solid black',
                      fontSize: '12px'
                    }}>
                      DEBUG: Passing selectedDate="{selectedDate}" to LocationManagement
                      <br />
                      Formatted: {selectedDate ? formatDateForDisplay(selectedDate) : 'No date'}
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="bookings" className="mt-4">
              <AdminBookings event={event} />
            </TabsContent>

            <TabsContent value="levels" className="mt-4">
              <div className="space-y-6">
                <EventLevelsManagement
                  eventId={eventId}
                  eventName={event.name}
                />
                <EventDressageTestsManagement
                  eventId={eventId}
                  eventName={event.name}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
};

export default EventManagementPage;
