import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useEvent } from '@/hooks/useEvents';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { EventDetail } from '@/components/admin/EventDetail';

const EventManagementPage = () => {
  const navigate = useNavigate();
  const { eventId } = useParams<{ eventId: string }>();
  const { data: event, isLoading, error } = useEvent(eventId);

  if (!eventId) {
    navigate('/admin/events');
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" data-testid="loading-spinner" />
      </div>
    );
  }

  if (error || !event) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
        <main className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600">Event not found</h2>
            <Button
              onClick={() => navigate('/admin/events')}
              className="mt-4"
            >
              Back to Events
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return <EventDetail event={event} eventId={eventId} />;
};

export { EventManagementPage };
