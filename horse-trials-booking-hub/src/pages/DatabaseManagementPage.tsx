import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Database, Users, Calendar, MapPin, FileText, Target, Activity } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface DatabaseStats {
  totalUsers: number;
  totalEvents: number;
  totalLocations: number;
  totalBookings: number;
  totalTimeSlots: number;
  totalLevels: number;
  totalDressageTests: number;
  totalActivities: number;
}

const DatabaseManagementPage = () => {
  const navigate = useNavigate();

  // Fetch database statistics
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ['database-stats'],
    queryFn: async (): Promise<DatabaseStats> => {
      // Get counts from various tables
      const [
        { count: usersCount },
        { count: eventsCount },
        { count: locationsCount },
        { count: bookingsCount },
        { count: timeSlotsCount },
        { count: levelsCount },
        { count: dressageTestsCount },
        { count: activitiesCount }
      ] = await Promise.all([
        supabase.from('profiles').select('*', { count: 'exact', head: true }),
        supabase.from('events').select('*', { count: 'exact', head: true }),
        supabase.from('locations').select('*', { count: 'exact', head: true }),
        supabase.from('bookings').select('*', { count: 'exact', head: true }),
        supabase.from('time_slots').select('*', { count: 'exact', head: true }),
        supabase.from('levels').select('*', { count: 'exact', head: true }),
        supabase.from('dressage_tests').select('*', { count: 'exact', head: true }),
        supabase.from('activities').select('*', { count: 'exact', head: true })
      ]);

      return {
        totalUsers: usersCount || 0,
        totalEvents: eventsCount || 0,
        totalLocations: locationsCount || 0,
        totalBookings: bookingsCount || 0,
        totalTimeSlots: timeSlotsCount || 0,
        totalLevels: levelsCount || 0,
        totalDressageTests: dressageTestsCount || 0,
        totalActivities: activitiesCount || 0
      };
    },
  });

  const handleBackToDashboard = () => {
    navigate('/admin/super-admin-dashboard');
  };

  const statCards = [
    {
      title: 'Total Users',
      value: stats?.totalUsers || 0,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Total Events',
      value: stats?.totalEvents || 0,
      icon: Calendar,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      title: 'Total Locations',
      value: stats?.totalLocations || 0,
      icon: MapPin,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      title: 'Total Bookings',
      value: stats?.totalBookings || 0,
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    },
    {
      title: 'Total Time Slots',
      value: stats?.totalTimeSlots || 0,
      icon: Calendar,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      borderColor: 'border-indigo-200'
    },
    {
      title: 'Total Levels',
      value: stats?.totalLevels || 0,
      icon: Target,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    {
      title: 'Total Dressage Tests',
      value: stats?.totalDressageTests || 0,
      icon: FileText,
      color: 'text-teal-600',
      bgColor: 'bg-teal-50',
      borderColor: 'border-teal-200'
    },
    {
      title: 'Total Activities',
      value: stats?.totalActivities || 0,
      icon: Activity,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header
        onBackToEvents={handleBackToDashboard}
        showBackButton={true}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <Button
                variant="ghost"
                onClick={handleBackToDashboard}
                className="text-green-700 hover:bg-green-50 mb-2"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              <h1 className="text-3xl font-bold text-green-800">Database Management</h1>
              <p className="text-gray-600 mt-1">View system statistics and database information</p>
            </div>
          </div>

          {/* System Statistics */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-green-800 flex items-center">
                <Database className="h-5 w-5 mr-2" />
                System Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">Loading statistics...</div>
              ) : error ? (
                <div className="text-center py-8 text-red-600">
                  Error loading statistics. Please try again.
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {statCards.map((stat) => {
                    const IconComponent = stat.icon;
                    return (
                      <Card key={stat.title} className={`${stat.borderColor}`}>
                        <CardContent className="p-4">
                          <div className="flex items-center space-x-3">
                            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                              <IconComponent className={`h-5 w-5 ${stat.color}`} />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                              <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* System Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Database Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-green-800">Database Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Database Type:</span>
                    <Badge variant="outline">PostgreSQL (Supabase)</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Authentication:</span>
                    <Badge variant="outline">Supabase Auth</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Storage:</span>
                    <Badge variant="outline">Supabase Storage</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Real-time:</span>
                    <Badge variant="outline">Enabled</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* System Health */}
            <Card>
              <CardHeader>
                <CardTitle className="text-green-800">System Health</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Database Status:</span>
                    <Badge className="bg-green-100 text-green-800">Healthy</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Authentication:</span>
                    <Badge className="bg-green-100 text-green-800">Active</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">API Status:</span>
                    <Badge className="bg-green-100 text-green-800">Online</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Last Updated:</span>
                    <span className="text-sm text-gray-900">{new Date().toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-green-800">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  onClick={() => navigate('/admin/levels')}
                  className="justify-start"
                >
                  <Target className="h-4 w-4 mr-2" />
                  Manage Levels
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/admin/dressage-tests')}
                  className="justify-start"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Manage Dressage Tests
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/admin/users')}
                  className="justify-start"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Manage Users
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default DatabaseManagementPage; 