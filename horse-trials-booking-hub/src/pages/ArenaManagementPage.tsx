import React from 'react';
import { ArenaManagement as ArenaManagementComponent } from '@/components/admin/ArenaManagement';
import { useNavigate, useParams, Navigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Header } from '@/components/Header';
import { useEvents } from '@/hooks/useEvents';

const ArenaManagementPage = () => {
  const navigate = useNavigate();
  const { eventId } = useParams<{ eventId: string }>();
  const { user, userRole } = useAuth();
  const { data: events } = useEvents();
  
  // Check if user is admin or organizer
  const isAdmin = userRole === 'admin' || userRole === 'organizer';
  
  // Redirect to login if not authenticated or not admin
  if (!user || !isAdmin) {
    return <Navigate to="/admin/login" />;
  }
  
  // Redirect if eventId is invalid
  if (!eventId) {
    return <Navigate to="/admin/dashboard" />;
  }
  
  // Find the event name
  const event = events?.find(e => e.id === eventId);
  const eventName = event?.name || 'Event';
  
  const handleBack = () => {
    navigate('/admin/dashboard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header 
        onBackToEvents={handleBack}
        showBackButton={true}
        isAdmin={isAdmin}
      />
      <main className="container mx-auto px-4 py-8">
        <ArenaManagementComponent 
          eventId={eventId}
          eventName={eventName}
          onBack={handleBack}
        />
      </main>
    </div>
  );
};

export default ArenaManagementPage;
