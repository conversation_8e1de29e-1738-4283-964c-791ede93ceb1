import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { useOrganizers, useCreateOrganizer, useUpdateOrganizer, useDeleteOrganizer, Organizer } from '@/hooks/useOrganizers';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Plus, Edit, Trash2, Users, Shield } from 'lucide-react';

const OrganizersManagementPage = () => {
  const navigate = useNavigate();
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  const { data: organizers, isLoading } = useOrganizers();
  const createOrganizer = useCreateOrganizer();
  const updateOrganizer = useUpdateOrganizer();
  const deleteOrganizer = useDeleteOrganizer();
  const { toast } = useToast();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingOrganizer, setEditingOrganizer] = useState<Organizer | null>(null);
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    company_name: '',
    business_name: ''
  });

  // Check if user is super admin
  const isSuperAdmin = userRole === 'super_admin';

  const handleBackToDashboard = () => {
    navigate('/admin/super-admin-dashboard');
  };

  const handleAddOrganizer = () => {
    setEditingOrganizer(null);
    setFormData({
      full_name: '',
      email: '',
      company_name: '',
      business_name: ''
    });
    setIsDialogOpen(true);
  };

  const handleEditOrganizer = (organizer: Organizer) => {
    setEditingOrganizer(organizer);
    setFormData({
      full_name: organizer.full_name,
      email: organizer.email,
      company_name: organizer.company_name || '',
      business_name: organizer.business_name || ''
    });
    setIsDialogOpen(true);
  };

  const handleDeleteOrganizer = async (organizerId: string) => {
    if (window.confirm('Are you sure you want to delete this organizer? This action cannot be undone.')) {
      try {
        await deleteOrganizer.mutateAsync(organizerId);
      } catch (error) {
        console.error('Error deleting organizer:', error);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.full_name || !formData.email) {
      toast({
        title: "Error",
        description: "Full name and email are required",
        variant: "destructive",
      });
      return;
    }

    try {
      if (editingOrganizer) {
        await updateOrganizer.mutateAsync({
          id: editingOrganizer.id,
          ...formData
        });
      } else {
        await createOrganizer.mutateAsync(formData);
      }
      
      setIsDialogOpen(false);
      setFormData({
        full_name: '',
        email: '',
        company_name: '',
        business_name: ''
      });
    } catch (error) {
      console.error('Error saving organizer:', error);
    }
  };

  // Redirect if not super admin
  if (!user || !isSuperAdmin) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
        <Header onBackToEvents={handleBackToDashboard} showBackButton={true} userRole={userRole} />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center">
            <Shield className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
            <p className="text-gray-600 mt-2">You don't have permission to access the Organizers Management page.</p>
            <Button onClick={handleBackToDashboard} className="mt-4">
              Back to Dashboard
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header 
        onBackToEvents={handleBackToDashboard}
        showBackButton={true}
        userRole={userRole}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center mb-2">
              <Users className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-3xl font-bold text-blue-800">Organizers Management</h1>
            </div>
            <p className="text-gray-600">Manage event organizers and their information</p>
          </div>

          {/* Add Organizer Button */}
          <div className="mb-6">
            <Button onClick={handleAddOrganizer} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Organizer
            </Button>
          </div>

          {/* Organizers Table */}
          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg text-blue-800">Event Organizers</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-600 mt-2">Loading organizers...</p>
                </div>
              ) : organizers && organizers.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Company</TableHead>
                      <TableHead>Business</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {organizers.map((organizer) => (
                      <TableRow key={organizer.id}>
                        <TableCell className="font-medium">{organizer.full_name}</TableCell>
                        <TableCell>{organizer.email}</TableCell>
                        <TableCell>
                          {organizer.company_name ? (
                            <Badge variant="outline" className="text-blue-700 border-blue-300">
                              {organizer.company_name}
                            </Badge>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {organizer.business_name ? (
                            <Badge variant="outline" className="text-green-700 border-green-300">
                              {organizer.business_name}
                            </Badge>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditOrganizer(organizer)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteOrganizer(organizer.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">No Organizers Found</h3>
                  <p className="text-gray-500 mb-4">Get started by adding your first event organizer.</p>
                  <Button onClick={handleAddOrganizer} className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Organizer
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Add/Edit Organizer Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingOrganizer ? 'Edit Organizer' : 'Add New Organizer'}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="full_name">Full Name *</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => setFormData({...formData, full_name: e.target.value})}
                placeholder="Enter full name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                placeholder="Enter email address"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company_name">Company Name</Label>
              <Input
                id="company_name"
                value={formData.company_name}
                onChange={(e) => setFormData({...formData, company_name: e.target.value})}
                placeholder="Enter company name (optional)"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="business_name">Business Name</Label>
              <Input
                id="business_name"
                value={formData.business_name}
                onChange={(e) => setFormData({...formData, business_name: e.target.value})}
                placeholder="Enter business name (optional)"
              />
            </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createOrganizer.isPending || updateOrganizer.isPending}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {createOrganizer.isPending || updateOrganizer.isPending ? 'Saving...' : (editingOrganizer ? 'Update' : 'Create')}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrganizersManagementPage; 