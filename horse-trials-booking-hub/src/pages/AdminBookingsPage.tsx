import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { AdminBookings } from '@/components/admin/AdminBookings'; // Correctly import the named export
import { useEvent } from '@/hooks/useEvents';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

const AdminBookingsPage = () => {
  const { eventId } = useParams<{ eventId: string }>();
  const navigate = useNavigate();
  const { data: event, isLoading, error } = useEvent(eventId);
      // Auth and role checks are now handled by AuthWrapper in App.tsx

  if (!eventId) {
    // Should not happen if route is matched, but good for safety
    return <Navigate to="/admin/dashboard" />;
  }

  const handleBack = () => {
    navigate(`/admin/events/${eventId}`); // Navigate back to the specific event management page
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
      </div>
    );
  }

  if (error || !event) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50 text-center py-10">
        <p className="text-red-600">Error loading event data or event not found.</p>
        <Button onClick={() => navigate('/admin/events')} className="mt-4">Back to Events List</Button>
      </div>
    );
  }

  return <AdminBookings event={event} onBack={handleBack} />;
};

export default AdminBookingsPage;