import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import IndexPage from './IndexPage';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { useEvents } from '@/hooks/useEvents';

// Mock dependencies
vi.mock('@supabase/auth-helpers-react');
vi.mock('@/hooks/useUserRoles');
vi.mock('@/hooks/useEvents');
vi.mock('@/components/EventList', () => ({
  EventList: ({ onEventSelect }: any) => (
    <div data-testid="event-list">
      <button onClick={() => onEventSelect({ id: 'event-1', name: 'Test Event' })} data-testid="select-event-btn">
        Select Event
      </button>
    </div>
  )
}));
vi.mock('@/components/EventDetails', () => ({
  EventDetails: ({ event, onBooking, initialSelectedDate }: any) => (
    <div data-testid="event-details">
      <h2>{event?.name}</h2>
      <p>Initial Date: {initialSelectedDate}</p>
      <button onClick={() => onBooking([{ activity_type: 'dressage', time: '10:00 AM' }])} data-testid="book-event-btn">
        Book Event
      </button>
    </div>
  )
}));
vi.mock('@/components/BookingFlow', () => ({
  BookingFlow: ({ selectedSlots, event, onBack }: any) => (
    <div data-testid="booking-flow">
      <h2>Booking Flow</h2>
      <p>Slots: {selectedSlots.length}</p>
      <p>Event: {event?.name}</p>
      <button onClick={onBack} data-testid="back-to-details-btn">Back</button>
    </div>
  )
}));
vi.mock('@/components/PublicBookings', () => ({
  PublicBookings: ({ event, onBack }: any) => (
    <div data-testid="public-bookings">
      <h2>Public Bookings</h2>
      <p>Event: {event?.name}</p>
      <button onClick={onBack} data-testid="back-from-public-btn">Back</button>
    </div>
  )
}));
vi.mock('@/components/Header', () => ({
  Header: ({ onAdminLogin, onBackToEvents, onViewPublicBookings, isAdmin, userRole, showBackButton, showPublicBookings }: any) => (
    <div data-testid="header">
      {showBackButton && <button onClick={onBackToEvents} data-testid="back-to-events-btn">Back to Events</button>}
      {showPublicBookings && <button onClick={onViewPublicBookings} data-testid="view-public-bookings-btn">View Public Bookings</button>}
      <button onClick={onAdminLogin} data-testid="admin-login-btn">Admin Login</button>
      <span data-testid="admin-status">Admin: {isAdmin ? 'Yes' : 'No'}</span>
      <span data-testid="user-role">Role: {userRole}</span>
    </div>
  )
}));
vi.mock('@/components/CartWidget', () => ({
  CartWidget: ({ onProceedToBooking, isOpen, onClose, eventId }: any) => (
    isOpen ? (
      <div data-testid="cart-widget">
        <h3>Cart</h3>
        <p>Event ID: {eventId}</p>
        <button onClick={() => onProceedToBooking([{ activity_type: 'dressage', time: '10:00 AM' }])} data-testid="proceed-to-booking-btn">
          Proceed to Booking
        </button>
        <button onClick={onClose} data-testid="close-cart-btn">Close</button>
      </div>
    ) : null
  )
}));
vi.mock('@/components/FloatingCartIcon', () => ({
  FloatingCartIcon: ({ onClick, eventId }: any) => (
    <button onClick={onClick} data-testid="floating-cart-icon">
      Cart ({eventId})
    </button>
  )
}));

const mockUseSession = vi.mocked(useSession);
const mockUseUserRole = vi.mocked(useUserRole);
const mockUseEvents = vi.mocked(useEvents);

const createQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>
        {component}
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('IndexPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mocks
    mockUseSession.mockReturnValue(null);
    mockUseUserRole.mockReturnValue({ userRole: null, isLoading: false });
    mockUseEvents.mockReturnValue({ 
      data: [{ 
        id: 'event-1', 
        name: 'Test Event',
        organizer: null,
        created_at: '2024-01-01T00:00:00Z',
        created_by: 'user-1',
        end_date: '2024-12-31T23:59:59Z',
        is_active: true,
        schooling_end_date: '2024-12-31T23:59:59Z',
        schooling_start_date: '2024-01-01T00:00:00Z',
        start_date: '2024-01-01T00:00:00Z'
      }], 
      isLoading: false, 
      error: null,
      isError: false,
      isPending: false,
      isLoadingError: false,
      isRefetchError: false,
      isSuccess: true,
      status: 'success',
      fetchStatus: 'idle',
      refetch: vi.fn(),
      dataUpdatedAt: 1,
      errorUpdatedAt: 0,
      failureCount: 0,
      failureReason: null,
      errorUpdateCount: 0,
      isFetched: true,
      isFetchedAfterMount: true,
      isInitialLoading: false,
      isPaused: false,
      isPlaceholderData: false,
      isRefetching: false,
      isStale: false,
      promise: Promise.resolve([]),
      isFetching: false,
    } as any);
  });

  describe('Initial Events View', () => {
    it('renders events view by default', () => {
      renderWithProviders(<IndexPage />);
      
      expect(screen.getByTestId('events-view')).toBeInTheDocument();
      expect(screen.getByTestId('event-list')).toBeInTheDocument();
      expect(screen.getByTestId('events-title')).toHaveTextContent('Upcoming Events');
      expect(screen.getByTestId('hero-description')).toHaveTextContent('Next Rider Up helps you book training rides');
    });

    it('displays version indicator', () => {
      renderWithProviders(<IndexPage />);
      
      expect(screen.getByTestId('version-indicator')).toHaveTextContent('Latest Code ✓');
    });

    it('renders header with admin login button', () => {
      renderWithProviders(<IndexPage />);
      
      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('admin-login-btn')).toBeInTheDocument();
    });

    it('shows correct admin status for non-admin user', () => {
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2024-01-01T00:00:00Z',
          email: '<EMAIL>',
          phone: null,
          role: 'authenticated',
          updated_at: '2024-01-01T00:00:00Z'
        },
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
        token_type: 'bearer'
      });
      mockUseUserRole.mockReturnValue({ userRole: 'rider', isLoading: false });

      renderWithProviders(<IndexPage />);
      
      expect(screen.getByTestId('admin-status')).toHaveTextContent('Admin: No');
      expect(screen.getByTestId('user-role')).toHaveTextContent('Role: rider');
    });

    it('shows correct admin status for admin user', () => {
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2024-01-01T00:00:00Z',
          email: '<EMAIL>',
          phone: null,
          role: 'authenticated',
          updated_at: '2024-01-01T00:00:00Z'
        },
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
        token_type: 'bearer'
      });
      mockUseUserRole.mockReturnValue({ userRole: 'super_admin', isLoading: false });

      renderWithProviders(<IndexPage />);
      
      expect(screen.getByTestId('admin-status')).toHaveTextContent('Admin: Yes');
      expect(screen.getByTestId('user-role')).toHaveTextContent('Role: super_admin');
    });
  });

  describe('Event Selection and Navigation', () => {
    it('navigates to event details when event is selected', async () => {
      const user = userEvent.setup();
      renderWithProviders(<IndexPage />);
      
      await user.click(screen.getByTestId('select-event-btn'));
      
      expect(screen.getByTestId('details-view')).toBeInTheDocument();
      expect(screen.getByTestId('event-details')).toBeInTheDocument();
      expect(screen.getByText('Test Event')).toBeInTheDocument();
    });

    it('shows back button when not in events view', async () => {
      const user = userEvent.setup();
      renderWithProviders(<IndexPage />);
      
      // Navigate to event details
      await user.click(screen.getByTestId('select-event-btn'));
      
      expect(screen.getByTestId('back-to-events-btn')).toBeInTheDocument();
    });

    it('returns to events view when back button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<IndexPage />);
      
      // Navigate to event details
      await user.click(screen.getByTestId('select-event-btn'));
      expect(screen.getByTestId('details-view')).toBeInTheDocument();
      
      // Go back to events
      await user.click(screen.getByTestId('back-to-events-btn'));
      
      expect(screen.getByTestId('events-view')).toBeInTheDocument();
      expect(screen.getByTestId('event-list')).toBeInTheDocument();
    });

    it('shows public bookings button in event details view', async () => {
      const user = userEvent.setup();
      renderWithProviders(<IndexPage />);
      
      // Navigate to event details
      await user.click(screen.getByTestId('select-event-btn'));
      
      expect(screen.getByTestId('view-public-bookings-btn')).toBeInTheDocument();
    });
  });

  describe('Booking Flow', () => {
    it('navigates to booking flow when booking is initiated', async () => {
      const user = userEvent.setup();
      renderWithProviders(<IndexPage />);
      
      // Navigate to event details
      await user.click(screen.getByTestId('select-event-btn'));
      
      // Start booking
      await user.click(screen.getByTestId('book-event-btn'));
      
      expect(screen.getByTestId('booking-view')).toBeInTheDocument();
      expect(screen.getByTestId('booking-flow')).toBeInTheDocument();
      expect(screen.getByText('Slots: 1')).toBeInTheDocument();
      expect(screen.getByText('Event: Test Event')).toBeInTheDocument();
    });

    it('returns to event details from booking flow', async () => {
      const user = userEvent.setup();
      renderWithProviders(<IndexPage />);
      
      // Navigate to booking flow
      await user.click(screen.getByTestId('select-event-btn'));
      await user.click(screen.getByTestId('book-event-btn'));
      
      // Go back to details
      await user.click(screen.getByTestId('back-to-details-btn'));
      
      expect(screen.getByTestId('details-view')).toBeInTheDocument();
      expect(screen.getByTestId('event-details')).toBeInTheDocument();
    });
  });

  describe('Public Bookings View', () => {
    it('navigates to public bookings view', async () => {
      const user = userEvent.setup();
      renderWithProviders(<IndexPage />);
      
      // Navigate to event details
      await user.click(screen.getByTestId('select-event-btn'));
      
      // View public bookings
      await user.click(screen.getByTestId('view-public-bookings-btn'));
      
      expect(screen.getByTestId('public-view')).toBeInTheDocument();
      expect(screen.getByTestId('public-bookings')).toBeInTheDocument();
      expect(screen.getByText('Event: Test Event')).toBeInTheDocument();
    });

    it('returns to event details from public bookings', async () => {
      const user = userEvent.setup();
      renderWithProviders(<IndexPage />);
      
      // Navigate to public bookings
      await user.click(screen.getByTestId('select-event-btn'));
      await user.click(screen.getByTestId('view-public-bookings-btn'));
      
      // Go back to details
      await user.click(screen.getByTestId('back-from-public-btn'));
      
      expect(screen.getByTestId('details-view')).toBeInTheDocument();
      expect(screen.getByTestId('event-details')).toBeInTheDocument();
    });
  });

  describe('Cart Functionality', () => {
    it('passes event ID to cart when in details view', async () => {
      const user = userEvent.setup();
      renderWithProviders(<IndexPage />);
      
      // Navigate to event details
      await user.click(screen.getByTestId('select-event-btn'));
      
      expect(screen.getByTestId('floating-cart-icon')).toHaveTextContent('Cart (event-1)');
    });
  });

  describe('UI Elements', () => {
    it('renders hero section with logo and description', () => {
      renderWithProviders(<IndexPage />);
      
      expect(screen.getByTestId('hero-section')).toBeInTheDocument();
      expect(screen.getByTestId('hero-logo')).toBeInTheDocument();
      expect(screen.getByTestId('hero-description')).toHaveTextContent('Next Rider Up helps you book training rides');
    });

    it('renders decorative SVG shape in hero section', () => {
      renderWithProviders(<IndexPage />);
      
      expect(screen.getByTestId('hero-decoration')).toBeInTheDocument();
    });

    it('applies correct CSS classes for layout', () => {
      const { container } = renderWithProviders(<IndexPage />);
      
      const mainDiv = container.firstChild as HTMLElement;
      expect(mainDiv).toHaveClass('min-h-screen', 'bg-cream', 'flex', 'flex-col');
    });
  });

  describe('Error Handling', () => {
    it('handles missing event gracefully', () => {
      mockUseEvents.mockReturnValue({ 
        data: [], 
        isLoading: false, 
        error: null,
        isError: false,
        isPending: false,
        isLoadingError: false,
        isRefetchError: false,
        isSuccess: true,
        status: 'success',
        fetchStatus: 'idle',
        refetch: vi.fn(),
        dataUpdatedAt: 1,
        errorUpdatedAt: 0,
        failureCount: 0,
        failureReason: null,
        errorUpdateCount: 0,
        isFetched: true,
        isFetchedAfterMount: true,
        isInitialLoading: false,
        isPaused: false,
        isPlaceholderData: false,
        isRefetching: false,
        isStale: false,
        promise: Promise.resolve([]),
        isFetching: false,
      } as any);

      renderWithProviders(<IndexPage />);
      
      expect(screen.getByTestId('events-view')).toBeInTheDocument();
      expect(screen.getByTestId('event-list')).toBeInTheDocument();
    });

    it('handles loading state gracefully', () => {
      mockUseEvents.mockReturnValue({ 
        data: undefined, 
        isLoading: true, 
        error: null,
        isError: false,
        isPending: true,
        isLoadingError: false,
        isRefetchError: false,
        isSuccess: false,
        status: 'pending',
        fetchStatus: 'fetching',
        refetch: vi.fn(),
        dataUpdatedAt: 0,
        errorUpdatedAt: 0,
        failureCount: 0,
        failureReason: null,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isInitialLoading: true,
        isPaused: false,
        isPlaceholderData: false,
        isRefetching: false,
        isStale: false,
        promise: Promise.resolve(undefined),
        isFetching: true,
      } as any);

      renderWithProviders(<IndexPage />);
      
      expect(screen.getByTestId('events-view')).toBeInTheDocument();
      expect(screen.getByTestId('event-list')).toBeInTheDocument();
    });
  });
}); 