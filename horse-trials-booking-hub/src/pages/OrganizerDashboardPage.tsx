import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Plus, ChevronRight, Users, Settings, List } from 'lucide-react';
import { useAccessibleEvents, useUserRole } from '@/hooks/useUserRoles';
import { formatDateForDisplay } from '@/utils/dateUtils';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useDeleteEvent } from '@/hooks/useEvents';

const OrganizerDashboardPage = () => {
  const navigate = useNavigate();
  const { data: events, isLoading } = useAccessibleEvents();
  const { userRole } = useUserRole();
  const deleteEvent = useDeleteEvent();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [eventToDelete, setEventToDelete] = React.useState<any>(null);

  const handleEventSelect = (eventId: string) => {
    navigate(`/admin/events/${eventId}`);
  };

  const handleCreateEvent = () => {
    navigate('/admin/events/create');
  };

  const handleBackToMain = () => {
    navigate('/');
  };

  const handleProfileSettings = () => {
    navigate('/admin/profile');
  };

  const handleViewAllEvents = () => {
    navigate('/admin/events');
  };

  // Filter to only show events organized by the current user
  const myEvents = events?.filter(event => event.organizer_id) || [];

  // Helper to determine if user can delete this event
  const canDeleteEvent = (event: any) => {
    if (userRole === 'super_admin') return true;
    if (userRole === 'organizer' && event.organizer_id) return true;
    return false;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header
        onBackToEvents={handleBackToMain}
        showBackButton={true}
        userRole={userRole}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-green-800 mb-2">Organizer Dashboard</h1>
            <p className="text-gray-600">Manage your events and view your bookings</p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="border-green-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-green-800 flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Total Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">{myEvents.length}</div>
                <p className="text-sm text-gray-600 mt-1">Events you're organizing</p>
              </CardContent>
            </Card>

            <Card className="border-blue-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-blue-800 flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Active Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">
                  {myEvents.filter(event => event.is_active).length}
                </div>
                <p className="text-sm text-gray-600 mt-1">Currently active</p>
              </CardContent>
            </Card>

            <Card className="border-amber-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-amber-800 flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Inactive Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-amber-600">
                  {myEvents.filter(event => !event.is_active).length}
                </div>
                <p className="text-sm text-gray-600 mt-1">Currently inactive</p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="mt-8">
            <Card className="border-blue-200">
              <CardHeader>
                <CardTitle className="text-lg text-blue-800">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={handleCreateEvent}
                    variant="outline"
                    className="h-16 text-green-600 border-green-300 hover:bg-green-50"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Create New Event
                  </Button>
                  <Button
                    onClick={handleViewAllEvents}
                    variant="outline"
                    className="h-16 text-blue-600 border-blue-300 hover:bg-blue-50"
                  >
                    <List className="h-5 w-5 mr-2" />
                    View All Events
                  </Button>
                  <Button
                    onClick={handleProfileSettings}
                    variant="outline"
                    className="h-16 text-purple-600 border-purple-300 hover:bg-purple-50"
                  >
                    <Settings className="h-5 w-5 mr-2" />
                    Profile Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      {/* Delete Confirmation Dialog */}
      {deleteDialogOpen && (
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Event</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete "{eventToDelete?.name}"? This action cannot be undone and will remove all related data.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (eventToDelete) {
                    deleteEvent.mutate(eventToDelete.id, {
                      onSuccess: () => {
                        setDeleteDialogOpen(false);
                        setEventToDelete(null);
                      }
                    });
                  }
                }}
                className="bg-red-600 hover:bg-red-700 text-white"
                disabled={deleteEvent.isPending}
              >
                {deleteEvent.isPending ? 'Deleting...' : 'Delete Event'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
};

export default OrganizerDashboardPage; 