import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Plus, ChevronRight, Users, Settings, Search, Filter, Shield, Target, FileText, List } from 'lucide-react';
import { useAccessibleEvents, useUserRole } from '@/hooks/useUserRoles';
import { useSession } from '@supabase/auth-helpers-react';
import { formatDateForDisplay } from '@/utils/dateUtils';

const SuperAdminDashboardPage = () => {
  const navigate = useNavigate();
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  const { data: events, isLoading: eventsLoading } = useAccessibleEvents();
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [organizerFilter, setOrganizerFilter] = useState<string>('all');

  // Check if user is super admin using userRole
  const isSuperAdmin = userRole === 'super_admin';

  const handleEventSelect = (eventId: string) => {
    navigate(`/admin/events/${eventId}`);
  };

  const handleCreateEvent = () => {
    navigate('/admin/events/create');
  };

  const handleBackToMain = () => {
    navigate('/');
  };

  const handleManageLevels = () => {
    navigate('/admin/levels');
  };

  const handleManageDressageTests = () => {
    navigate('/admin/dressage-tests');
  };

  const handleManageUsers = () => {
    navigate('/admin/users');
  };

  const handleManageOrganizers = () => {
    navigate('/admin/organizers');
  };

  const handleViewAllEvents = () => {
    navigate('/admin/events');
  };

  // Get unique organizers for filter
  const organizers = useMemo(() => {
    if (!events) return [];
    const organizerMap = new Map();
    events.forEach(event => {
      if (event.organizer) {
        organizerMap.set(event.organizer.id, event.organizer.full_name || event.organizer.email);
      }
    });
    return Array.from(organizerMap.entries()).map(([id, name]) => ({ id, name }));
  }, [events]);

  // Filter events
  const filteredEvents = useMemo(() => {
    if (!events) return [];
    
    return events.filter(event => {
      // Search term filter
      const matchesSearch = !searchTerm || 
        event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (event.organizer?.full_name || event.organizer?.email || '').toLowerCase().includes(searchTerm.toLowerCase());
      
      // Status filter
      const matchesStatus = statusFilter === 'all' || 
        (statusFilter === 'active' && event.is_active) ||
        (statusFilter === 'inactive' && !event.is_active);
      
      // Organizer filter
      const matchesOrganizer = organizerFilter === 'all' || 
        event.organizer?.id === organizerFilter;
      
      return matchesSearch && matchesStatus && matchesOrganizer;
    });
  }, [events, searchTerm, statusFilter, organizerFilter]);

  // Stats
  const totalEvents = events?.length || 0;
  const activeEvents = events?.filter(event => event.is_active).length || 0;
  const totalOrganizers = organizers.length;

  // Show loading while checking role
  if (userRole === null) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50 flex items-center justify-center">
        <div className="text-center">
          <Shield className="w-16 h-16 text-purple-400 mx-auto mb-4 animate-pulse" data-testid="loading-shield-icon" />
          <p className="text-gray-600">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // Show access denied if not super admin
  if (userRole !== 'super_admin') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
        <Header onBackToEvents={handleBackToMain} showBackButton={true} userRole={userRole} />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center">
            <Shield className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
            <p className="text-gray-600 mt-2">You don't have permission to access the Super Admin Dashboard.</p>
            <Button onClick={handleBackToMain} className="mt-4">
              Back to Home
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header
        onBackToEvents={handleBackToMain}
        showBackButton={true}
        userRole={userRole}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="mb-8">
            <div className="flex items-center mb-2">
              <Shield className="h-8 w-8 text-purple-600 mr-3" />
              <h1 className="text-3xl font-bold text-purple-800">Super Admin Dashboard</h1>
            </div>
            <p className="text-gray-600">Manage all events, users, and app-wide settings</p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="border-purple-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-purple-800 flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Total Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-600">{totalEvents}</div>
                <p className="text-sm text-gray-600 mt-1">All events in system</p>
              </CardContent>
            </Card>

            <Card className="border-green-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-green-800 flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Active Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">{activeEvents}</div>
                <p className="text-sm text-gray-600 mt-1">Currently active</p>
              </CardContent>
            </Card>

            <Card className="border-blue-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-blue-800 flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Organizers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">{totalOrganizers}</div>
                <p className="text-sm text-gray-600 mt-1">Event organizers</p>
              </CardContent>
            </Card>

            <Card className="border-amber-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-amber-800 flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button
                  onClick={handleManageUsers}
                  className="w-full bg-purple-600 hover:bg-purple-700"
                >
                  <Users className="h-4 w-4 mr-2" /> Manage Users
                </Button>
                <Button
                  onClick={handleManageOrganizers}
                  className="w-full bg-indigo-600 hover:bg-indigo-700"
                >
                  <Users className="h-4 w-4 mr-2" /> Manage Organizers
                </Button>
                <Button
                  onClick={handleManageLevels}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  <Target className="h-4 w-4 mr-2" /> Manage Levels
                </Button>
                <Button
                  onClick={handleManageDressageTests}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  <FileText className="h-4 w-4 mr-2" /> Manage Tests
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="mt-8">
            <Card className="border-purple-200">
              <CardHeader>
                <CardTitle className="text-lg text-purple-800">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Button
                    onClick={handleCreateEvent}
                    variant="outline"
                    className="h-16 text-green-600 border-green-300 hover:bg-green-50"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Create Event
                  </Button>
                  <Button
                    onClick={handleViewAllEvents}
                    variant="outline"
                    className="h-16 text-blue-600 border-blue-300 hover:bg-blue-50"
                  >
                    <List className="h-5 w-5 mr-2" />
                    View All Events
                  </Button>
                  <Button
                    onClick={handleManageUsers}
                    variant="outline"
                    className="h-16 text-purple-600 border-purple-300 hover:bg-purple-50"
                  >
                    <Users className="h-5 w-5 mr-2" />
                    Manage Users
                  </Button>
                  <Button
                    onClick={handleManageOrganizers}
                    variant="outline"
                    className="h-16 text-indigo-600 border-indigo-300 hover:bg-indigo-50"
                  >
                    <Users className="h-5 w-5 mr-2" />
                    Manage Organizers
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* App Settings Section */}
          <div className="mt-8">
            <Card className="border-purple-200">
              <CardHeader>
                <CardTitle className="text-lg text-purple-800">App-Wide Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={handleManageLevels}
                    variant="outline"
                    className="h-16 text-blue-600 border-blue-300 hover:bg-blue-50"
                  >
                    <Settings className="h-5 w-5 mr-2" />
                    Manage Levels
                  </Button>
                  <Button
                    onClick={handleManageDressageTests}
                    variant="outline"
                    className="h-16 text-green-600 border-green-300 hover:bg-green-50"
                  >
                    <Settings className="h-5 w-5 mr-2" />
                    Manage Dressage Tests
                  </Button>
                  <Button
                    onClick={handleManageUsers}
                    variant="outline"
                    className="h-16 text-purple-600 border-purple-300 hover:bg-purple-50"
                  >
                    <Users className="h-5 w-5 mr-2" />
                    Manage Users
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SuperAdminDashboardPage; 