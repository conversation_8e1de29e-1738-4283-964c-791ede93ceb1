import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter, Routes, Route, useParams } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { createTestQueryClient } from '../test/test-utils';
import { vi } from 'vitest';
import AdminBookingsPage from './AdminBookingsPage';
import { useEvent } from '@/hooks/useEvents';

// Mock the hooks
vi.mock('@/hooks/useEvents');
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: vi.fn(),
  };
});
vi.mock('@/components/admin/AdminBookings', () => ({
  AdminBookings: ({ event, onBack }: { event: any; onBack: () => void }) => (
    <div data-testid="admin-bookings-component">
      <h1>Admin Bookings for {event.name}</h1>
      <button onClick={onBack} data-testid="back-button">
        Back to Event
      </button>
    </div>
  ),
}));

const mockUseEvent = vi.mocked(useEvent);
const mockUseParams = vi.mocked(useParams);

describe('AdminBookingsPage', () => {
  const mockEvent = {
    id: 'event-123',
    name: 'Test Event',
    start_date: '2024-01-01',
    end_date: '2024-01-02',
    event_type: 'dressage' as const,
    created_by: 'user-123',
    created_at: '2024-01-01T00:00:00Z',
    is_active: true,
    organizer_id: 'org-123',
    organizer: {
      id: 'org-123',
      full_name: 'Test Organizer',
      email: '<EMAIL>',
      company_name: 'Test Company',
    },
  };

  const renderWithRouter = (eventId: string) => {
    mockUseParams.mockReturnValue({ eventId });
    return render(
      <QueryClientProvider client={createTestQueryClient()}>
        <MemoryRouter initialEntries={[`/admin/events/${eventId}/bookings`]}>
          <Routes>
            <Route path="/admin/events/:eventId/bookings" element={<AdminBookingsPage />} />
            <Route path="/admin/dashboard" element={<div>Dashboard</div>} />
            <Route path="/admin/events" element={<div>Events List</div>} />
          </Routes>
        </MemoryRouter>
      </QueryClientProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseParams.mockReturnValue({ eventId: 'event-123' });
  });

  describe('Route Parameters', () => {
    it('redirects to dashboard when no eventId is provided', () => {
      mockUseParams.mockReturnValue({ eventId: undefined });
      
      render(
        <QueryClientProvider client={createTestQueryClient()}>
          <MemoryRouter initialEntries={['/admin/dashboard']}>
            <Routes>
              <Route path="/admin/dashboard" element={<div data-testid="dashboard">Dashboard</div>} />
            </Routes>
          </MemoryRouter>
        </QueryClientProvider>
      );

      expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    it('shows loading spinner when event is loading', () => {
      mockUseEvent.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
        isError: false,
        isSuccess: false,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'pending',
        fetchStatus: 'fetching',
      });

      renderWithRouter('event-123');

      // Use a more appropriate selector for the SVG spinner
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('shows loading spinner with correct styling', () => {
      mockUseEvent.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
        isError: false,
        isSuccess: false,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'pending',
        fetchStatus: 'fetching',
      });

      renderWithRouter('event-123');

      const loadingContainer = screen.getByTestId('loading-spinner').closest('div');
      expect(loadingContainer).toHaveClass('min-h-screen', 'bg-gradient-to-br', 'from-green-50', 'to-amber-50');
      
      const spinner = screen.getByTestId('loading-spinner');
      expect(spinner).toHaveClass('h-8', 'w-8', 'animate-spin', 'text-green-600');
    });
  });

  describe('Error State', () => {
    it('shows error message when event fails to load', () => {
      mockUseEvent.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error('Failed to load event'),
        isError: true,
        isSuccess: false,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'error',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      expect(screen.getByText(/error loading event data or event not found/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /back to events list/i })).toBeInTheDocument();
    });

    it('shows error message when event is null', () => {
      mockUseEvent.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
        isError: false,
        isSuccess: true,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'success',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      expect(screen.getByText(/error loading event data or event not found/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /back to events list/i })).toBeInTheDocument();
    });

    it('has correct error page styling', () => {
      mockUseEvent.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error('Failed to load event'),
        isError: true,
        isSuccess: false,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'error',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      const errorContainer = screen.getByText(/error loading event data/i).parentElement;
      expect(errorContainer).toHaveClass('min-h-screen', 'bg-gradient-to-br', 'from-green-50', 'to-amber-50', 'text-center', 'py-10');
      
      const errorMessage = screen.getByText(/error loading event data/i);
      expect(errorMessage).toHaveClass('text-red-600');
    });
  });

  describe('Successful Rendering', () => {
    it('renders AdminBookings component when event loads successfully', async () => {
      mockUseEvent.mockReturnValue({
        data: mockEvent,
        isLoading: false,
        error: null,
        isError: false,
        isSuccess: true,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'success',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      await waitFor(() => {
        expect(screen.getByTestId('admin-bookings-component')).toBeInTheDocument();
      });

      expect(screen.getByText(`Admin Bookings for ${mockEvent.name}`)).toBeInTheDocument();
    });

    it('passes correct props to AdminBookings component', async () => {
      mockUseEvent.mockReturnValue({
        data: mockEvent,
        isLoading: false,
        error: null,
        isError: false,
        isSuccess: true,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'success',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      await waitFor(() => {
        expect(screen.getByTestId('admin-bookings-component')).toBeInTheDocument();
      });

      // The back button should be rendered by the AdminBookings component
      expect(screen.getByTestId('back-button')).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('navigates back to event management page when back button is clicked', async () => {
      mockUseEvent.mockReturnValue({
        data: mockEvent,
        isLoading: false,
        error: null,
        isError: false,
        isSuccess: true,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'success',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      await waitFor(() => {
        expect(screen.getByTestId('admin-bookings-component')).toBeInTheDocument();
      });

      const backButton = screen.getByTestId('back-button');
      expect(backButton).toBeInTheDocument();
    });

    it('navigates to events list when error page back button is clicked', () => {
      mockUseEvent.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error('Failed to load event'),
        isError: true,
        isSuccess: false,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'error',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      const backButton = screen.getByRole('button', { name: /back to events list/i });
      expect(backButton).toBeInTheDocument();
    });
  });

  describe('Hook Integration', () => {
    it('calls useEvent with correct eventId', () => {
      mockUseEvent.mockReturnValue({
        data: mockEvent,
        isLoading: false,
        error: null,
        isError: false,
        isSuccess: true,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'success',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      expect(mockUseEvent).toHaveBeenCalledWith('event-123');
    });

    it('handles different event types correctly', async () => {
      const horseTrialsEvent = {
        ...mockEvent,
        event_type: 'horse_trials' as const,
        name: 'Horse Trials Event',
      };

      mockUseEvent.mockReturnValue({
        data: horseTrialsEvent,
        isLoading: false,
        error: null,
        isError: false,
        isSuccess: true,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'success',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      await waitFor(() => {
        expect(screen.getByText('Admin Bookings for Horse Trials Event')).toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases', () => {
    it('handles event with missing organizer gracefully', async () => {
      const eventWithoutOrganizer = {
        ...mockEvent,
        organizer_id: undefined,
        organizer: null,
      };

      mockUseEvent.mockReturnValue({
        data: eventWithoutOrganizer,
        isLoading: false,
        error: null,
        isError: false,
        isSuccess: true,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'success',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      await waitFor(() => {
        expect(screen.getByTestId('admin-bookings-component')).toBeInTheDocument();
      });

      expect(screen.getByText('Admin Bookings for Test Event')).toBeInTheDocument();
    });

    it('handles event with minimal required fields', async () => {
      const minimalEvent = {
        id: 'event-123',
        name: 'Minimal Event',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        event_type: 'dressage' as const,
      };

      mockUseEvent.mockReturnValue({
        data: minimalEvent,
        isLoading: false,
        error: null,
        isError: false,
        isSuccess: true,
        isFetching: false,
        isRefetching: false,
        refetch: vi.fn(),
        status: 'success',
        fetchStatus: 'idle',
      });

      renderWithRouter('event-123');

      await waitFor(() => {
        expect(screen.getByTestId('admin-bookings-component')).toBeInTheDocument();
      });

      expect(screen.getByText('Admin Bookings for Minimal Event')).toBeInTheDocument();
    });
  });
}); 