import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import AboutPage from './AboutPage';

// Mock dependencies
vi.mock('@/components/Header', () => ({
  Header: ({ showBackButton, onBackToEvents }: any) => (
    <div data-testid="header">
      {showBackButton && <button onClick={onBackToEvents} data-testid="back-to-events-btn">Back to Events</button>}
    </div>
  )
}));

const createQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>
        {component}
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('AboutPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the about page with correct content', () => {
      renderWithProviders(<AboutPage />);
      
      expect(screen.getByText('Why We Built It')).toBeInTheDocument();
      expect(screen.getByText(/We looked—twice/)).toBeInTheDocument();
      expect(screen.getByText(/At one event alone/)).toBeInTheDocument();
      expect(screen.getByText(/NextRiderUp was born from that frustration/)).toBeInTheDocument();
    });

    it('renders header with back button', () => {
      renderWithProviders(<AboutPage />);
      
      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('back-to-events-btn')).toBeInTheDocument();
    });

    it('renders main content section', () => {
      const { container } = renderWithProviders(<AboutPage />);
      
      const section = container.querySelector('section');
      expect(section).toHaveClass('min-h-screen', 'bg-cream', 'flex', 'flex-col', 'items-center', 'justify-center', 'py-16', 'px-4');
    });

    it('renders content container with correct styling', () => {
      const { container } = renderWithProviders(<AboutPage />);
      
      const contentDiv = container.querySelector('.max-w-2xl');
      expect(contentDiv).toHaveClass('max-w-2xl', 'w-full', 'bg-white', 'rounded-lg', 'shadow-lg', 'p-8', 'border', 'border-cream-light');
    });
  });

  describe('Content', () => {
    it('displays the main heading', () => {
      renderWithProviders(<AboutPage />);
      
      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toHaveTextContent('Why We Built It');
      expect(heading).toHaveClass('font-playfair', 'text-3xl', 'md:text-4xl', 'font-bold', 'text-primary', 'mb-6', 'text-center');
    });

    it('displays the first paragraph about the problem', () => {
      renderWithProviders(<AboutPage />);
      
      const paragraph = screen.getByText(/We looked—twice. There was no platform built for managing ride times across dressage tests, jumping rounds, and cross-country training days./);
      expect(paragraph).toBeInTheDocument();
      expect(paragraph).toHaveClass('text-lg', 'text-text-secondary', 'leading-relaxed', 'mb-4');
    });

    it('displays the second paragraph about the scale', () => {
      renderWithProviders(<AboutPage />);
      
      const paragraph = screen.getByText(/At one event alone, over 400 rounds were scheduled in a single day, raising nearly \$10,000—all while using a tool that wasn't made for horse shows./);
      expect(paragraph).toBeInTheDocument();
      expect(paragraph).toHaveClass('text-lg', 'text-text-secondary', 'leading-relaxed', 'mb-4');
    });

    it('displays the third paragraph about the solution', () => {
      renderWithProviders(<AboutPage />);
      
      const paragraph = screen.getByText(/NextRiderUp was born from that frustration—and a deep love for making events smoother, safer, and more rider-friendly. Built by riders, for riders./);
      expect(paragraph).toBeInTheDocument();
      expect(paragraph).toHaveClass('text-lg', 'text-text-secondary', 'leading-relaxed');
    });
  });

  describe('Navigation', () => {
    it('shows back button in header', () => {
      renderWithProviders(<AboutPage />);
      
      expect(screen.getByTestId('back-to-events-btn')).toHaveTextContent('Back to Events');
    });

    it('header has showBackButton prop set to true', () => {
      renderWithProviders(<AboutPage />);
      
      // The mock component shows the back button when showBackButton is true
      expect(screen.getByTestId('back-to-events-btn')).toBeInTheDocument();
    });
  });

  describe('Styling and Layout', () => {
    it('applies correct background and layout classes', () => {
      const { container } = renderWithProviders(<AboutPage />);
      
      const section = container.querySelector('section');
      expect(section).toHaveClass('min-h-screen', 'bg-cream', 'flex', 'flex-col', 'items-center', 'justify-center');
    });

    it('applies correct padding and spacing', () => {
      const { container } = renderWithProviders(<AboutPage />);
      
      const section = container.querySelector('section');
      expect(section).toHaveClass('py-16', 'px-4');
    });

    it('applies correct content container styling', () => {
      const { container } = renderWithProviders(<AboutPage />);
      
      const contentDiv = container.querySelector('.max-w-2xl');
      expect(contentDiv).toHaveClass('max-w-2xl', 'w-full', 'bg-white', 'rounded-lg', 'shadow-lg', 'p-8');
    });

    it('applies correct border styling', () => {
      const { container } = renderWithProviders(<AboutPage />);
      
      const contentDiv = container.querySelector('.max-w-2xl');
      expect(contentDiv).toHaveClass('border', 'border-cream-light');
    });
  });

  describe('Typography', () => {
    it('uses correct font family for heading', () => {
      renderWithProviders(<AboutPage />);
      
      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toHaveClass('font-playfair');
    });

    it('uses correct text sizing for heading', () => {
      renderWithProviders(<AboutPage />);
      
      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toHaveClass('text-3xl', 'md:text-4xl');
    });

    it('uses correct text sizing for paragraphs', () => {
      renderWithProviders(<AboutPage />);
      
      const paragraph1 = screen.getByText(/We looked—twice/);
      const paragraph2 = screen.getByText(/At one event alone/);
      const paragraph3 = screen.getByText(/NextRiderUp was born from that frustration/);
      
      expect(paragraph1).toHaveClass('text-lg');
      expect(paragraph2).toHaveClass('text-lg');
      expect(paragraph3).toHaveClass('text-lg');
    });

    it('uses correct text color for paragraphs', () => {
      renderWithProviders(<AboutPage />);
      
      const paragraph1 = screen.getByText(/We looked—twice/);
      const paragraph2 = screen.getByText(/At one event alone/);
      const paragraph3 = screen.getByText(/NextRiderUp was born from that frustration/);
      
      expect(paragraph1).toHaveClass('text-text-secondary');
      expect(paragraph2).toHaveClass('text-text-secondary');
      expect(paragraph3).toHaveClass('text-text-secondary');
    });
  });

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      renderWithProviders(<AboutPage />);
      
      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toBeInTheDocument();
    });

    it('has proper semantic structure', () => {
      const { container } = renderWithProviders(<AboutPage />);
      
      const section = container.querySelector('section');
      expect(section).toBeInTheDocument();
    });
  });
}); 