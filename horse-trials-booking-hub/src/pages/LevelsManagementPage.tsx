import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Plus, Edit, Trash, Target, Filter } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { getErrorMessage } from '@/types/errors';

interface Level {
  id: string;
  name: string;
  discipline: string;
  description?: string;
  sort_order?: number;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

const LevelsManagementPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newLevel, setNewLevel] = useState({ name: '', discipline: '', description: '', sort_order: 0, is_active: true });
  const [editLevel, setEditLevel] = useState({ name: '', discipline: '', description: '', sort_order: 0, is_active: true });
  const [disciplineFilter, setDisciplineFilter] = useState<string>('all');

  // Fetch all levels
  const { data: levels, isLoading } = useQuery({
    queryKey: ['levels'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('levels_library')
        .select('*')
        .order('sort_order', { ascending: true });

      if (error) throw error;
      return data as Level[];
    },
  });

  // Filter levels by discipline
  const filteredLevels = levels?.filter(level => 
    disciplineFilter === 'all' || level.discipline === disciplineFilter
  ) || [];

  // Get unique disciplines for filter
  const disciplines = useMemo(() => {
    if (!levels) return [];
    const uniqueDisciplines = [...new Set(levels.map(level => level.discipline))];
    return uniqueDisciplines.sort();
  }, [levels]);

  // Add new level
  const addLevel = useMutation({
    mutationFn: async (level: { name: string; discipline: string; description?: string; sort_order?: number; is_active?: boolean }) => {
      const { data, error } = await supabase
        .from('levels_library')
        .insert(level)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['levels'] });
      setIsAdding(false);
      setNewLevel({ name: '', description: '' });
      toast({
        title: 'Level Added',
        description: 'The level has been successfully added.',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: getErrorMessage(error),
        variant: 'destructive',
      });
    },
  });

  // Update level
  const updateLevel = useMutation({
    mutationFn: async ({ id, level }: { id: string; level: { name: string; discipline: string; description?: string; sort_order?: number; is_active?: boolean } }) => {
      const { data, error } = await supabase
        .from('levels_library')
        .update(level)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['levels'] });
      setEditingId(null);
      setEditLevel({ name: '', description: '' });
      toast({
        title: 'Level Updated',
        description: 'The level has been successfully updated.',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: getErrorMessage(error),
        variant: 'destructive',
      });
    },
  });

  // Delete level
  const deleteLevel = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('levels_library')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['levels'] });
      toast({
        title: 'Level Deleted',
        description: 'The level has been successfully deleted.',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: getErrorMessage(error),
        variant: 'destructive',
      });
    },
  });

  const handleBackToDashboard = () => {
    navigate('/admin/super-admin-dashboard');
  };

  const handleAddLevel = () => {
    if (!newLevel.name.trim() || !newLevel.discipline.trim()) {
      toast({
        title: 'Error',
        description: 'Level name and discipline are required.',
        variant: 'destructive',
      });
      return;
    }

    addLevel.mutate(newLevel);
  };

  const handleEditLevel = (level: Level) => {
    setEditingId(level.id);
    setEditLevel({ 
      name: level.name, 
      discipline: level.discipline,
      description: level.description || '', 
      sort_order: level.sort_order || 0,
      is_active: level.is_active ?? true
    });
  };

  const handleUpdateLevel = () => {
    if (!editLevel.name.trim() || !editLevel.discipline.trim() || !editingId) return;

    updateLevel.mutate({ id: editingId, level: editLevel });
  };

  const handleDeleteLevel = (id: string) => {
    if (confirm('Are you sure you want to delete this level? This action cannot be undone.')) {
      deleteLevel.mutate(id);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header
        onBackToEvents={handleBackToDashboard}
        showBackButton={true}
        userRole={userRole}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <Button
                variant="ghost"
                onClick={handleBackToDashboard}
                className="text-green-700 hover:bg-green-50 mb-2"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              <h1 className="text-3xl font-bold text-green-800">Levels Management</h1>
              <p className="text-gray-600 mt-1">Manage competition levels available across all events</p>
            </div>
            <Button
              onClick={() => setIsAdding(true)}
              className="bg-green-600 hover:bg-green-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Level
            </Button>
          </div>

          {/* Add New Level Form */}
          {isAdding && (
            <Card className="mb-6 border-green-200">
              <CardHeader>
                <CardTitle className="text-green-800">Add New Level</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="levelName">Level Name *</Label>
                    <Input
                      id="levelName"
                      value={newLevel.name}
                      onChange={(e) => setNewLevel({ ...newLevel, name: e.target.value })}
                      placeholder="e.g., Beginner, Intermediate, Advanced"
                    />
                  </div>
                  <div>
                    <Label htmlFor="levelDiscipline">Discipline *</Label>
                    <Input
                      id="levelDiscipline"
                      value={newLevel.discipline}
                      onChange={(e) => setNewLevel({ ...newLevel, discipline: e.target.value })}
                      placeholder="e.g., Dressage, Jumping, Eventing"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="levelDescription">Description (Optional)</Label>
                  <Input
                    id="levelDescription"
                    value={newLevel.description}
                    onChange={(e) => setNewLevel({ ...newLevel, description: e.target.value })}
                    placeholder="Brief description of the level"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="levelSortOrder">Sort Order</Label>
                    <Input
                      id="levelSortOrder"
                      type="number"
                      value={newLevel.sort_order}
                      onChange={(e) => setNewLevel({ ...newLevel, sort_order: parseInt(e.target.value) || 0 })}
                      placeholder="0"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      id="levelActive"
                      type="checkbox"
                      checked={newLevel.is_active}
                      onChange={(e) => setNewLevel({ ...newLevel, is_active: e.target.checked })}
                      className="rounded"
                    />
                    <Label htmlFor="levelActive">Active</Label>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={handleAddLevel}
                    disabled={addLevel.isPending}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {addLevel.isPending ? 'Adding...' : 'Add Level'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsAdding(false);
                      setNewLevel({ name: '', discipline: '', description: '', sort_order: 0, is_active: true });
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Levels List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-green-800 flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Competition Levels ({filteredLevels.length})
                </CardTitle>
                {disciplines.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <Filter className="h-4 w-4 text-gray-500" />
                    <Select value={disciplineFilter} onValueChange={setDisciplineFilter}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Filter by discipline" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Disciplines</SelectItem>
                        {disciplines.map(discipline => (
                          <SelectItem key={discipline} value={discipline}>
                            {discipline}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">Loading levels...</div>
              ) : !levels || levels.length === 0 ? (
                <div className="text-center py-12">
                  <Target className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No levels yet</h3>
                  <p className="text-gray-600 mb-4">Add your first competition level to get started.</p>
                  <Button
                    onClick={() => setIsAdding(true)}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Level
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredLevels.map((level) => (
                    <Card key={level.id} className="border-gray-200">
                      <CardContent className="p-4">
                        {editingId === level.id ? (
                          <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor={`edit-name-${level.id}`}>Level Name *</Label>
                                <Input
                                  id={`edit-name-${level.id}`}
                                  value={editLevel.name}
                                  onChange={(e) => setEditLevel({ ...editLevel, name: e.target.value })}
                                />
                              </div>
                              <div>
                                <Label htmlFor={`edit-discipline-${level.id}`}>Discipline *</Label>
                                <Input
                                  id={`edit-discipline-${level.id}`}
                                  value={editLevel.discipline}
                                  onChange={(e) => setEditLevel({ ...editLevel, discipline: e.target.value })}
                                />
                              </div>
                            </div>
                            <div>
                              <Label htmlFor={`edit-description-${level.id}`}>Description</Label>
                              <Input
                                id={`edit-description-${level.id}`}
                                value={editLevel.description}
                                onChange={(e) => setEditLevel({ ...editLevel, description: e.target.value })}
                              />
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor={`edit-sort-order-${level.id}`}>Sort Order</Label>
                                <Input
                                  id={`edit-sort-order-${level.id}`}
                                  type="number"
                                  value={editLevel.sort_order}
                                  onChange={(e) => setEditLevel({ ...editLevel, sort_order: parseInt(e.target.value) || 0 })}
                                />
                              </div>
                              <div className="flex items-center space-x-2">
                                <input
                                  id={`edit-active-${level.id}`}
                                  type="checkbox"
                                  checked={editLevel.is_active}
                                  onChange={(e) => setEditLevel({ ...editLevel, is_active: e.target.checked })}
                                  className="rounded"
                                />
                                <Label htmlFor={`edit-active-${level.id}`}>Active</Label>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                onClick={handleUpdateLevel}
                                disabled={updateLevel.isPending}
                                size="sm"
                                className="bg-green-600 hover:bg-green-700"
                              >
                                {updateLevel.isPending ? 'Updating...' : 'Update'}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setEditingId(null);
                                  setEditLevel({ name: '', discipline: '', description: '', sort_order: 0, is_active: true });
                                }}
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <h3 className="font-semibold text-gray-800">{level.name}</h3>
                                <Badge variant={level.is_active ? "default" : "outline"} className={`text-xs ${level.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                                  {level.is_active ? 'Active' : 'Inactive'}
                                </Badge>
                              </div>
                              <div className="flex items-center space-x-2 mt-1">
                                <span className="text-sm text-gray-600">Discipline: {level.discipline}</span>
                                {level.sort_order !== undefined && (
                                  <span className="text-sm text-gray-500">• Sort: {level.sort_order}</span>
                                )}
                              </div>
                              {level.description && (
                                <p className="text-sm text-gray-600 mt-1">{level.description}</p>
                              )}
                              <div className="flex items-center mt-2">
                                <Badge variant="outline" className="text-xs">
                                  Created: {new Date(level.created_at || '').toLocaleDateString()}
                                </Badge>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditLevel(level)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteLevel(level.id)}
                                disabled={deleteLevel.isPending}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default LevelsManagementPage; 