import React, { useState } from 'react';
import { useSession } from '@supabase/auth-helpers-react';
import { useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useAllUsersComprehensive, ComprehensiveUser } from '@/hooks/useAllUsers';
import { useUserRole } from '@/hooks/useUserRoles';
import { useUserFiltering } from '@/hooks/useUserFiltering';
import UserFilters from '@/components/users/UserFilters';
import UserList from '@/components/users/UserList';
import CreateUserDialog from '@/components/users/CreateUserDialog';
import DeleteUserDialog from '@/components/users/DeleteUserDialog';

const UsersManagementPage = () => {
  const session = useSession();
  const navigate = useNavigate();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<ComprehensiveUser | null>(null);

  // Fetch all users with their roles using the existing hook
  const { data: users, isLoading } = useAllUsersComprehensive();
  
  // Use custom hook for filtering
  const filteredUsers = useUserFiltering(users, searchTerm, roleFilter);

  const user = session?.user;
  const { userRole } = useUserRole(!!user);

  // Check if user has super admin access
  if (!session?.user) {
    return <div data-testid="login-prompt">Please log in to access this page.</div>;
  }

  const handleBackToDashboard = () => {
    navigate('/admin/super-admin-dashboard');
  };

  const handleDeleteUser = (user: ComprehensiveUser) => {
    setUserToDelete(user);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteSuccess = () => {
    setIsDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  // Debug logging - only log when users data changes, not on every keystroke
  React.useEffect(() => {
    console.log('🔍 Users Management: Total users:', users?.length, 'Filtered users:', filteredUsers.length);
    console.log('🔍 Users with roles:', users?.map(user => ({
      name: user?.full_name || 'Unknown',
      email: user?.auth_email || 'Unknown',
      role: user?.user_role || 'No Role'
    })));
    
    // Debug each user's role
    users?.forEach(user => {
      if (user && user.full_name && user.auth_email) {
        console.log(`🔍 User ${user.full_name} (${user.auth_email}) has role: ${user.user_role || 'No Role'}`);
      }
    });
  }, [users, filteredUsers.length]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header 
        userRole={userRole} 
        data-testid="header"
      />
      <main className="container mx-auto px-4 py-8" data-testid="users-management-main">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <Button
                variant="ghost"
                onClick={handleBackToDashboard}
                className="text-green-700 hover:bg-green-50 mb-2"
                data-testid="back-to-dashboard-btn"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              <h1 className="text-3xl font-bold text-green-800" data-testid="page-title">Users Management</h1>
              <p className="text-gray-600 mt-1" data-testid="page-description">Manage user accounts and roles across the platform</p>
            </div>
            <CreateUserDialog 
              isOpen={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
            />
          </div>

          <UserFilters
            searchTerm={searchTerm}
            roleFilter={roleFilter}
            onSearchChange={setSearchTerm}
            onRoleFilterChange={setRoleFilter}
          />

          <UserList
            users={users}
            filteredUsers={filteredUsers}
            isLoading={isLoading}
            onDeleteUser={handleDeleteUser}
          />
        </div>
      </main>
      
      <DeleteUserDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        userToDelete={userToDelete}
        onDeleteSuccess={handleDeleteSuccess}
      />
    </div>
  );
};

export default UsersManagementPage; 