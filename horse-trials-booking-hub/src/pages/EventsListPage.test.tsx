import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { createTestQueryClient } from '../test/test-utils';
import { vi } from 'vitest';
import EventsListPage from './EventsListPage';
import { useAccessibleEvents, useIsSuperAdmin } from '@/hooks/useUserRoles';
import { useDeleteEvent, useUpdateEvent } from '@/hooks/useEvents';
import userEvent from '@testing-library/user-event';

// Mock hooks
vi.mock('@/hooks/useUserRoles');
vi.mock('@/hooks/useEvents');

// Mock components
vi.mock('@/components/Header', () => ({
  Header: ({ onBackToEvents, showBackButton }: any) => (
    <header data-testid="header">
      <h1>Events Header</h1>
      {showBackButton && <button data-testid="back-button" onClick={onBackToEvents}>Back</button>}
    </header>
  ),
}));
vi.mock('@/components/OrganizerInfo', () => ({
  OrganizerInfo: ({ organizer }: any) => (
    <span data-testid="organizer-info">{organizer.full_name || organizer.email}</span>
  ),
}));

const mockUseAccessibleEvents = vi.mocked(useAccessibleEvents);
const mockUseIsSuperAdmin = vi.mocked(useIsSuperAdmin);
const mockUseDeleteEvent = vi.mocked(useDeleteEvent);
const mockUseUpdateEvent = vi.mocked(useUpdateEvent);

const renderWithRouter = () => {
  return render(
    <QueryClientProvider client={createTestQueryClient()}>
      <MemoryRouter initialEntries={["/admin/events"]}>
        <Routes>
          <Route path="/admin/events" element={<EventsListPage />} />
          <Route path="/admin/events/create" element={<div data-testid="create-event-page">Create Event</div>} />
          <Route path="/admin/events/:eventId" element={<div data-testid="event-details-page">Event Details</div>} />
          <Route path="/admin/dashboard" element={<div data-testid="dashboard">Dashboard</div>} />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('EventsListPage', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
    
    // Default mocks
    mockUseAccessibleEvents.mockReturnValue({
      data: [],
      isLoading: false,
    });
    
    mockUseIsSuperAdmin.mockReturnValue({
      data: false,
    });
    
    mockUseUpdateEvent.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    });
    
    mockUseDeleteEvent.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    });
  });

  it('shows loading state', () => {
    mockUseAccessibleEvents.mockReturnValue({
      data: undefined,
      isLoading: true,
    });

    renderWithRouter();
    expect(screen.getByText('Loading events...')).toBeInTheDocument();
  });

  it('shows empty state', () => {
    mockUseAccessibleEvents.mockReturnValue({
      data: [],
      isLoading: false,
    });

    renderWithRouter();
    expect(screen.getByText('No events yet')).toBeInTheDocument();
    expect(screen.getByText('Create your first event to get started.')).toBeInTheDocument();
  });

  it('shows grouped events and organizer info for super admin', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
      {
        id: '2',
        name: 'Event 2',
        start_date: '2024-01-03',
        end_date: '2024-01-04',
        is_active: false,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    mockUseIsSuperAdmin.mockReturnValue({
      data: true,
    });

    renderWithRouter();

    expect(screen.getByText('Events')).toBeInTheDocument();
    expect(screen.getByText('Event 1')).toBeInTheDocument();
    expect(screen.getByText('Event 2')).toBeInTheDocument();
    expect(screen.getAllByTestId('organizer-info')).toHaveLength(2);
    expect(screen.getByText('2 events')).toBeInTheDocument();
  });

  it('shows grouped events without organizer info for regular user', () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    mockUseIsSuperAdmin.mockReturnValue({
      data: false,
    });

    renderWithRouter();

    expect(screen.getByText('Event 1')).toBeInTheDocument();
    expect(screen.queryByTestId('organizer-info')).not.toBeInTheDocument();
  });

  it('navigates to event details when event is clicked', async () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    renderWithRouter();

    const eventButton = screen.getByRole('button', { name: /Event 1/ });
    await user.click(eventButton);

    expect(screen.getByTestId('event-details-page')).toBeInTheDocument();
  });

  it('navigates to create event page when create button is clicked', async () => {
    renderWithRouter();

    const createButton = screen.getByRole('button', { name: /Create Event/ });
    await user.click(createButton);

    expect(screen.getByTestId('create-event-page')).toBeInTheDocument();
  });

  it('navigates back to dashboard when back button is clicked', async () => {
    renderWithRouter();

    const backButton = screen.getByTestId('back-button');
    await user.click(backButton);

    expect(screen.getByTestId('dashboard')).toBeInTheDocument();
  });

  it('shows and handles delete dialog', async () => {
    const mockEvents = [
      {
        id: '1',
        name: 'Event 1',
        start_date: '2024-01-01',
        end_date: '2024-01-02',
        is_active: true,
        organizer: {
          id: 'org1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      },
    ];

    mockUseAccessibleEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
    });

    const mockMutate = vi.fn();
    mockUseDeleteEvent.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
    });

    renderWithRouter();

    // Find and click the delete button (trash icon)
    const deleteButton = screen.getByRole('button', { name: '' }); // Trash button has no accessible name
    await user.click(deleteButton);

    // Check that the dialog appears - use the title specifically
    expect(screen.getByRole('heading', { name: 'Delete Event' })).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to delete "Event 1"/)).toBeInTheDocument();

    // Click confirm delete - use the button specifically
    const confirmButton = screen.getByRole('button', { name: /Delete Event/ });
    await user.click(confirmButton);

    // Check that the mutation was called
    expect(mockMutate).toHaveBeenCalledWith('1', expect.any(Object));
  });
}); 