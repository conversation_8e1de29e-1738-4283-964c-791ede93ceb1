import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Plus, Edit, Trash, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { getErrorMessage } from '@/types/errors';

interface DressageTest {
  id: string;
  label: string;
  description?: string;
  level_id?: string;
  organization?: string;
  is_active?: boolean;
  levels?: {
    id: string;
    name: string;
    discipline: string;
  };
}

const DressageTestsManagementPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showActiveOnly, setShowActiveOnly] = useState(true);
  const [newTest, setNewTest] = useState({ label: '', level_id: '', description: '', organization: '', is_active: true });
  const [editTest, setEditTest] = useState({ label: '', level_id: '', description: '', organization: '', is_active: true });

  // Fetch all dressage tests
  const { data: dressageTests, isLoading } = useQuery({
    queryKey: ['dressage-tests', showActiveOnly],
    queryFn: async () => {
      let query = supabase
        .from('dressage_test_library')
        .select(`
          *,
          levels_library!dressage_test_library_level_id_fkey (
            id,
            name,
            discipline
          )
        `)
        .order('label', { ascending: true });

      // Filter by active status if showActiveOnly is true
      if (showActiveOnly) {
        query = query.eq('is_active', true);
      }

      const { data, error } = await query;

      if (error) throw error;
      
      // Transform the data to match our interface
      const transformedData = data?.map(item => ({
        ...item,
        levels: item.levels_library
      })) || [];
      
      return transformedData as DressageTest[];
    },
  });

  // Fetch all levels for the dropdown
  const { data: levels } = useQuery({
    queryKey: ['levels'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('levels_library')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;
      return data;
    },
  });

  // Add new dressage test
  const addDressageTest = useMutation({
    mutationFn: async (test: { label: string; level_id: string; description?: string; organization?: string; is_active?: boolean }) => {
      const { data, error } = await supabase
        .from('dressage_test_library')
        .insert(test)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dressage-tests'] });
      setIsAdding(false);
      setNewTest({ label: '', level_id: '', description: '', organization: '', is_active: true });
      toast({
        title: 'Dressage Test Added',
        description: 'The dressage test has been successfully added.',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });

  // Update dressage test
  const updateDressageTest = useMutation({
    mutationFn: async ({ id, test }: { id: string; test: { label: string; level_id: string; description?: string; organization?: string; is_active?: boolean } }) => {
      const { data, error } = await supabase
        .from('dressage_test_library')
        .update(test)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dressage-tests'] });
      setEditingId(null);
      setEditTest({ label: '', level_id: '', description: '', organization: '', is_active: true });
      toast({
        title: 'Dressage Test Updated',
        description: 'The dressage test has been successfully updated.',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });

  // Delete dressage test
  const deleteDressageTest = useMutation({
    mutationFn: async (id: string) => {
      // First, check if this dressage test is being used in any events
      const { data: eventTests, error: eventError } = await supabase
        .from('event_dressage_tests')
        .select('id, event_id')
        .eq('test_id', id);

      if (eventError) throw eventError;

      // If the test is being used in events, we need to delete those references first
      if (eventTests && eventTests.length > 0) {
        // Delete all event_dressage_tests references first
        const { error: deleteEventTestsError } = await supabase
          .from('event_dressage_tests')
          .delete()
          .eq('test_id', id);

        if (deleteEventTestsError) throw deleteEventTestsError;
      }

      // Now we can safely delete the dressage test
      const { error } = await supabase
        .from('dressage_test_library')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dressage-tests'] });
      queryClient.invalidateQueries({ queryKey: ['event_dressage_tests'] });
      toast({
        title: 'Dressage Test Deleted',
        description: 'The dressage test has been successfully deleted.',
      });
    },
    onError: (error: unknown) => {
      console.error('Delete dressage test error:', error);
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    },
  });

  // Helper function to safely format dates
  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return 'Unknown date';
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date:', dateString, error);
      return 'Invalid date';
    }
  };

  const handleBackToDashboard = () => {
    navigate('/admin/super-admin-dashboard');
  };

  const handleAddDressageTest = () => {
    if (!newTest.label.trim() || !newTest.level_id.trim()) {
      toast({
        title: 'Error',
        description: 'Test label and level are required.',
        variant: 'destructive',
      });
      return;
    }

    addDressageTest.mutate(newTest);
  };

  const handleEditDressageTest = (test: DressageTest) => {
    setEditingId(test.id);
    setEditTest({ 
      label: test.label, 
      level_id: test.level_id || '', 
      description: test.description || '', 
      organization: test.organization || '', 
      is_active: test.is_active || true 
    });
  };

  const handleUpdateDressageTest = () => {
    if (!editTest.label.trim() || !editTest.level_id.trim() || !editingId) return;

    updateDressageTest.mutate({ id: editingId, test: editTest });
  };

  const handleDeleteDressageTest = (id: string) => {
    if (confirm('Are you sure you want to delete this dressage test? This will also remove it from all events where it is currently assigned. This action cannot be undone.')) {
      deleteDressageTest.mutate(id);
    }
  };

  // Group tests by level name (using the levels relationship)
  const testsByLevel = dressageTests?.reduce((acc, test) => {
    const levelName = test.levels?.name || 'Unknown Level';
    if (!acc[levelName]) {
      acc[levelName] = [];
    }
    acc[levelName].push(test);
    return acc;
  }, {} as Record<string, DressageTest[]>) || {};

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header
        onBackToEvents={handleBackToDashboard}
        showBackButton={true}
        userRole={userRole}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <Button
                variant="ghost"
                onClick={handleBackToDashboard}
                className="text-green-700 hover:bg-green-50 mb-2"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              <h1 className="text-3xl font-bold text-green-800">Dressage Tests Management</h1>
              <p className="text-gray-600 mt-1">Manage dressage tests available across all events</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <input
                  id="show-active-only"
                  type="checkbox"
                  checked={showActiveOnly}
                  onChange={(e) => setShowActiveOnly(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="show-active-only" className="text-sm">Show active tests only</Label>
              </div>
              <Button
                onClick={() => setIsAdding(true)}
                className="bg-green-600 hover:bg-green-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Test
              </Button>
            </div>
          </div>

          {/* Add New Dressage Test Form */}
          {isAdding && (
            <Card className="mb-6 border-green-200">
              <CardHeader>
                <CardTitle className="text-green-800">Add New Dressage Test</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="testLabel">Test Label *</Label>
                    <Input
                      id="testLabel"
                      value={newTest.label}
                      onChange={(e) => setNewTest({ ...newTest, label: e.target.value })}
                      placeholder="e.g., USDF First Level Test 1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="testOrganization">Organization</Label>
                    <Input
                      id="testOrganization"
                      value={newTest.organization}
                      onChange={(e) => setNewTest({ ...newTest, organization: e.target.value })}
                      placeholder="e.g., USDF, FEI"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="testLevel">Level *</Label>
                  <Select value={newTest.level_id} onValueChange={(value) => setNewTest({ ...newTest, level_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a level" />
                    </SelectTrigger>
                    <SelectContent>
                      {levels?.map(level => (
                        <SelectItem key={level.id} value={level.id}>
                          {level.name} ({level.discipline})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="testDescription">Description (Optional)</Label>
                  <Input
                    id="testDescription"
                    value={newTest.description}
                    onChange={(e) => setNewTest({ ...newTest, description: e.target.value })}
                    placeholder="Brief description of the test"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    id="testActive"
                    type="checkbox"
                    checked={newTest.is_active}
                    onChange={(e) => setNewTest({ ...newTest, is_active: e.target.checked })}
                    className="rounded"
                  />
                  <Label htmlFor="testActive">Active</Label>
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={handleAddDressageTest}
                    disabled={addDressageTest.isPending}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {addDressageTest.isPending ? 'Adding...' : 'Add Test'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsAdding(false);
                      setNewTest({ label: '', level_id: '', description: '', organization: '', is_active: true });
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Dressage Tests List */}
          <Card>
            <CardHeader>
              <CardTitle className="text-green-800 flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Dressage Tests ({dressageTests?.length || 0})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">Loading dressage tests...</div>
              ) : !dressageTests || dressageTests.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {showActiveOnly ? 'No active dressage tests' : 'No dressage tests yet'}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {showActiveOnly 
                      ? 'All dressage tests are currently inactive. Uncheck "Show active tests only" to see all tests.'
                      : 'Add your first dressage test to get started.'
                    }
                  </p>
                  {!showActiveOnly && (
                    <Button
                      onClick={() => setIsAdding(true)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add First Test
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-6">
                  {Object.entries(testsByLevel).map(([level, tests]) => (
                    <div key={level}>
                      <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <Badge variant="outline" className="mr-2">{level}</Badge>
                        {tests.length} test{tests.length !== 1 ? 's' : ''}
                      </h3>
                      <div className="space-y-3">
                        {tests.map((test) => (
                          <Card key={test.id} className="border-gray-200">
                            <CardContent className="p-4">
                              {editingId === test.id ? (
                                <div className="space-y-4">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                      <Label htmlFor={`edit-label-${test.id}`}>Test Label *</Label>
                                      <Input
                                        id={`edit-label-${test.id}`}
                                        value={editTest.label}
                                        onChange={(e) => setEditTest({ ...editTest, label: e.target.value })}
                                      />
                                    </div>
                                    <div>
                                      <Label htmlFor={`edit-organization-${test.id}`}>Organization</Label>
                                      <Input
                                        id={`edit-organization-${test.id}`}
                                        value={editTest.organization}
                                        onChange={(e) => setEditTest({ ...editTest, organization: e.target.value })}
                                      />
                                    </div>
                                  </div>
                                  <div>
                                    <Label htmlFor={`edit-level-${test.id}`}>Level *</Label>
                                    <Select value={editTest.level_id} onValueChange={(value) => setEditTest({ ...editTest, level_id: value })}>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select a level" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {levels?.map(level => (
                                          <SelectItem key={level.id} value={level.id}>
                                            {level.name} ({level.discipline})
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>
                                  <div>
                                    <Label htmlFor={`edit-description-${test.id}`}>Description</Label>
                                    <Input
                                      id={`edit-description-${test.id}`}
                                      value={editTest.description}
                                      onChange={(e) => setEditTest({ ...editTest, description: e.target.value })}
                                    />
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <input
                                      id={`edit-active-${test.id}`}
                                      type="checkbox"
                                      checked={editTest.is_active}
                                      onChange={(e) => setEditTest({ ...editTest, is_active: e.target.checked })}
                                      className="rounded"
                                    />
                                    <Label htmlFor={`edit-active-${test.id}`}>Active</Label>
                                  </div>
                                  <div className="flex space-x-2">
                                    <Button
                                      onClick={handleUpdateDressageTest}
                                      disabled={updateDressageTest.isPending}
                                      size="sm"
                                      className="bg-green-600 hover:bg-green-700"
                                    >
                                      {updateDressageTest.isPending ? 'Updating...' : 'Update'}
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        setEditingId(null);
                                        setEditTest({ label: '', level_id: '', description: '', organization: '', is_active: true });
                                      }}
                                    >
                                      Cancel
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center space-x-2">
                                      <h4 className="font-semibold text-gray-800">{test.label}</h4>
                                      <Badge variant={test.is_active ? "default" : "outline"} className={`text-xs ${test.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                                        {test.is_active ? 'Active' : 'Inactive'}
                                      </Badge>
                                    </div>
                                    <div className="flex items-center space-x-2 mt-1">
                                      <span className="text-sm text-gray-600">Level: {test.levels?.name || 'Unknown'}</span>
                                      {test.organization && (
                                        <span className="text-sm text-gray-500">• {test.organization}</span>
                                      )}
                                    </div>
                                    {test.description && (
                                      <p className="text-sm text-gray-600 mt-1">{test.description}</p>
                                    )}
                                  </div>
                                  <div className="flex space-x-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleEditDressageTest(test)}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleDeleteDressageTest(test.id)}
                                      disabled={deleteDressageTest.isPending}
                                      className="text-red-600 hover:text-red-700"
                                    >
                                      <Trash className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default DressageTestsManagementPage; 