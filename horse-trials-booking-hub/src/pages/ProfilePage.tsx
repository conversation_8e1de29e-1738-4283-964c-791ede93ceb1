import React from 'react';
import { useNavigate, Navigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { User, Calendar, ArrowLeft, Edit, Mail, Phone } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useBookings } from '@/hooks/useBookings';
import { formatDateForDisplay } from '@/utils/dateUtils';

const ProfilePage = () => {
  const navigate = useNavigate();
  const { user, userRole } = useAuth();
  const { data: bookings, isLoading: bookingsLoading } = useBookings();

  const handleBackToHome = () => {
    navigate('/');
  };

  if (!user) {
    return <Navigate to="/" />;
  }

  return (
    <div className="min-h-screen bg-cream">
      <Header 
        onBackToEvents={handleBackToHome}
        showBackButton={true}
        isAdmin={userRole === 'admin' || userRole === 'organizer'}
      />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Profile Header */}
          <div className="flex items-center space-x-4">
            <Button 
              variant="ghost" 
              onClick={handleBackToHome}
              className="text-primary hover:bg-cream-light"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Events
            </Button>
            <h1 className="text-2xl font-bold text-primary">My Profile</h1>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profile Info Card */}
            <div className="lg:col-span-1">
              <Card className="card-base">
                <CardHeader>
                  <CardTitle className="flex items-center text-primary">
                    <User className="w-5 h-5 mr-2" />
                    Profile Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                      <User className="w-8 h-8 text-cream" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-primary">
                        {user.user_metadata?.full_name || 'User'}
                      </h3>
                      <p className="text-sm text-text-secondary">{user.email}</p>
                      <Badge variant="outline" className="mt-1">
                        {userRole === 'admin' ? 'Administrator' : 
                         userRole === 'organizer' ? 'Organizer' : 'Rider'}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-3 pt-4 border-t border-cream-light">
                    <div className="flex items-center space-x-3">
                      <Mail className="w-4 h-4 text-text-secondary" />
                      <span className="text-sm text-text-secondary">{user.email}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-4 h-4 text-text-secondary" />
                      <span className="text-sm text-text-secondary">
                        Member since {new Date(user.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <Button 
                    variant="outline" 
                    className="w-full mt-4"
                    onClick={() => {/* TODO: Add edit profile functionality */}}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Profile
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Bookings and Activity */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="bookings" className="space-y-4">
                <TabsList>
                  <TabsTrigger value="bookings">My Bookings</TabsTrigger>
                  <TabsTrigger value="activity">Activity</TabsTrigger>
                </TabsList>
                
                <TabsContent value="bookings">
                  <Card className="card-base">
                    <CardHeader>
                      <CardTitle className="flex items-center text-primary">
                        <Calendar className="w-5 h-5 mr-2" />
                        My Bookings
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {bookingsLoading ? (
                        <div className="text-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                          <p className="mt-2 text-text-secondary">Loading bookings...</p>
                        </div>
                      ) : bookings && bookings.length > 0 ? (
                        <div className="space-y-4">
                          {bookings.map((booking: any) => (
                            <div key={booking.booking_id} className="border border-cream-light rounded-lg p-4">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h4 className="font-semibold text-primary">
                                    {booking.activity_description}
                                  </h4>
                                  <p className="text-sm text-text-secondary">
                                    {booking.location_name} • {formatDateForDisplay(booking.time_slot_start_time)}
                                  </p>
                                  <p className="text-sm text-text-secondary">
                                    {booking.participant_name} {booking.horse_name && `• ${booking.horse_name}`}
                                  </p>
                                </div>
                                <div className="text-right">
                                  <Badge 
                                    variant={booking.payment_status === 'paid' ? 'default' : 'secondary'}
                                    className="mb-2"
                                  >
                                    {booking.payment_status === 'paid' ? 'Paid' : 'Pending'}
                                  </Badge>
                                  <p className="text-sm font-medium text-primary">
                                    ${booking.total_price}
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <Calendar className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-primary mb-2">No bookings yet</h3>
                          <p className="text-text-secondary mb-4">
                            Start by browsing events and making your first booking!
                          </p>
                          <Button onClick={handleBackToHome}>
                            Browse Events
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="activity">
                  <Card className="card-base">
                    <CardHeader>
                      <CardTitle className="text-primary">Recent Activity</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-8">
                        <p className="text-text-secondary">Activity tracking coming soon!</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ProfilePage; 