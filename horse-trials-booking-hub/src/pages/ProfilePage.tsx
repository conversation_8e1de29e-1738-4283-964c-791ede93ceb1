import React, { useState } from 'react';
import { useNavigate, Navigate } from 'react-router-dom';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { User, Calendar, ArrowLeft, Edit, Mail, Phone, Shield } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useUserBookings } from '@/hooks/useBookings';
import { formatDateForDisplay } from '@/utils/dateUtils';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { supabase } from '@/integrations/supabase/client';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const ProfilePage = () => {
  const navigate = useNavigate();
  const session = useSession();
  const user = session?.user;
  const { toast } = useToast();
  const { userRole } = useUserRole(!!user);
  const { data: userBookings, isLoading: bookingsLoading } = useUserBookings();
  
  // Debug logging
  console.log('ProfilePage - userBookings:', userBookings);
  console.log('ProfilePage - bookingsLoading:', bookingsLoading);
  console.log('ProfilePage - user:', user);
  
  // Edit profile state
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedEventFilter, setSelectedEventFilter] = useState<string>('all');
  const [editForm, setEditForm] = useState({
    full_name: user?.user_metadata?.full_name || '',
    email: user?.email || '',
    phone: user?.user_metadata?.phone || ''
  });
  const [isUpdating, setIsUpdating] = useState(false);

  // Get unique events for the filter dropdown
  const uniqueEvents = userBookings ? 
    [...new Set(userBookings.map(booking => booking.event_name || booking.location_event_id).filter(Boolean))] : [];

  // Filter bookings based on selected event
  const filteredBookings = userBookings ? 
    selectedEventFilter === 'all' 
      ? userBookings 
      : userBookings.filter(booking => 
          (booking.event_name || booking.location_event_id) === selectedEventFilter
        )
    : [];

  // Debug logging for filtering
  console.log('ProfilePage - uniqueEvents:', uniqueEvents);
  console.log('ProfilePage - selectedEventFilter:', selectedEventFilter);
  console.log('ProfilePage - filteredBookings:', filteredBookings);

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleEditProfile = () => {
    setEditForm({
      full_name: user?.user_metadata?.full_name || '',
      email: user?.email || '',
      phone: user?.user_metadata?.phone || ''
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateProfile = async () => {
    if (!user) return;
    
    setIsUpdating(true);
    try {
      // Update user metadata
      const { error: updateError } = await supabase.auth.updateUser({
        data: { 
          full_name: editForm.full_name,
          phone: editForm.phone
        }
      });

      if (updateError) {
        throw updateError;
      }

      // Update profile in profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          full_name: editForm.full_name,
          email: editForm.email,
          phone: editForm.phone
        });

      if (profileError) {
        throw profileError;
      }

      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
      });

      setIsEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  if (!user) {
    return <Navigate to="/" />;
  }

  return (
    <div className="min-h-screen bg-cream">
      <Header 
        onBackToEvents={handleBackToHome}
        showBackButton={true}
        userRole={userRole}
      />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Profile Header */}
          <div className="flex items-center space-x-4">
            <Button 
              variant="ghost" 
              onClick={handleBackToHome}
              className="text-primary hover:bg-cream-light"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Events
            </Button>
            <h1 className="text-2xl font-bold text-primary">My Profile</h1>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profile Info Card */}
            <div className="lg:col-span-1">
              <Card className="card-base">
                <CardHeader>
                  <CardTitle className="flex items-center text-primary">
                    <User className="w-5 h-5 mr-2" />
                    Profile Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                      <User className="w-8 h-8 text-cream" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-primary">
                        {user.user_metadata?.full_name || 'User'}
                      </h3>
                      <p className="text-sm text-text-secondary">{user.email}</p>
                      <Badge variant="outline" className="mt-1">
                        {userRole === 'super_admin' ? 'Super Administrator' :
                         userRole === 'organizer' ? 'Organizer' : 'User'}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-3 pt-4 border-t border-cream-light">
                    <div className="flex items-center space-x-3">
                      <Mail className="w-4 h-4 text-text-secondary" />
                      <span className="text-sm text-text-secondary">{user.email}</span>
                    </div>
                    {user.user_metadata?.phone && (
                      <div className="flex items-center space-x-3">
                        <Phone className="w-4 h-4 text-text-secondary" />
                        <span className="text-sm text-text-secondary">{user.user_metadata.phone}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-4 h-4 text-text-secondary" />
                      <span className="text-sm text-text-secondary">
                        Member since {new Date(user.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <Button 
                    variant="outline" 
                    className="w-full mt-4"
                    onClick={handleEditProfile}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Profile
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Bookings and Activity */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="bookings" className="space-y-4">
                <TabsList>
                  <TabsTrigger value="bookings">My Bookings</TabsTrigger>
                  <TabsTrigger value="activity">Activity</TabsTrigger>
                </TabsList>
                
                <TabsContent value="bookings">
                  <Card className="card-base">
                    <CardHeader>
                      <CardTitle className="text-primary">My Bookings</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center space-x-4 mb-4">
                        <Label htmlFor="eventFilter" className="text-right">
                          Filter by Event:
                        </Label>
                        <Select 
                          value={selectedEventFilter} 
                          onValueChange={setSelectedEventFilter}
                        >
                          <SelectTrigger className="w-64">
                            <SelectValue placeholder="All Events" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Events</SelectItem>
                            {uniqueEvents.map((eventName) => (
                              <SelectItem key={eventName} value={eventName}>
                                {eventName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <span className="text-sm text-gray-600">
                          {filteredBookings.length} booking{filteredBookings.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                      {bookingsLoading ? (
                        <div className="text-center py-8">Loading bookings...</div>
                      ) : filteredBookings && filteredBookings.length > 0 ? (
                        <div className="space-y-6">
                          {/* Group bookings by event */}
                          {Object.entries(
                            filteredBookings.reduce((groups, booking) => {
                              const eventKey = booking.event_name || booking.location_event_id || 'Unknown Event';
                              if (!groups[eventKey]) {
                                groups[eventKey] = [];
                              }
                              groups[eventKey].push(booking);
                              return groups;
                            }, {} as Record<string, typeof filteredBookings>)
                          ).map(([eventName, eventBookings]) => (
                            <div key={eventName} className="border rounded-lg p-4">
                              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <Calendar className="w-5 h-5 mr-2" />
                                {eventName}
                              </h3>
                              <div className="space-y-4">
                                {eventBookings.map((booking) => (
                                  <div key={booking.booking_id} className="bg-white border rounded-lg p-4 shadow-sm">
                                    {/* Rider and Horse Information */}
                                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-3">
                                      <div>
                                        <h4 className="font-medium text-gray-900">
                                          {booking.participant_name} on {booking.horse_name}
                                        </h4>
                                        {booking.activity_level && (
                                          <p className="text-sm text-gray-600">
                                            Level: {booking.activity_level}
                                          </p>
                                        )}
                                      </div>
                                      <div className="mt-2 sm:mt-0">
                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                          booking.payment_status === 'paid' 
                                            ? 'bg-green-100 text-green-800' 
                                            : 'bg-yellow-100 text-yellow-800'
                                        }`}>
                                          {booking.payment_status === 'paid' ? 'Paid' : 'Pending Payment'}
                                        </span>
                                      </div>
                                    </div>

                                    {/* Location and Date Information */}
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-gray-600 mb-3">
                                      {booking.location_name && (
                                        <div className="flex items-center">
                                          <Calendar className="w-4 h-4 mr-1" />
                                          <span>{booking.location_name}</span>
                                        </div>
                                      )}
                                      
                                      {booking.time_slot_start_time && (
                                        <div className="flex items-center">
                                          <Calendar className="w-4 h-4 mr-1" />
                                          <span>Competition: {formatDateForDisplay(booking.time_slot_start_time)}</span>
                                        </div>
                                      )}
                                    </div>

                                    {/* Activity and Level Information */}
                                    <div className="space-y-2 mb-3">
                                      {booking.activity_description && (
                                        <p className="text-sm text-gray-700">
                                          <span className="font-medium">Activity:</span> {booking.activity_description}
                                        </p>
                                      )}
                                      {booking.dressage_test_label && (
                                        <p className="text-sm text-gray-700">
                                          <span className="font-medium">Dressage Test:</span> {booking.dressage_test_label}
                                        </p>
                                      )}
                                    </div>

                                    {/* Payment and Booking Information */}
                                    <div className="text-xs text-gray-500 space-y-1">
                                      <div>Booking Date: {formatDateForDisplay(booking.booking_created_at)}</div>
                                      {booking.stripe_payment_id && (
                                        <div>Payment ID: {booking.stripe_payment_id}</div>
                                      )}
                                      <div>Booking Code: {booking.booking_code}</div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-text-secondary">
                          No bookings found.
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="activity">
                  <Card className="card-base">
                    <CardHeader>
                      <CardTitle className="text-primary">Activity</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-8 text-text-secondary">
                        Activity tracking coming soon.
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>

      {/* Edit Profile Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Profile</DialogTitle>
            <DialogDescription>
              Update your profile information.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="full_name" className="text-right">
                Full Name
              </Label>
              <Input
                id="full_name"
                value={editForm.full_name}
                onChange={(e) => setEditForm({ ...editForm, full_name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={editForm.email}
                onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone" className="text-right">
                Phone
              </Label>
              <Input
                id="phone"
                type="tel"
                value={editForm.phone}
                onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateProfile} disabled={isUpdating}>
              {isUpdating ? 'Updating...' : 'Update Profile'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProfilePage; 