import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import AdminLoginPage from './AdminLoginPage';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Mock dependencies
vi.mock('@supabase/auth-helpers-react');
vi.mock('@/hooks/useUserRoles');
vi.mock('@/integrations/supabase/client');
vi.mock('@/hooks/use-toast');

const mockUseSession = vi.mocked(useSession);
const mockUseUserRole = vi.mocked(useUserRole);
const mockSupabase = vi.mocked(supabase);
const mockUseToast = vi.mocked(useToast);

// Mock the AdminLogin component
vi.mock('@/components/AdminLogin', () => ({
  AdminLogin: ({ onLogin, onBack }: any) => (
    <div>
      <button onClick={() => onLogin('<EMAIL>', 'password123')}>
        Login
      </button>
      <button onClick={onBack}>
        Back
      </button>
    </div>
  ),
}));

const createQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>
        {component}
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('AdminLoginPage', () => {
  const mockNavigate = vi.fn();
  const mockToast = vi.fn();
  const mockDismiss = vi.fn();
  const mockToasts = [];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mocks
    mockUseSession.mockReturnValue(null);
    mockUseUserRole.mockReturnValue({ userRole: null, isLoading: false });
    mockUseToast.mockReturnValue({ 
      toast: mockToast, 
      dismiss: mockDismiss, 
      toasts: mockToasts 
    });
    mockSupabase.auth.signInWithPassword = vi.fn() as any;
  });

  describe('Initial Render', () => {
    it('renders the admin login page', async () => {
      renderWithProviders(<AdminLoginPage />);
      
      expect(screen.getByText('Login')).toBeInTheDocument();
      expect(screen.getByText('Back')).toBeInTheDocument();
    });

    it('renders with correct styling classes', async () => {
      renderWithProviders(<AdminLoginPage />);
      
      const mainElement = screen.getByRole('main');
      expect(mainElement).toHaveClass('container', 'mx-auto', 'px-4', 'py-8');
      
      const pageElement = mainElement.parentElement;
      expect(pageElement).toHaveClass('min-h-screen', 'bg-gradient-to-br', 'from-green-50', 'to-amber-50');
    });
  });

  describe('Authentication Flow', () => {
    it('handles successful login for super_admin', async () => {
      const user = userEvent.setup();
      (mockSupabase.auth.signInWithPassword as any).mockResolvedValue({ error: null });
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2023-01-01T00:00:00Z'
        } as any,
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        token_type: 'bearer'
      } as any);
      mockUseUserRole.mockReturnValue({ userRole: 'super_admin', isLoading: false });

      renderWithProviders(<AdminLoginPage />);
      
      await user.click(screen.getByText('Login'));
      
      await waitFor(() => {
        expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123'
        });
      });
    });

    it('handles successful login for organizer', async () => {
      const user = userEvent.setup();
      (mockSupabase.auth.signInWithPassword as any).mockResolvedValue({ error: null });
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2023-01-01T00:00:00Z'
        } as any,
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        token_type: 'bearer'
      } as any);
      mockUseUserRole.mockReturnValue({ userRole: 'organizer', isLoading: false });

      renderWithProviders(<AdminLoginPage />);
      
      await user.click(screen.getByText('Login'));
      
      await waitFor(() => {
        expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123'
        });
      });
    });

    it('shows access denied for non-admin users', async () => {
      const user = userEvent.setup();
      (mockSupabase.auth.signInWithPassword as any).mockResolvedValue({ error: null });
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2023-01-01T00:00:00Z'
        } as any,
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        token_type: 'bearer'
      } as any);
      mockUseUserRole.mockReturnValue({ userRole: 'rider', isLoading: false });

      renderWithProviders(<AdminLoginPage />);
      
      await user.click(screen.getByText('Login'));
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: "Access Denied",
          description: "You don't have permission to access the admin area.",
          variant: "destructive"
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('handles authentication error from Supabase', async () => {
      const user = userEvent.setup();
      const authError = { message: 'Invalid credentials' };
      (mockSupabase.auth.signInWithPassword as any).mockResolvedValue({ error: authError });

      renderWithProviders(<AdminLoginPage />);
      
      await user.click(screen.getByText('Login'));
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: "Error",
          description: "Invalid credentials",
          variant: "destructive",
        });
      });
    });

    it('handles unexpected errors during login', async () => {
      const user = userEvent.setup();
      (mockSupabase.auth.signInWithPassword as any).mockRejectedValue(new Error('Network error'));

      renderWithProviders(<AdminLoginPage />);
      
      await user.click(screen.getByText('Login'));
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: "Error",
          description: expect.any(String),
          variant: "destructive",
        });
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading state during authentication', async () => {
      const user = userEvent.setup();
      (mockSupabase.auth.signInWithPassword as any).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ error: null }), 100))
      );

      renderWithProviders(<AdminLoginPage />);
      
      await user.click(screen.getByText('Login'));
      
      // The loading state is handled internally by the AdminLogin component
      // We can verify the login function was called
      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalled();
    });

    it('waits for user role to load before navigation', async () => {
      const user = userEvent.setup();
      (mockSupabase.auth.signInWithPassword as any).mockResolvedValue({ error: null });
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2023-01-01T00:00:00Z'
        } as any,
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        token_type: 'bearer'
      } as any);
      
      // Initially loading, then resolves
      mockUseUserRole.mockReturnValue({ userRole: null, isLoading: true });

      renderWithProviders(<AdminLoginPage />);
      
      await user.click(screen.getByText('Login'));
      
      // Should not show access denied while loading
      expect(mockToast).not.toHaveBeenCalled();
    });
  });

  describe('Navigation', () => {
    it('handles back button navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdminLoginPage />);
      
      await user.click(screen.getByText('Back'));
      
      // The navigation is handled by the AdminLogin component
      // We just verify the button click works
      expect(screen.getByText('Back')).toBeInTheDocument();
    });
  });

  describe('Role-based Access Control', () => {
    it('allows super_admin access', async () => {
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2023-01-01T00:00:00Z'
        } as any,
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        token_type: 'bearer'
      } as any);
      mockUseUserRole.mockReturnValue({ userRole: 'super_admin', isLoading: false });

      renderWithProviders(<AdminLoginPage />);
      
      // The navigation logic is in useEffect, so we just verify the component renders
      expect(screen.getByText('Login')).toBeInTheDocument();
    });

    it('allows organizer access', async () => {
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2023-01-01T00:00:00Z'
        } as any,
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        token_type: 'bearer'
      } as any);
      mockUseUserRole.mockReturnValue({ userRole: 'organizer', isLoading: false });

      renderWithProviders(<AdminLoginPage />);
      
      // The navigation logic is in useEffect, so we just verify the component renders
      expect(screen.getByText('Login')).toBeInTheDocument();
    });

    it('denies access to riders', async () => {
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2023-01-01T00:00:00Z'
        } as any,
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        token_type: 'bearer'
      } as any);
      mockUseUserRole.mockReturnValue({ userRole: 'rider', isLoading: false });

      renderWithProviders(<AdminLoginPage />);
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: "Access Denied",
          description: "You don't have permission to access the admin area.",
          variant: "destructive"
        });
      });
    });
  });

  describe('Edge Cases', () => {
    it('handles null session gracefully', async () => {
      mockUseSession.mockReturnValue(null);
      mockUseUserRole.mockReturnValue({ userRole: null, isLoading: false });

      renderWithProviders(<AdminLoginPage />);
      
      // Should render without errors
      expect(screen.getByText('Login')).toBeInTheDocument();
    });

    it('handles undefined user in session', async () => {
      mockUseSession.mockReturnValue({ 
        user: undefined,
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        token_type: 'bearer'
      } as any);
      mockUseUserRole.mockReturnValue({ userRole: null, isLoading: false });

      renderWithProviders(<AdminLoginPage />);
      
      // Should render without errors
      expect(screen.getByText('Login')).toBeInTheDocument();
    });

    it('logs user role for debugging', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      mockUseSession.mockReturnValue({ 
        user: { 
          id: 'user-1',
          app_metadata: {},
          user_metadata: {},
          aud: 'authenticated',
          created_at: '2023-01-01T00:00:00Z'
        } as any,
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        token_type: 'bearer'
      } as any);
      mockUseUserRole.mockReturnValue({ userRole: 'super_admin', isLoading: false });

      renderWithProviders(<AdminLoginPage />);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('User authenticated with role:', 'super_admin');
      });
      
      consoleSpy.mockRestore();
    });
  });
}); 