import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, ArrowLeft, Mail, Calendar, Clock, AlertCircle } from 'lucide-react';
import { Header } from '@/components/Header';
import { supabase, getEdgeFunctionUrl } from '@/integrations/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import { getUserSessionId } from '@/hooks/useSlotReservations';

const BookingSuccessPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bookingDetails, setBookingDetails] = useState<any>(null);
  const queryClient = useQueryClient();

  // Get session ID from URL params (Stripe sends this)
  const sessionId = searchParams.get('session_id');
  // Get event ID from URL params (we added this)
  const eventId = searchParams.get('event_id');
  // Get date from URL params (we added this)
  const date = searchParams.get('date');

  useEffect(() => {
    const confirmBooking = async () => {
      // If no session ID, just show success (webhook should have handled it)
      if (!sessionId) {
        console.log('No session ID found - assuming webhook handled the booking');
        // Clear cart data even if no session ID (webhook handled it)
        const userSessionId = getUserSessionId();
        queryClient.invalidateQueries({ queryKey: ['slot_reservations', userSessionId] });
        queryClient.invalidateQueries({ queryKey: ['all_slot_reservations'] });
        queryClient.invalidateQueries({ queryKey: ['time_slots'] });
        queryClient.invalidateQueries({ queryKey: ['bookings'] });
        console.log('Cart data invalidated - reservations should be cleared');
        setIsLoading(false);
        return;
      }

      try {
        console.log('Confirming booking for session:', sessionId);
        
        // Use the same function name as configured in the environment
        const functionName = import.meta.env.VITE_STRIPE_FUNCTION_NAME || 'create-stripe-checkout-public';
        const functionUrl = getEdgeFunctionUrl(functionName);
        
        console.log('Calling function:', functionName, 'at URL:', functionUrl);
        
        // Call our Edge Function to verify the payment and create the booking
        const response = await fetch(functionUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'confirm_booking',
            sessionId: sessionId
          })
        });

        const data = await response.json();
        console.log('Function response:', data);

        if (!response.ok) {
          console.error('Error confirming booking:', data);
          // Don't show error if webhook might have handled it
          console.log('Frontend confirmation failed, but webhook may have succeeded');
        }

        if (data?.success) {
          setBookingDetails(data.booking);
          console.log('Booking confirmed successfully:', data.booking);
          
          // Force refresh cart data to clear any remaining reservations
          const userSessionId = getUserSessionId();
          queryClient.invalidateQueries({ queryKey: ['slot_reservations', userSessionId] });
          queryClient.invalidateQueries({ queryKey: ['all_slot_reservations'] });
          queryClient.invalidateQueries({ queryKey: ['time_slots'] });
          queryClient.invalidateQueries({ queryKey: ['bookings'] });
          
          console.log('Cart data invalidated - reservations should be cleared');
        } else if (data?.error) {
          console.log('Frontend confirmation error:', data.error);
          // Don't show error to user since webhook might have worked
        }
      } catch (err) {
        console.error('Error in confirmBooking:', err);
        // Don't show error to user since webhook might have worked
      } finally {
        setIsLoading(false);
      }
    };

    confirmBooking();
  }, [sessionId, queryClient]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-cream">
        <Header />
        <div className="max-w-4xl mx-auto p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-text-primary">Confirming your booking...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-cream">
      <Header />
      <div className="max-w-4xl mx-auto p-8 space-y-6">
        <Card className="card-base">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <CheckCircle className="w-16 h-16 text-primary" />
            </div>
            <CardTitle className="text-2xl text-primary font-playfair">Payment Successful!</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center">
              <p className="text-lg">Thank you for your booking!</p>
              <p className="text-text-secondary mt-2">
                Your payment has been processed successfully and your booking is confirmed.
              </p>
              {bookingDetails && (
                <div className="mt-4 p-4 bg-cream-light rounded-lg">
                  <h4 className="font-medium text-primary mb-2">Booking Details</h4>
                  <p className="text-sm text-text-secondary">
                    <span className="font-medium">Booking ID:</span> {bookingDetails.id}
                  </p>
                  <p className="text-sm text-text-secondary">
                    <span className="font-medium">Amount:</span> ${bookingDetails.total_price}
                  </p>
                </div>
              )}
              {sessionId && (
                <p className="mt-4 text-sm text-text-secondary">
                  <span className="font-medium">Payment Reference:</span> {sessionId}
                </p>
              )}
            </div>

            <div className="bg-cream-light p-4 rounded-lg">
              <h3 className="font-medium text-primary mb-3">What happens next?</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <Mail className="w-5 h-5 text-gold mt-0.5" />
                  <div>
                    <p className="font-medium text-primary">Confirmation Email</p>
                    <p className="text-sm text-text-secondary">
                      You'll receive a confirmation email with your booking details and a unique link to manage your booking.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Calendar className="w-5 h-5 text-gold mt-0.5" />
                  <div>
                    <p className="font-medium text-primary">Event Details</p>
                    <p className="text-sm text-text-secondary">
                      Check your email for specific event information, arrival times, and any special instructions.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Clock className="w-5 h-5 text-gold mt-0.5" />
                  <div>
                    <p className="font-medium text-primary">Booking Management</p>
                    <p className="text-sm text-text-secondary">
                      You can view and manage your booking through the link in your confirmation email.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
              <Button
                onClick={() => navigate(eventId ? `/?event=${eventId}${date ? `&date=${date}` : ''}` : '/')}
                className="btn-primary"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Return to Event
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/public-bookings')}
                className="border-primary text-primary hover:bg-cream-light"
              >
                View All Bookings
              </Button>
            </div>

            <div className="text-center pt-4 border-t border-cream-light">
              <p className="text-xs text-text-secondary">
                If you have any questions about your booking, please contact us at{' '}
                <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                  <EMAIL>
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default BookingSuccessPage; 