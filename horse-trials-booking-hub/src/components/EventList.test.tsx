import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EventList } from './EventList';
import { useEvents, Event } from '@/hooks/useEvents';
import { useSession } from '@supabase/auth-helpers-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Mock the hooks and dependencies
vi.mock('@/hooks/useEvents');
vi.mock('@supabase/auth-helpers-react');
vi.mock('@/integrations/supabase/client');
vi.mock('@/hooks/use-toast');

const mockUseEvents = vi.mocked(useEvents);
const mockUseSession = vi.mocked(useSession);
const mockUseToast = vi.mocked(useToast);
const mockSupabase = vi.mocked(supabase);

describe('EventList Component', () => {
  const mockOnEventSelect = vi.fn();
  const mockToast = vi.fn();

  const mockEvents: Event[] = [
    {
      id: 'event1',
      name: 'Spring Dressage Show',
      start_date: '2024-05-15',
      end_date: '2024-05-15',
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'event2',
      name: 'Summer Jumping Competition',
      start_date: '2024-06-20',
      end_date: '2024-06-20',
      is_active: false,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseToast.mockReturnValue({ toast: mockToast });
    mockUseSession.mockReturnValue(null);
  });

  const renderEventList = () => {
    return render(<EventList onEventSelect={mockOnEventSelect} />);
  };

  describe('Loading State', () => {
    beforeEach(() => {
      mockUseEvents.mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
      });
    });

    it('displays loading skeleton cards', () => {
      renderEventList();

      // Should show 3 skeleton cards
      const skeletonCards = screen.getAllByText('', { selector: '.animate-pulse' });
      expect(skeletonCards.length).toBeGreaterThan(0);
    });

    it('shows skeleton with correct structure', () => {
      renderEventList();

      // Check for skeleton elements - there are more than 9 due to additional styling elements
      const skeletonElements = screen.getAllByText('', { selector: '.bg-cream-light' });
      expect(skeletonElements.length).toBeGreaterThan(0);
    });
  });

  describe('Error State', () => {
    beforeEach(() => {
      mockUseEvents.mockReturnValue({
        data: [],
        isLoading: false,
        error: new Error('Failed to load events'),
      });
    });

    it('displays error message', () => {
      renderEventList();

      expect(screen.getByText('Error loading events: Failed to load events')).toBeInTheDocument();
      expect(screen.getByText('Please try refreshing the page or contact support.')).toBeInTheDocument();
    });

    it('shows error in red color', () => {
      renderEventList();

      const errorMessage = screen.getByText('Error loading events: Failed to load events');
      expect(errorMessage).toHaveClass('text-red-600');
    });
  });

  describe('Empty State', () => {
    beforeEach(() => {
      mockUseEvents.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });
    });

    it('displays no events message when no events available', () => {
      renderEventList();

      expect(screen.getByText('No Events Available')).toBeInTheDocument();
      expect(screen.getByText(/Check back later for upcoming events/)).toBeInTheDocument();
    });

    it('shows sign up button for non-authenticated users', () => {
      renderEventList();

      expect(screen.getByText('Sign Up for Updates')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Sign Up for Updates/i })).toBeInTheDocument();
    });

    it('does not show sign up button for authenticated users', () => {
      mockUseSession.mockReturnValue({
        user: { id: 'user1', email: '<EMAIL>' },
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        expires_at: Date.now() + 3600000,
        token_type: 'bearer',
      });

      renderEventList();

      expect(screen.queryByText('Sign Up for Updates')).not.toBeInTheDocument();
    });
  });

  describe('Sign Up Flow', () => {
    beforeEach(() => {
      mockUseEvents.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });
    });

    it('shows sign up form when sign up button is clicked', async () => {
      const user = userEvent.setup();
      renderEventList();

      const signUpButton = screen.getByRole('button', { name: /Sign Up for Updates/i });
      await user.click(signUpButton);

      expect(screen.getByText('Create Account')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Your full name')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Password')).toBeInTheDocument();
    });

    it('allows filling out the sign up form', async () => {
      const user = userEvent.setup();
      renderEventList();

      const signUpButton = screen.getByRole('button', { name: /Sign Up for Updates/i });
      await user.click(signUpButton);

      const fullNameInput = screen.getByPlaceholderText('Your full name');
      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      const passwordInput = screen.getByPlaceholderText('Password');

      await user.type(fullNameInput, 'John Doe');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      expect(fullNameInput).toHaveValue('John Doe');
      expect(emailInput).toHaveValue('<EMAIL>');
      expect(passwordInput).toHaveValue('password123');
    });

    it('submits sign up form successfully', async () => {
      const user = userEvent.setup();
      mockSupabase.auth.signUp.mockResolvedValue({ data: { user: null }, error: null });

      renderEventList();

      const signUpButton = screen.getByRole('button', { name: /Sign Up for Updates/i });
      await user.click(signUpButton);

      const fullNameInput = screen.getByPlaceholderText('Your full name');
      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      const passwordInput = screen.getByPlaceholderText('Password');
      const submitButton = screen.getByRole('button', { name: /Sign Up/i });

      await user.type(fullNameInput, 'John Doe');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          data: {
            full_name: 'John Doe',
          },
        },
      });

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Success',
          description: 'Account created successfully! Please check your email to verify your account.',
        });
      });
    });

    it('handles sign up error', async () => {
      const user = userEvent.setup();
      const signUpError = new Error('Email already exists');
      mockSupabase.auth.signUp.mockResolvedValue({ data: { user: null }, error: signUpError });

      renderEventList();

      const signUpButton = screen.getByRole('button', { name: /Sign Up for Updates/i });
      await user.click(signUpButton);

      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      const passwordInput = screen.getByPlaceholderText('Password');
      const submitButton = screen.getByRole('button', { name: /Sign Up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Email already exists',
          variant: 'destructive',
        });
      });
    });

    it('shows loading state during sign up', async () => {
      const user = userEvent.setup();
      // Mock a delayed response
      mockSupabase.auth.signUp.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ data: { user: null }, error: null }), 100))
      );

      renderEventList();

      const signUpButton = screen.getByRole('button', { name: /Sign Up for Updates/i });
      await user.click(signUpButton);

      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      const passwordInput = screen.getByPlaceholderText('Password');
      const submitButton = screen.getByRole('button', { name: /Sign Up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      expect(screen.getByText('Creating...')).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
    });

    it('cancels sign up form', async () => {
      const user = userEvent.setup();
      renderEventList();

      const signUpButton = screen.getByRole('button', { name: /Sign Up for Updates/i });
      await user.click(signUpButton);

      const cancelButton = screen.getByRole('button', { name: /Cancel/i });
      await user.click(cancelButton);

      expect(screen.queryByText('Create Account')).not.toBeInTheDocument();
      expect(screen.getByText('Sign Up for Updates')).toBeInTheDocument();
    });
  });

  describe('Events Display', () => {
    beforeEach(() => {
      mockUseEvents.mockReturnValue({
        data: mockEvents,
        isLoading: false,
        error: null,
      });
    });

    it('displays all events in a grid', () => {
      renderEventList();

      expect(screen.getByText('Spring Dressage Show')).toBeInTheDocument();
      expect(screen.getByText('Summer Jumping Competition')).toBeInTheDocument();
    });

    it('shows formatted dates for events', () => {
      renderEventList();

      // Check for formatted dates (formatDateForDisplay returns MM/DD/YYYY format)
      expect(screen.getByText('5/15/2024')).toBeInTheDocument();
      expect(screen.getByText('6/20/2024')).toBeInTheDocument();
    });

    it('shows active event description', () => {
      renderEventList();

      expect(screen.getByText(/Perfect for riders looking to gain experience/)).toBeInTheDocument();
    });

    it('shows inactive event description', () => {
      renderEventList();

      expect(screen.getByText(/This event is currently inactive/)).toBeInTheDocument();
    });

    it('shows view details button for each event', () => {
      renderEventList();

      const viewDetailsButtons = screen.getAllByRole('button', { name: /View Details/i });
      expect(viewDetailsButtons).toHaveLength(2);
    });

    it('renders events with correct accessibility attributes', () => {
      renderEventList();

      const eventCards = screen.getAllByRole('button', { name: /View Details/i });
      eventCards.forEach((button, index) => {
        const event = mockEvents[index];
        expect(button).toHaveAttribute('aria-label', `View Details for ${event.name}`);
      });
    });
  });

  describe('Event Selection', () => {
    beforeEach(() => {
      mockUseEvents.mockReturnValue({
        data: mockEvents,
        isLoading: false,
        error: null,
      });
    });

    it('calls onEventSelect when event card is clicked', async () => {
      const user = userEvent.setup();
      renderEventList();

      const eventCard = screen.getByText('Spring Dressage Show').closest('div');
      expect(eventCard).toBeTruthy();
      
      await user.click(eventCard!);

      expect(mockOnEventSelect).toHaveBeenCalledWith(mockEvents[0]);
    });

    it('calls onEventSelect when view details button is clicked', async () => {
      const user = userEvent.setup();
      renderEventList();

      const viewDetailsButton = screen.getByRole('button', { name: /View Details for Spring Dressage Show/i });
      await user.click(viewDetailsButton);

      expect(mockOnEventSelect).toHaveBeenCalledWith(mockEvents[0]);
    });

    it('handles keyboard navigation for event selection', async () => {
      const user = userEvent.setup();
      renderEventList();

      const eventCard = screen.getByText('Spring Dressage Show').closest('div');
      expect(eventCard).toBeTruthy();
      
      eventCard!.focus();
      await user.keyboard('{Enter}');

      expect(mockOnEventSelect).toHaveBeenCalledWith(mockEvents[0]);
    });

    it('handles space key for event selection', async () => {
      const user = userEvent.setup();
      renderEventList();

      const eventCard = screen.getByText('Spring Dressage Show').closest('div');
      expect(eventCard).toBeTruthy();
      
      eventCard!.focus();
      await user.keyboard(' ');

      expect(mockOnEventSelect).toHaveBeenCalledWith(mockEvents[0]);
    });
  });

  describe('Edge Cases', () => {
    it('handles network error during sign up', async () => {
      const user = userEvent.setup();
      mockSupabase.auth.signUp.mockRejectedValue(new Error('Network error'));

      mockUseEvents.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      renderEventList();

      const signUpButton = screen.getByRole('button', { name: /Sign Up for Updates/i });
      await user.click(signUpButton);

      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      const passwordInput = screen.getByPlaceholderText('Password');
      const submitButton = screen.getByRole('button', { name: /Sign Up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Network error',
          variant: 'destructive',
        });
      });
    });

    it('handles empty events array', () => {
      mockUseEvents.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      renderEventList();

      expect(screen.getByText('No Events Available')).toBeInTheDocument();
    });

    it('handles null events data', () => {
      mockUseEvents.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
      });

      renderEventList();

      expect(screen.getByText('No Events Available')).toBeInTheDocument();
    });
  });
}); 