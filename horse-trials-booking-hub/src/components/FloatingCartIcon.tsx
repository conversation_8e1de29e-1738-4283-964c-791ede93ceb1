import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart } from 'lucide-react';
import { useSlotReservations, getUserSessionId } from '@/hooks/useSlotReservations';

interface FloatingCartIconProps {
  onClick: () => void;
}

export const FloatingCartIcon: React.FC<FloatingCartIconProps> = ({ onClick }) => {
  const userSessionId = getUserSessionId();
  const { data: reservations = [] } = useSlotReservations(userSessionId);

  const handleClick = () => {
    onClick();
  };

  return (
    <div className="fixed bottom-4 right-4 lg:right-20 z-50">
      <Button 
        size="lg" 
        onClick={handleClick}
        className="relative bg-primary hover:bg-primary-light text-cream shadow-lg rounded-full w-14 h-14 p-0"
        aria-label="Open shopping cart"
      >
        <ShoppingCart className="w-6 h-6" />
        {reservations.length > 0 ? (
          <Badge 
            variant="secondary" 
            className="absolute -top-2 -right-2 bg-gold text-primary min-w-[1.5rem] h-6 rounded-full flex items-center justify-center text-xs font-bold"
          >
            {reservations.length}
          </Badge>
        ) : (
          <Badge 
            variant="secondary" 
            className="absolute -top-2 -right-2 bg-cream-light text-text-secondary min-w-[1.5rem] h-6 rounded-full flex items-center justify-center text-xs font-bold"
          >
            0
          </Badge>
        )}
      </Button>
    </div>
  );
};
