// src/components/FloatingCartIcon.test.tsx
// @vitest-environment jsdom 
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { FloatingCartIcon } from './FloatingCartIcon';

// Mock the useSlotReservations hook
// We'll control the number of reservations for our tests
vi.mock('@/hooks/useSlotReservations', () => ({
  useSlotReservations: vi.fn(),
  getUserSessionId: () => 'test-session-id', // Mock session ID
}));

describe('FloatingCartIcon Component', () => {
  // Clean up after each test
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the shopping cart icon and shows 0 when cart is empty', async () => { 
    // Mock useSlotReservations to return an empty array for this test
    const { useSlotReservations } = await import('@/hooks/useSlotReservations');
    (useSlotReservations as any).mockReturnValue({ data: [] });

    render(<FloatingCartIcon onClick={vi.fn()} />);

    // Check for the badge text "0" when cart is empty.
    expect(screen.getByText('0')).toBeInTheDocument();
    // Also check if the button itself is rendered using its accessible name
    expect(screen.getByRole('button', { name: /open shopping cart/i })).toBeInTheDocument();
    
    // Verify the ShoppingCart icon is present (assuming it has a specific class or test-id)
    // If your component uses Lucide icons, they typically have specific classes
    expect(document.querySelector('.lucide-shopping-cart')).not.toBeNull();
  });

  it('displays the correct number of items in the badge', async () => {
    const mockReservations = [{ id: '1' }, { id: '2' }];
    // Mock useSlotReservations to return reservations
    const { useSlotReservations } = await import('@/hooks/useSlotReservations');
    (useSlotReservations as any).mockReturnValue({ data: mockReservations });

    render(<FloatingCartIcon onClick={vi.fn()} />);

    // Check if the badge displays the correct count
    expect(screen.getByText(mockReservations.length.toString())).toBeInTheDocument();
  });

  it('displays a large number of items correctly', async () => {
    // Test with a larger number of items to ensure the badge handles it properly
    const mockReservations = Array(15).fill(null).map((_, i) => ({ id: i.toString() }));
    
    const { useSlotReservations } = await import('@/hooks/useSlotReservations');
    (useSlotReservations as any).mockReturnValue({ data: mockReservations });

    render(<FloatingCartIcon onClick={vi.fn()} />);

    // Check if the badge displays the correct count
    expect(screen.getByText('15')).toBeInTheDocument();
  });

  it('calls the onClick prop when clicked', async () => { 
    const handleClick = vi.fn(); // Create a mock function

    // Mock useSlotReservations to return an empty array
    const { useSlotReservations } = await import('@/hooks/useSlotReservations');
    (useSlotReservations as any).mockReturnValue({ data: [] });

    render(<FloatingCartIcon onClick={handleClick} />);

    // Find the button by its role and accessible name (aria-label)
    const buttonElement = screen.getByRole('button', { name: /open shopping cart/i });
    fireEvent.click(buttonElement);

    // Assert that the mock function was called
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('has the correct styling for positioning and appearance', async () => {
    const { useSlotReservations } = await import('@/hooks/useSlotReservations');
    (useSlotReservations as any).mockReturnValue({ data: [] });

    render(<FloatingCartIcon onClick={vi.fn()} />);

    // Get the button by its role
    const button = screen.getByRole('button', { name: /open shopping cart/i });
    
    // Get the parent container div which should have the fixed positioning
    const container = button.closest('div');
    expect(container).not.toBeNull();
    expect(container?.className).toContain('fixed');
    expect(container?.className).toContain('bottom-4');
    expect(container?.className).toContain('right-4');
    
    // Check for the correct styling classes on the button itself
    expect(button.className).toContain('rounded-full');
  });

  it('handles undefined data from useSlotReservations gracefully', async () => {
    // Test the fallback to empty array when data is undefined
    const { useSlotReservations } = await import('@/hooks/useSlotReservations');
    (useSlotReservations as any).mockReturnValue({ data: undefined });

    render(<FloatingCartIcon onClick={vi.fn()} />);

    // Should default to showing "0"
    expect(screen.getByText('0')).toBeInTheDocument();
  });
});
