import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clock, User, Target, Calendar, AlertCircle, PawPrint } from 'lucide-react';
import '@fortawesome/fontawesome-free/css/all.min.css';
import { useTimeSlots } from '@/hooks/useTimeSlots';
import { useSlotReservations, getUserSessionId, useReserveSlot } from '@/hooks/useSlotReservations';
import { useAllSlotReservations } from '@/hooks/useAllSlotReservations';
import { useBookings } from '@/hooks/useBookings';
import { useAppSettings } from '@/hooks/useAppSettings';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useEventDressageTests } from '@/hooks/useDressageTests';
import { useQueryClient } from '@tanstack/react-query';

export interface LocationScheduleProps {
  date: string;
  locations: Array<{
    id: string;
    name: string;
    activity_type: string;
    fixed_level?: string;
    activities?: Array<{
      id: string;
      activity_type: string;
      start_time: string;
      end_time: string;
      level?: string;
    }>;
  }>;
  activityMode?: boolean;
  eventId?: string;
  timeFilter?: string;
  isTimeSlotAfterTime?: (timeSlotStartTime: string, filterTime: string) => boolean;
  showOnlyAvailable?: boolean;
}

export const LocationSchedule: React.FC<LocationScheduleProps> = ({
  date,
  locations,
  activityMode = false,
  eventId,
  timeFilter = 'all',
  isTimeSlotAfterTime,
  showOnlyAvailable = false
}) => {
  const [selectedDressageTests, setSelectedDressageTests] = useState<{ [key: string]: string }>({});
  const { toast } = useToast();
  const userSessionId = getUserSessionId();
  const queryClient = useQueryClient();
  const { data: bookings, refetch: refetchBookings } = useBookings(eventId);
  const { data: cart = [] } = useSlotReservations(userSessionId);
  const { data: allReservations = [] } = useAllSlotReservations();
  const reserveSlot = useReserveSlot();

  // Refetch data when component mounts or when returning from booking flow
  useEffect(() => {
    // Refetch bookings and time slots when component mounts
    refetchBookings();

    // Invalidate time slots for all locations
    if (locations && locations.length > 0) {
      locations.forEach(location => {
        queryClient.invalidateQueries({ queryKey: ['time_slots', location.id] });
      });
    }
  }, [locations, refetchBookings, queryClient]);

  // Format date for display
  const formatDisplayDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const generateTimeSlotsFromActivity = (location, activity) => {
    const { data: timeSlots } = useTimeSlots(location.id);

    if (!timeSlots || timeSlots.length === 0) {
      return [];
    }

    // Extract the date part from the activity's start time
    const activityDate = activity.start_time.split('T')[0];

    // Filter time slots that fall within this activity's time range
    // AND match the activity's date
    let activitySlots = timeSlots.filter(slot => {
      // Check if the slot's date matches the activity's date
      const slotDate = slot.start_time.split('T')[0];
      if (slotDate !== activityDate) {
        return false;
      }

      // Now check if the slot's time falls within the activity's time range
      const slotStart = new Date(slot.start_time);
      const activityStart = new Date(activity.start_time);
      const activityEnd = new Date(activity.end_time);

      return slotStart >= activityStart && slotStart < activityEnd;
    });

    // Apply time filter AFTER getting the actual time slots
    if (timeFilter !== 'all') {
      activitySlots = activitySlots.filter(slot => {
        // Extract the time from the slot's start_time (not activity start_time)
        const slotTime = slot.start_time.split('T')[1]?.substring(0, 5);
        if (!slotTime) return true;

        const slotHour = parseInt(slotTime.split(':')[0]);
        const filterHour = parseInt(timeFilter.split(':')[0]);

        return slotHour >= filterHour;
      });
    }

    return activitySlots.map(slot => {
      const startTime = new Date(slot.start_time);
      const endTime = new Date(slot.end_time);
      const timeString = startTime.toTimeString().slice(0, 5); // Show only start time

      // Check if this slot is in current user's cart
      const isInCart = cart.some(item =>
        item.time_slot_id === slot.id
      );

      // Check if this slot is reserved by ANY user (including current user)
      const reservation = allReservations.find(res => res.time_slot_id === slot.id);
      const isReservedByOther = reservation && reservation.user_session_id !== userSessionId;
      const isReservedByCurrentUser = reservation && reservation.user_session_id === userSessionId;

      // Find booking details for this slot
      const booking = bookings?.find(b => b.time_slot_id === slot.id);

      // Determine slot level based on activity type
      const slotLevel = activity.activity_type === 'show_jumping'
        ? activity.level || location.fixed_level
        : slot.level;

      // Create the slot object with explicit activity_type
      const slotObject = {
        id: slot.id,
        time_slot_id: slot.id,
        location_id: location.id,
        location_name: location.name,
        time: timeString,
        activity_type: activity.activity_type, // Explicitly set from activity
        activity_id: activity.id,
        fixed_level: activity.level || location.fixed_level,
        level: slotLevel,
        isBooked: slot.is_booked,
        isInCart: isInCart || isReservedByCurrentUser,
        isReservedByOther,
        booking: booking,
        expires_at: reservation?.expires_at || null // ✅ add this
      };

      return slotObject;
    });
  };

  useEffect(() => {
    // One-time logging to help diagnose time slot issues
    if (locations && locations.length > 0) {
      locations.forEach(location => {
        if (location.activities && location.activities.length > 0) {
          location.activities.forEach((activity, index) => {
          });
        }
      });
    }
  }, [locations]);

  return (
    <div className="space-y-6 w-full max-w-[1000px] mx-auto px-2">      {locations.map((location) => {
      let timeSlots = [];

      if (activityMode && location.activities && location.activities.length > 0) {
        // Activity-based mode - ONLY use database time slots
        location.activities.forEach(activity => {
          const activitySlots = generateTimeSlotsFromActivity(location, activity);
          timeSlots.push(...activitySlots);
        });
      } else {
        // For non-activity mode, just get all time slots for the location from database
        const { data: allTimeSlots } = useTimeSlots(location.id);
        if (allTimeSlots && allTimeSlots.length > 0) {
          timeSlots = allTimeSlots.map(slot => {
            const startTime = new Date(slot.start_time);
            const endTime = new Date(slot.end_time);
            const timeString = startTime.toTimeString().slice(0, 5); // Show only start time

            // Check if this slot is in current user's cart
            const isInCart = cart.some(item =>
              item.time_slot_id === slot.id
            );

            // Check if this slot is reserved by ANY user (including current user)
            const reservation = allReservations.find(res => res.time_slot_id === slot.id);
            const isReservedByOther = reservation && reservation.user_session_id !== userSessionId;
            const isReservedByCurrentUser = reservation && reservation.user_session_id === userSessionId;

            // Find booking details for this slot
            const booking = bookings?.find(b => b.time_slot_id === slot.id);

            return {
              id: slot.id,
              time_slot_id: slot.id,
              location_id: location.id,
              location_name: location.name,
              time: timeString,
              activity_type: location.activity_type,
              fixed_level: location.fixed_level,
              level: slot.level,
              isBooked: slot.is_booked,
              isInCart: isInCart || isReservedByCurrentUser,
              isReservedByOther,
              booking: booking
            };
          });
        }
      }

      // Apply the "show only available" filter if active
      const finalTimeSlots = showOnlyAvailable
        ? timeSlots.filter(slot => !slot.isBooked && !slot.isReservedByOther)
        : timeSlots;

      const availableSlotsCount = timeSlots.filter(slot => !slot.isBooked && !slot.isReservedByOther).length;

      return (
        <div key={location.id} className="space-y-2">

          {!activityMode && (
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center">

                <Target className="w-4 h-4 mr-1" />
                {location.activity_type}
              </div>
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-1" />
                {availableSlotsCount} available
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4  gap-x-1 gap-y-1">
            {finalTimeSlots.map((slot) => (
              <SlotButton
                key={`${slot.id}-${slot.expires_at ?? ''}`}
                slot={slot}
                eventId={eventId}
                reserveSlot={reserveSlot}
                userSessionId={userSessionId}
              />
            ))}
          </div>
        </div>
      );
    })}
    </div>
  );
};

interface SlotButtonProps {
  slot: {
    id: string;
    time_slot_id: string;
    location_id: string;
    location_name: string;
    time: string;
    activity_type: string;
    activity_id?: string;
    fixed_level?: string;
    level?: string;
    isBooked: boolean;
    isInCart: boolean;
    isReservedByOther: boolean;
    booking?: {
      participant_name: string;
      horse_name: string;
      time_slot_level?: string;
      activity_level?: string;
      activity_specific_type?: string;
      dressage_test_label?: string;
    };
    expires_at?: string | null;
  };
  eventId?: string;
  reserveSlot: ReturnType<typeof useReserveSlot>;
  userSessionId: string;
}

const SlotButton: React.FC<SlotButtonProps> = ({ slot, eventId, reserveSlot, userSessionId }) => {
  const [isSelected, setIsSelected] = useState(false);
  const [isReserving, setIsReserving] = useState(false);
  const { getCartRetentionMinutes } = useAppSettings();
  const holdDurationMinutes = getCartRetentionMinutes();
  const queryClient = useQueryClient();

  const initialRemainingTime = useMemo(() => {
    return slot.expires_at
      ? new Date(slot.expires_at).getTime() - Date.now()
      : null;
  }, [slot.expires_at]);

  const [remainingTime, setRemainingTime] = useState<number | null>(initialRemainingTime);

  useEffect(() => {
    setRemainingTime(initialRemainingTime);
  }, [initialRemainingTime]);

  useEffect(() => {
    if (!slot.expires_at) return;

    const updateRemaining = () => {
      const newRemaining = new Date(slot.expires_at).getTime() - Date.now();
      setRemainingTime(newRemaining);
    };

    updateRemaining(); // recalculate immediately
    const interval = setInterval(updateRemaining, 5000);
    return () => clearInterval(interval);
  }, [slot.expires_at]);

  useEffect(() => {
    if (remainingTime !== null && remainingTime <= 0) {
      setIsSelected(false);
    }
  }, [remainingTime]);

  const handleSlotClick = () => {
    if (slot.isBooked || slot.isInCart || slot.isReservedByOther) return;
    setIsSelected(!isSelected);
  };

  const handleReserveSlot = async () => {
    setIsReserving(true);

    try {
      // 🔍 DEBUG: Log the time slot ID being reserved
      console.log('🔍 [DEBUG] Attempting to reserve time slot:', {
        timeSlotId: slot.time_slot_id,
        slotId: slot.id,
        locationId: slot.location_id,
        locationName: slot.location_name,
        time: slot.time,
        userSessionId: userSessionId,
        timestamp: new Date().toISOString()
      });

      await reserveSlot.mutateAsync({
        timeSlotId: slot.time_slot_id,
        userSessionId: userSessionId,
      });

      // 🔍 DEBUG: Log successful reservation
      console.log('✅ [DEBUG] Successfully reserved time slot:', {
        timeSlotId: slot.time_slot_id,
        slotId: slot.id,
        timestamp: new Date().toISOString()
      });

      queryClient.invalidateQueries({ queryKey: ['slot_reservations', userSessionId] });

      const holdDurationMs = holdDurationMinutes * 60 * 1000;
      const estimatedExpiresAt = new Date(Date.now() + holdDurationMs);
      slot.expires_at = estimatedExpiresAt.toISOString();
      setRemainingTime(holdDurationMs);

      setIsSelected(false);
    } catch (error) {
      // 🔍 DEBUG: Log reservation failure
      console.error('❌ [DEBUG] Failed to reserve time slot:', {
        timeSlotId: slot.time_slot_id,
        slotId: slot.id,
        error: error.message,
        timestamp: new Date().toISOString()
      });

      toast({
        title: "Error",
        description: "Failed to reserve slot. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsReserving(false);
    }
  };

  // Show booking details for booked slots - available to everyone (public view)
  if (slot.isBooked && slot.booking) {
    return (
      <div className="bg-card border border-border rounded-sm p-1 text-xs max-w-full">
        <div className="flex justify-center items-center gap-1 mb-1">
          <div className="font-medium text-muted-foreground text-[11px]">
            {slot.time}
          </div>
        </div>
        <div className="bg-background border border-border rounded-sm p-1 text-[10px] space-y-0.5">
          <div className="flex items-center gap-1">
            <User className="h-3 w-3 text-muted-foreground" />
            <span className="font-medium text-[11px] text-[hsl(90,20%,25%)]">
              {slot.booking.participant_name}
            </span>
          </div>
          <div className="flex items-center gap-1 text-[hsl(90,15%,30%)]">
            <i className="fas fa-horse h-3 w-3" />
            {slot.booking.horse_name}
          </div>
          {slot.activity_type === 'show_jumping' && slot.fixed_level && (
            <div className="flex items-center gap-1 text-[hsl(90,15%,30%)]">
              <Target className="h-3 w-3" />
              {slot.fixed_level}
            </div>
          )}
          {slot.booking.activity_specific_type === 'dressage' && slot.booking.dressage_test_label && (
            <div className="text-[hsl(90,25%,35%)] text-[10px] font-medium leading-tight">
              Test: {slot.booking.dressage_test_label}
            </div>
          )}
        </div>
      </div>
    );
  } else if (slot.isBooked) {
    // Fallback for booked slots without booking details
    return (
      <div className="bg-gold border border-gold-hover rounded-lg p-1">
        <div className="text-center">
          <div className="font-medium text-primary text-xs flex justify-center">
            {slot.time}
          </div>
          {slot.activity_type === 'show_jumping' && slot.fixed_level && (
            <div className="text-xs mt-1 text-primary">
              {slot.fixed_level}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Show "In Cart by Another User" status
  if (slot.isReservedByOther) {
    return (
      <div className="bg-cream-light border border-cream-dark rounded-lg p-1">
        <div className="text-center">
          <div className="font-medium text-text-secondary flex justify-center">
            {slot.time}
          </div>
          {slot.activity_type === 'show_jumping' && slot.fixed_level && (
            <div className="text-xs mt-1 text-text-secondary">
              {slot.fixed_level}
            </div>
          )}
          <div className="text-xs mt-1 text-text-secondary font-medium">In Another Cart</div>
        </div>
      </div>
    );
  }

  // Available slot - show selection interface
  if (!slot.isBooked && !slot.isReservedByOther) {
    return (
      <div className="space-y-1">
        <Button
          variant={slot.isInCart ? "default" : isSelected ? "default" : "outline"}
          size="sm"
          disabled={slot.isBooked || slot.isReservedByOther}
          onClick={handleSlotClick}
          className={`
            text-xs h-auto py-1 px-2 w-full
            ${slot.isInCart
              ? 'bg-primary hover:bg-primary-light text-cream'
              : isSelected
                ? 'bg-cream hover:bg-cream-light text-primary border-primary'
                : 'border-primary text-primary hover:bg-cream'
            }
            border-2
          `}
        >
          <div className="text-center w-full">
            <div className="font-medium flex justify-center">
              {slot.time}
            </div>
            {slot.activity_type === 'show_jumping' && slot.fixed_level && (
              <div className="text-xs mt-1 text-text-secondary">
                {slot.fixed_level}
              </div>
            )}
            {slot.isInCart && (
              <div className="text-xs mt-1 opacity-90 space-y-0.5 text-center">
                <div>In Your Cart</div>
                {remainingTime !== null && (
                  <div className={`${remainingTime < 2 * 60 * 1000 ? 'text-red-600' : 'text-cream-dark'}`}>
                    {remainingTime <= 0
                      ? '⏳ Hold expired'
                      : `⏳ Hold time: ${Math.floor(remainingTime / 60000)}:${String(
                        Math.floor((remainingTime % 60000) / 1000)
                      ).padStart(2, '0')}`}
                  </div>
                )}
              </div>
            )}
            {isSelected && (
              <div className="text-xs mt-1 opacity-90">Selected</div>
            )}
          </div>
        </Button>

        {/* Show reserve button and dressage test dropdown for selected slots */}
        {isSelected && !slot.isInCart && (
          <div className="mt-1 space-y-1">
            <Button
              variant="default"
              size="sm"
              className="w-full bg-primary hover:bg-primary-light text-cream text-xs"
              onClick={handleReserveSlot}
              disabled={isReserving}
            >
              {isReserving ? 'Adding to Cart...' : 'Add to Cart'}
            </Button>
          </div>
        )}
      </div>
    );
  }

  // Fallback return (should never reach here)
  return null;
};