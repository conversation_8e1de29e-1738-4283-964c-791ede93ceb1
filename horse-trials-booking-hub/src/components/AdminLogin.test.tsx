import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AdminLogin } from './AdminLogin';
import { useToast } from '@/hooks/use-toast';

// Mock the useToast hook
vi.mock('@/hooks/use-toast');

const mockUseToast = vi.mocked(useToast);

describe('AdminLogin', () => {
  const mockOnLogin = vi.fn();
  const mockOnBack = vi.fn();
  const mockToast = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseToast.mockReturnValue({ 
      toast: mockToast, 
      dismiss: vi.fn(), 
      toasts: [] 
    });
  });

  describe('Rendering', () => {
    it('renders the admin login form', () => {
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      expect(screen.getByText('Organizer Login')).toBeInTheDocument();
      expect(screen.getByText('Sign in to manage events and locations')).toBeInTheDocument();
      expect(screen.getByLabelText('Email')).toBeInTheDocument();
      expect(screen.getByLabelText('Password')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument();
    });

    it('renders the back button', () => {
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      expect(screen.getByRole('button', { name: /back to events/i })).toBeInTheDocument();
    });

    it('renders with correct styling classes', () => {
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      // Traverse up to find the card element with the expected classes
      let card = screen.getByText('Organizer Login').parentElement;
      while (card && !card.className.includes('rounded-lg')) {
        card = card.parentElement;
      }
      expect(card).toHaveClass('rounded-lg', 'border', 'text-card-foreground', 'shadow-sm', 'bg-white', 'border-green-200');
      
      const title = screen.getByText('Organizer Login');
      expect(title).toHaveClass('text-xl', 'sm:text-2xl', 'text-green-800');
    });
  });

  describe('Form Interaction', () => {
    it('updates email input when user types', async () => {
      const user = userEvent.setup();
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const emailInput = screen.getByLabelText('Email');
      await user.type(emailInput, '<EMAIL>');
      
      expect(emailInput).toHaveValue('<EMAIL>');
    });

    it('updates password input when user types', async () => {
      const user = userEvent.setup();
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const passwordInput = screen.getByLabelText('Password');
      await user.type(passwordInput, 'password123');
      
      expect(passwordInput).toHaveValue('password123');
    });

    it('calls onBack when back button is clicked', async () => {
      const user = userEvent.setup();
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const backButton = screen.getByRole('button', { name: /back to events/i });
      await user.click(backButton);
      
      expect(mockOnBack).toHaveBeenCalledTimes(1);
    });
  });

  describe('Form Submission', () => {
    it('submits form with email and password', async () => {
      const user = userEvent.setup();
      mockOnLogin.mockResolvedValue(undefined);
      
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const emailInput = screen.getByLabelText('Email');
      const passwordInput = screen.getByLabelText('Password');
      const submitButton = screen.getByRole('button', { name: 'Sign In' });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);
      
      expect(mockOnLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
    });

    it('shows loading state during submission', async () => {
      const user = userEvent.setup();
      let resolveLogin: () => void;
      const loginPromise = new Promise<void>((resolve) => {
        resolveLogin = resolve;
      });
      mockOnLogin.mockReturnValue(loginPromise);
      
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const emailInput = screen.getByLabelText('Email');
      const passwordInput = screen.getByLabelText('Password');
      const submitButton = screen.getByRole('button', { name: 'Sign In' });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);
      
      expect(screen.getByRole('button', { name: 'Signing in...' })).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
      
      resolveLogin!();
      await waitFor(() => {
        expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument();
      });
    });

    it('prevents submission with empty fields', async () => {
      const user = userEvent.setup();
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const submitButton = screen.getByRole('button', { name: 'Sign In' });
      await user.click(submitButton);
      
      expect(mockOnLogin).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('shows error toast when login fails', async () => {
      const user = userEvent.setup();
      const loginError = new Error('Authentication failed');
      mockOnLogin.mockRejectedValue(loginError);
      
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const emailInput = screen.getByLabelText('Email');
      const passwordInput = screen.getByLabelText('Password');
      const submitButton = screen.getByRole('button', { name: 'Sign In' });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: "Authentication Error",
          description: "Invalid email or password. Please try again.",
          variant: "destructive"
        });
      });
    });

    it('resets loading state after error', async () => {
      const user = userEvent.setup();
      mockOnLogin.mockRejectedValue(new Error('Authentication failed'));
      
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const emailInput = screen.getByLabelText('Email');
      const passwordInput = screen.getByLabelText('Password');
      const submitButton = screen.getByRole('button', { name: 'Sign In' });
      
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument();
        expect(submitButton).not.toBeDisabled();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper form labels', () => {
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      expect(screen.getByLabelText('Email')).toBeInTheDocument();
      expect(screen.getByLabelText('Password')).toBeInTheDocument();
    });

    it('has required attributes on form fields', () => {
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const emailInput = screen.getByLabelText('Email');
      const passwordInput = screen.getByLabelText('Password');
      
      expect(emailInput).toHaveAttribute('required');
      expect(passwordInput).toHaveAttribute('required');
    });

    it('has proper input types', () => {
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const emailInput = screen.getByLabelText('Email');
      const passwordInput = screen.getByLabelText('Password');
      
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(passwordInput).toHaveAttribute('type', 'password');
    });
  });

  describe('Responsive Design', () => {
    it('shows full text on larger screens', () => {
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const backButton = screen.getByRole('button', { name: /back to events/i });
      expect(backButton).toHaveTextContent('Back to Events');
    });

    it('has responsive title sizing', () => {
      render(<AdminLogin onLogin={mockOnLogin} onBack={mockOnBack} />);
      
      const title = screen.getByText('Organizer Login');
      expect(title).toHaveClass('text-xl', 'sm:text-2xl');
    });
  });
}); 