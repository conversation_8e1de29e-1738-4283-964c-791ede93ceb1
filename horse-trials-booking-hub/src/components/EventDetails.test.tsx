// Import the centralized mocks. This line needs to be at the top.
import '../test/test-utils';
import { createTestQueryClient } from '../test/test-utils';

// Mock the Select component to avoid Radix UI issues (pattern from ActivityManagement.test.tsx)
import { vi } from 'vitest';
vi.mock('@/components/ui/select', () => {
  const React = require('react');
  return {
    Select: ({ children, value, onValueChange, ...props }: any) => {
      const [internalValue, setInternalValue] = React.useState(value || '');
      React.useEffect(() => { setInternalValue(value || ''); }, [value]);
      return (
        <select
          value={internalValue}
          onChange={e => {
            setInternalValue(e.target.value);
            onValueChange?.(e.target.value);
          }}
          data-testid={props['data-testid'] || 'location-filter'}
        >
          {children}
        </select>
      );
    },
    SelectContent: ({ children }: any) => <>{children}</>,
    SelectItem: ({ children, value }: any) => <option value={value}>{children}</option>,
    SelectTrigger: ({ children, ...props }: any) => {
      const { 'data-testid': _omit, ...rest } = props;
      return <div {...rest}>{children}</div>;
    },
    SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
  };
});

// src/components/EventDetails.test.tsx
import { render, screen, waitFor, waitForElementToBeRemoved, act, within } from '@testing-library/react';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import userEventLib from '@testing-library/user-event';

import { EventDetails } from './EventDetails';

let user: ReturnType<typeof userEventLib.setup>;

beforeEach(() => {
  user = userEventLib.setup();
});

const mockEvent = {
  id: '1',
  name: 'Test Event',
  start_date: '2023-12-15',
  end_date: '2023-12-16',
  event_type: 'horse_trials',
  is_active: true
};

describe('EventDetails Component', () => {
    let queryClient: QueryClient;
    let mockOnBooking: ReturnType<typeof vi.fn>;

    beforeEach(() => {
        queryClient = createTestQueryClient();
        mockOnBooking = vi.fn();
    });

    afterEach(() => {
        vi.clearAllMocks();
        // vi.restoreAllMocks(); // Use this if you are using vi.spyOn to restore original implementations
    });


    it('allows selecting an event date and displays relevant locations', async () => {
        // Render the component
        const { container } = render(
            <QueryClientProvider client={queryClient}>
                <EventDetails event={mockEvent} onBooking={mockOnBooking} />
            </QueryClientProvider>
        );

        // Wait for initial loading to complete
        const scheduleTitle = await waitFor(() =>
            screen.findByRole('heading', {
                name: /Locations for Fri, Dec 15/i,
                level: 3,
            })
        );
        expect(scheduleTitle).toHaveTextContent('Locations for Fri, Dec 15');

        // Click the second date button (Sat, Dec 16)
        const secondDateButton = await screen.findByRole('button', {
            name: /Sat, Dec 16/i
        });
        await user.click(secondDateButton);

        // Assert that the UI updates to show locations for the selected date
        const locationsTitle = await waitFor(() =>
            screen.findByRole('heading', {
                name: /Locations for Sat, Dec 16/i,
                level: 3,
            })
        );
        expect(locationsTitle).toHaveTextContent('Locations for Sat, Dec 16');

        // --- Dressage Tab Verification (should be active by default) ---
        const dressageTab = await screen.findByRole('tab', { name: /Dressage/i });
        expect(dressageTab).toBeInTheDocument();
        expect(dressageTab).toHaveAttribute('data-state', 'active');

        // Find and open the location filter under the Dressage tab
        let locationFilter = await screen.findByRole('combobox', {
            name: (accessibleName, element) => /All Locations/i.test(element.textContent || '')
        });
        await user.click(locationFilter);

        // Check options for Dressage on Sat, Dec 16
        await screen.findByRole('option', { name: 'All Locations' });
        await screen.findByRole('option', { name: 'Main Arena' });
        await screen.findByRole('option', { name: 'Another Main Arena' });
        const dressageFilterOptions = within(locationFilter).getAllByRole('option');
        expect(dressageFilterOptions.length).toBe(3); // "All Locations" + 2 specific dressage locations

        // Close the Dressage dropdown by clicking outside or pressing Escape
        await user.keyboard('{Escape}'); // Press Escape to close
        // Note: With the Select mock, options remain in DOM even when "closed"
        // This is expected behavior for the mock and matches other tests

        // --- Show Jumping Tab Verification ---
        // Find all tab elements and log them for debugging
        const allTabElements = container.querySelectorAll('[role="tab"]');
        console.log('All tab elements:', Array.from(allTabElements).map(el => ({
            textContent: el.textContent,
            dataState: el.getAttribute('data-state')
        })));

        // find show jumping tab
        const showJumpingTab = await screen.findByRole('tab', {
            name: /Show Jumping/i
        });
        expect(showJumpingTab).toBeInTheDocument();

        // click show jumping tab
        await act(async () => {
            user.click(showJumpingTab);
        });



        // check that the tab really changed
        // After clicking the Show Jumping tab and verifying the tab is active:
        await waitFor(() => {
            expect(screen.getByText(/show_jumping/i)).toBeInTheDocument();
        });

        // Find and open the location filter under the Show Jumping tab
        locationFilter = await screen.findByRole('combobox', {
            name: (accessibleName, element) => /All Locations/i.test(element.textContent || '')
        });
        await user.click(locationFilter);

        await waitFor(() => {
            const options = screen.getAllByRole('option').map((el) => el.textContent?.trim());
            console.log('[Dropdown Options]', options);
            expect(options).toContain('Jumper Ring');
        });
        // Check options for Show Jumping on Sat, Dec 16
        await screen.findByRole('option', { name: 'All Locations' });
        await screen.findByRole('option', { name: 'Jumper Ring' });
        const showJumpingFilterOptions = within(locationFilter).getAllByRole('option');
        expect(showJumpingFilterOptions.length).toBe(2); // "All Locations" + 1 specific show jumping location

        // Close the Show Jumping location filter dropdown
        await user.keyboard('{Escape}'); // Press Escape to close
        // Note: With the Select mock, options remain in DOM even when "closed"
        // This is expected behavior for the mock and matches other tests

        // --- Verify Rendered Location Sections for Sat, Dec 16 ---
        expect(await screen.findByRole('heading', { name: 'Jumper Ring', level: 4 })).toBeInTheDocument();
    });
    // More tests will go here for date selection, filtering, booking flow transition, etc.
});
