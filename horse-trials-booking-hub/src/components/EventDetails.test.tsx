// Import the centralized mocks. This line needs to be at the top.
import '../test/test-utils';
import { createTestQueryClient } from '../test/test-utils';

// src/components/EventDetails.test.tsx
import { render, screen, waitFor, fireEvent, waitForElementToBeRemoved, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import userEventLib from '@testing-library/user-event';

import { EventDetails } from './EventDetails';

let user: ReturnType<typeof userEventLib.setup>;

beforeEach(() => {
  user = userEventLib.setup();
});

const MOCK_EVENT_PROP = {
    id: 'event123',
    name: 'Spring Fling Horse Trials',
    start_date: '2023-12-17',
    end_date: '2023-12-18',
    schooling_start_date: '2023-12-15',
    schooling_end_date: '2023-12-16',
};

describe('EventDetails Component', () => {
    let queryClient: QueryClient;
    let mockOnBooking: ReturnType<typeof vi.fn>;

    beforeEach(() => {
        queryClient = createTestQueryClient();
        mockOnBooking = vi.fn();
    });

    afterEach(() => {
        vi.clearAllMocks();
        // vi.restoreAllMocks(); // Use this if you are using vi.spyOn to restore original implementations
    });


    it('allows selecting a schooling date and displays relevant locations', async () => {
        // Render the component
        const { container } = render(
            <QueryClientProvider client={queryClient}>
                <EventDetails event={MOCK_EVENT_PROP} onBooking={mockOnBooking} />
            </QueryClientProvider>
        );

        // Wait for initial loading to complete
        const scheduleTitle = await waitFor(() =>
            screen.findByRole('heading', {
                name: /Locations for Fri, Dec 15/i,
                level: 3,
            })
        );
        expect(scheduleTitle).toHaveTextContent('Locations for Fri, Dec 15');

        // Click the date selector trigger
        const dateSelectorTrigger = await screen.findByRole('combobox', {
            name: (accessibleName, element) => /Fri, Dec 15\s*3\s*Locations/i.test(element.textContent || '')
        });
        fireEvent.click(dateSelectorTrigger);

        // Select the second schooling date option
        const secondSchoolingDateOption = await screen.findByRole('option', { name: /Sat, Dec 16\s*3\s*Locations/i });
        fireEvent.click(secondSchoolingDateOption);

        // Assert that the UI updates to show locations for the selected date
        const locationsTitle = await waitFor(() =>
            screen.findByRole('heading', {
                name: /Locations for Sat, Dec 16/i,
                level: 3,
            })
        );
        expect(locationsTitle).toHaveTextContent('Locations for Sat, Dec 16');

        // --- Dressage Tab Verification (should be active by default) ---
        const dressageTab = await screen.findByRole('tab', { name: /Dressage/i });
        expect(dressageTab).toBeInTheDocument();
        expect(dressageTab).toHaveAttribute('data-state', 'active');

        // Find and open the location filter under the Dressage tab
        let locationFilter = await screen.findByRole('combobox', {
            name: (accessibleName, element) => /All Locations/i.test(element.textContent || '')
        });
        fireEvent.click(locationFilter);

        // Check options for Dressage on Sat, Dec 16
        await screen.findByRole('option', { name: 'All Locations' });
        await screen.findByRole('option', { name: 'Main Arena' });
        await screen.findByRole('option', { name: 'Another Main Arena' });
        let dressageFilterOptions = screen.getAllByRole('option');
        expect(dressageFilterOptions.length).toBe(3); // "All Locations" + 2 specific dressage locations

        // Close the Dressage location filter dropdown
        fireEvent.keyDown(document.body, { key: 'Escape', code: 'Escape' }); // Press Escape to close
        await waitFor(() => {
            expect(screen.queryByRole('option', { name: 'All Locations' })).not.toBeInTheDocument();
        });

        // --- Show Jumping Tab Verification ---
        // Find all tab elements and log them for debugging
        const allTabElements = container.querySelectorAll('[role="tab"]');
        console.log('All tab elements:', Array.from(allTabElements).map(el => ({
            textContent: el.textContent,
            dataState: el.getAttribute('data-state')
        })));

        // find show jumping tab
        const showJumpingTab = await screen.findByRole('tab', {
            name: /Show Jumping/i
        });
        expect(showJumpingTab).toBeInTheDocument();

        // click show jumping tab
        await act(async () => {
            user.click(showJumpingTab);
        });



        // check that the tab really changed
        // After clicking the Show Jumping tab and verifying the tab is active:
        await waitFor(() => {
            expect(screen.getByText(/show_jumping/i)).toBeInTheDocument();
        });

        // Find and open the location filter under the Show Jumping tab
        locationFilter = await screen.findByRole('combobox', {
            name: (accessibleName, element) => /All Locations/i.test(element.textContent || '')
        });
        fireEvent.click(locationFilter);

        await waitFor(() => {
            const options = screen.getAllByRole('option').map((el) => el.textContent?.trim());
            console.log('[Dropdown Options]', options);
            expect(options).toContain('Jumper Ring');
        });
        // Check options for Show Jumping on Sat, Dec 16
        await screen.findByRole('option', { name: 'All Locations' });
        await screen.findByRole('option', { name: 'Jumper Ring' });
        let showJumpingFilterOptions = screen.getAllByRole('option');
        expect(showJumpingFilterOptions.length).toBe(2); // "All Locations" + 1 specific show jumping location

        // Close the Show Jumping location filter dropdown
        fireEvent.keyDown(document.body, { key: 'Escape', code: 'Escape' }); // Press Escape to close
        await waitFor(() => {
            expect(screen.queryByRole('option', { name: 'All Locations' })).not.toBeInTheDocument();
        });

        // --- Verify Rendered Location Sections for Sat, Dec 16 ---
        expect(await screen.findByText('Jumper Ring')).toBeInTheDocument();
    });
    // More tests will go here for date selection, filtering, booking flow transition, etc.
});
