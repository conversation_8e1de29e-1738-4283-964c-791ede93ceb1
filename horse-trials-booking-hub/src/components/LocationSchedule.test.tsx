import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { LocationSchedule } from './LocationSchedule';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock UI components
vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, className }: { children: React.ReactNode; onClick?: () => void; disabled?: boolean; className?: string }) => (
    <button data-testid="button" onClick={onClick} disabled={disabled} className={className}>{children}</button>
  ),
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant, className }: { children: React.ReactNode; variant?: string; className?: string }) => (
    <span data-testid="badge" data-variant={variant} className={className}>{children}</span>
  ),
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => <div data-testid="card-content">{children}</div>,
  CardHeader: ({ children }: { children: React.ReactNode }) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: { children: React.ReactNode }) => <div data-testid="card-title">{children}</div>,
}));

// Mock hooks
vi.mock('@/hooks/useSlotReservations', () => ({
  useSlotReservations: vi.fn(),
  useReserveSlot: vi.fn(),
  getUserSessionId: vi.fn(() => 'test-session-id'),
}));

vi.mock('@/hooks/useBookings', () => ({
  useBookings: vi.fn(),
}));

vi.mock('@/hooks/useTimeSlots', () => ({
  useTimeSlots: vi.fn(),
}));

vi.mock('@/hooks/useAppSettings', () => ({
  useAppSettings: vi.fn(),
}));

vi.mock('@supabase/auth-helpers-react', () => ({
  useSession: vi.fn(),
}));

// Import mocked hooks after mocking
import { useSlotReservations, useReserveSlot } from '@/hooks/useSlotReservations';
import { useBookings } from '@/hooks/useBookings';
import { useTimeSlots } from '@/hooks/useTimeSlots';
import { useAppSettings } from '@/hooks/useAppSettings';
import { useSession } from '@supabase/auth-helpers-react';

const mockUseSlotReservations = vi.mocked(useSlotReservations);
const mockUseReserveSlot = vi.mocked(useReserveSlot);
const mockUseBookings = vi.mocked(useBookings);
const mockUseTimeSlots = vi.mocked(useTimeSlots);
const mockUseAppSettings = vi.mocked(useAppSettings);
const mockUseSession = vi.mocked(useSession);

describe('LocationSchedule', () => {
  let queryClient: QueryClient;

  const mockTimeSlots = [
    {
      id: 'slot1',
      location_id: 'loc1',
      start_time: '2024-01-15T09:00:00Z',
      end_time: '2024-01-15T09:30:00Z',
      level: 'Beginner',
      is_booked: false,
      activity_id: 'act1',
    },
    {
      id: 'slot2',
      location_id: 'loc1',
      start_time: '2024-01-15T09:30:00Z',
      end_time: '2024-01-15T10:00:00Z',
      level: 'Beginner',
      is_booked: true,
      activity_id: 'act1',
    },
  ];

  const mockSlotReservations = [
    {
      id: 'res1',
      time_slot_id: 'slot1',
      user_session_id: 'test-session-id',
      expires_at: new Date(Date.now() + 600000).toISOString(),
    },
  ];

  const mockBookings = [
    {
      id: 'booking1',
      time_slot_id: 'slot2',
      participant_name: 'John Doe',
      horse_name: 'Thunder',
      time_slot_level: 'Beginner',
      activity_specific_type: 'dressage',
    },
  ];

  const mockAppSettings = {
    currency_symbol: '$',
    dressage_price: 45,
    show_jumping_price: 35,
    cross_country_price: 50,
  };

  const mockSession = {
    user: {
      id: 'user123',
      email: '<EMAIL>',
    },
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    // Setup default mocks
    mockUseSession.mockReturnValue(mockSession);
    
    mockUseSlotReservations.mockReturnValue({
      data: mockSlotReservations,
      isLoading: false,
      error: null,
    });

    mockUseReserveSlot.mockReturnValue({
      mutateAsync: vi.fn().mockResolvedValue({}),
      isPending: false,
    });

    mockUseBookings.mockReturnValue({
      data: mockBookings,
      isLoading: false,
      error: null,
    });

    mockUseTimeSlots.mockReturnValue({
      data: mockTimeSlots,
      isLoading: false,
      error: null,
    });

    mockUseAppSettings.mockReturnValue({
      data: mockAppSettings,
      isLoading: false,
      error: null,
      getCartRetentionMinutes: () => 10,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const renderLocationSchedule = (props = {}) => {
    const defaultProps = {
      locations: [
        {
          id: 'loc1',
          name: 'Main Arena',
          activity_type: 'dressage',
          activities: [
            {
              id: 'act1',
              activity_type: 'dressage',
              start_time: '2024-01-15T09:00:00Z',
              end_time: '2024-01-15T12:00:00Z',
              level: 'Beginner',
            },
          ],
        },
      ],
      date: '2024-01-15',
      ...props,
    };

    return render(
      <QueryClientProvider client={queryClient}>
        <LocationSchedule {...defaultProps} />
      </QueryClientProvider>
    );
  };

  describe('Rendering', () => {
    it('renders time slots correctly', () => {
      renderLocationSchedule();

      expect(screen.getByText('01:00')).toBeInTheDocument();
      expect(screen.getByText('01:30')).toBeInTheDocument();
    });

    it('shows booking status for booked slots', () => {
      renderLocationSchedule();

      // Should show "John Doe" for booked slot
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });

  describe('Slot Interactions', () => {
    it('shows "In Your Cart" for available slots', () => {
      renderLocationSchedule();

      const inCartTexts = screen.getAllByText('In Your Cart');
      expect(inCartTexts.length).toBeGreaterThan(0);
    });

    it('handles slot reservation when slot is clicked', async () => {
      const user = userEvent.setup();
      const mockMutateAsync = vi.fn().mockResolvedValue({});
      
      mockUseReserveSlot.mockReturnValue({
        mutateAsync: mockMutateAsync,
        isPending: false,
      });

      renderLocationSchedule();

      const buttons = screen.getAllByTestId('button');
      const addToCartButton = buttons.find(button => 
        button.textContent?.includes('Add to Cart')
      );
      
      if (addToCartButton) {
        await user.click(addToCartButton);
        expect(mockMutateAsync).toHaveBeenCalled();
      }
    });
  });

  describe('Filtering and Display', () => {
    it('does not show slots from other locations', () => {
      renderLocationSchedule();

      // Should not show slots from other locations
      expect(screen.queryByText('02:00')).not.toBeInTheDocument();
    });
  });
}); 