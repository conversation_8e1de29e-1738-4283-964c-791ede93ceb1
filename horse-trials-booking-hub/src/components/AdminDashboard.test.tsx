import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import { AdminDashboard } from './AdminDashboard';

// Mock the UI components
vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs">{children}</div>,
  TabsContent: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs-content">{children}</div>,
  TabsList: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children }: { children: React.ReactNode }) => <button data-testid="tabs-trigger">{children}</button>,
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => <div data-testid="card-content">{children}</div>,
  CardDescription: ({ children }: { children: React.ReactNode }) => <div data-testid="card-description">{children}</div>,
  CardFooter: ({ children }: { children: React.ReactNode }) => <div data-testid="card-footer">{children}</div>,
  CardHeader: ({ children }: { children: React.ReactNode }) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: { children: React.ReactNode }) => <div data-testid="card-title">{children}</div>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ 
    children, 
    onClick, 
    disabled, 
    className, 
    variant 
  }: { 
    children: React.ReactNode; 
    onClick?: () => void; 
    disabled?: boolean; 
    className?: string;
    variant?: string;
  }) => (
    <button 
      data-testid="button" 
      onClick={onClick} 
      disabled={disabled}
      className={className}
      data-variant={variant}
    >
      {children}
    </button>
  ),
}));

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  CalendarDays: () => <div data-testid="calendar-days-icon">CalendarDays</div>,
  Users: () => <div data-testid="users-icon">Users</div>,
  Settings: () => <div data-testid="settings-icon">Settings</div>,
  ArrowRight: () => <div data-testid="arrow-right-icon">ArrowRight</div>,
}));

describe('AdminDashboard Component', () => {
  const defaultProps = {
    onEventManage: vi.fn(),
    onCreateEvent: vi.fn(),
    onSettingsManage: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the admin dashboard with correct title and description', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Manage events, locations, users, and bookings')).toBeInTheDocument();
    });

    it('renders all three main cards', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const cards = screen.getAllByTestId('card');
      expect(cards).toHaveLength(3);
    });

    it('renders Events card with correct content', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      expect(screen.getByText('Events')).toBeInTheDocument();
      expect(screen.getByText('Manage competition and training events')).toBeInTheDocument();
      expect(screen.getByText('Create, edit, and configure events, locations, levels, and dressage tests.')).toBeInTheDocument();
      expect(screen.getByTestId('calendar-days-icon')).toBeInTheDocument();
    });

    it('renders Users card with correct content', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const cardTitles = screen.getAllByTestId('card-title');
      const usersCardTitle = cardTitles.find(title => title.textContent === 'Users');
      expect(usersCardTitle).toBeInTheDocument();
      expect(screen.getByText('Manage user accounts and permissions')).toBeInTheDocument();
      expect(screen.getByText('Add, edit, and remove user accounts. Configure user roles and permissions.')).toBeInTheDocument();
      expect(screen.getByTestId('users-icon')).toBeInTheDocument();
    });

    it('renders Settings card with correct content', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const cardTitles = screen.getAllByTestId('card-title');
      const settingsCardTitle = cardTitles.find(title => title.textContent === 'Settings');
      expect(settingsCardTitle).toBeInTheDocument();
      expect(screen.getByText('Configure system settings')).toBeInTheDocument();
      expect(screen.getByText('Manage global settings, pricing, and system preferences.')).toBeInTheDocument();
      expect(screen.getByTestId('settings-icon')).toBeInTheDocument();
    });
  });

  describe('Button Interactions', () => {
    it('calls onCreateEvent when Create New button is clicked', async () => {
      const user = userEvent.setup();
      render(<AdminDashboard {...defaultProps} />);
      
      const createButton = screen.getByText('Create New');
      await user.click(createButton);
      
      expect(defaultProps.onCreateEvent).toHaveBeenCalledTimes(1);
    });

    it('calls onEventManage when Manage Events button is clicked', async () => {
      const user = userEvent.setup();
      render(<AdminDashboard {...defaultProps} />);
      
      const manageButton = screen.getByText('Manage Events');
      await user.click(manageButton);
      
      expect(defaultProps.onEventManage).toHaveBeenCalledTimes(1);
    });

    it('calls onSettingsManage when Manage Settings button is clicked', async () => {
      const user = userEvent.setup();
      render(<AdminDashboard {...defaultProps} />);
      
      const settingsButton = screen.getByText('Manage Settings');
      await user.click(settingsButton);
      
      expect(defaultProps.onSettingsManage).toHaveBeenCalledTimes(1);
    });

    it('has disabled Users button with Coming Soon text', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      // Use getAllByText to get all "Coming Soon" elements and find the button
      const comingSoonElements = screen.getAllByText('Coming Soon');
      const usersButton = comingSoonElements.find(el => el.closest('button'));
      expect(usersButton?.closest('button')).toBeDisabled();
    });
  });

  describe('Responsive Design', () => {
    it('renders mobile-friendly button text for Create New', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const createButton = screen.getByText('Create New');
      expect(createButton).toBeInTheDocument();
    });

    it('renders mobile-friendly button text for Manage Events', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const manageButton = screen.getByText('Manage Events');
      expect(manageButton).toBeInTheDocument();
    });

    it('renders mobile-friendly button text for Settings', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const settingsButton = screen.getByText('Manage Settings');
      expect(settingsButton).toBeInTheDocument();
    });

    it('renders mobile-friendly button text for Coming Soon', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      // Check that both mobile and desktop versions exist
      const comingSoonElements = screen.getAllByText('Coming Soon');
      expect(comingSoonElements).toHaveLength(2); // Mobile and desktop versions
    });
  });

  describe('Icons and Visual Elements', () => {
    it('renders all required icons', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      expect(screen.getByTestId('calendar-days-icon')).toBeInTheDocument();
      expect(screen.getByTestId('users-icon')).toBeInTheDocument();
      expect(screen.getByTestId('settings-icon')).toBeInTheDocument();
    });

    it('renders arrow icons on all buttons', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const arrowIcons = screen.getAllByTestId('arrow-right-icon');
      expect(arrowIcons).toHaveLength(3); // 3 buttons have arrows (Create New, Manage Events, Settings)
    });
  });

  describe('Card Layout and Styling', () => {
    it('applies correct CSS classes to Events card', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const cards = screen.getAllByTestId('card');
      const eventsCard = cards[0];
      expect(eventsCard.className).toContain('border-green-200');
      expect(eventsCard.className).toContain('hover:shadow-md');
      expect(eventsCard.className).toContain('transition-shadow');
    });

    it('applies correct CSS classes to Users card', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const cards = screen.getAllByTestId('card');
      const usersCard = cards[1];
      expect(usersCard.className).toContain('border-blue-200');
    });

    it('applies correct CSS classes to Settings card', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const cards = screen.getAllByTestId('card');
      const settingsCard = cards[2];
      expect(settingsCard.className).toContain('border-purple-200');
      expect(settingsCard.className).toContain('lg:col-span-2');
      expect(settingsCard.className).toContain('xl:col-span-1');
    });
  });

  describe('Button Styling', () => {
    it('applies correct styling to Create New button', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const createButton = screen.getByText('Create New').closest('button');
      expect(createButton?.getAttribute('data-variant')).toBe('outline');
      expect(createButton?.className).toContain('border-green-500');
      expect(createButton?.className).toContain('text-green-700');
    });

    it('applies correct styling to Manage Events button', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const manageButton = screen.getByText('Manage Events').closest('button');
      expect(manageButton?.className).toContain('bg-green-600');
      expect(manageButton?.className).toContain('hover:bg-green-700');
    });

    it('applies correct styling to Users button', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const comingSoonElements = screen.getAllByText('Coming Soon');
      const usersButton = comingSoonElements.find(el => el.closest('button'))?.closest('button');
      expect(usersButton?.className).toContain('bg-blue-600');
      expect(usersButton?.className).toContain('hover:bg-blue-700');
    });

    it('applies correct styling to Settings button', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const settingsButton = screen.getByText('Manage Settings').closest('button');
      expect(settingsButton?.className).toContain('bg-purple-600');
      expect(settingsButton?.className).toContain('hover:bg-purple-700');
    });
  });

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toHaveTextContent('Admin Dashboard');
    });

    it('has clickable buttons with proper roles', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
      
      buttons.forEach(button => {
        expect(button).toBeInTheDocument();
      });
    });

    it('has disabled button for Users section', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const comingSoonElements = screen.getAllByText('Coming Soon');
      const usersButton = comingSoonElements.find(el => el.closest('button'))?.closest('button');
      expect(usersButton).toBeDisabled();
    });
  });

  describe('Edge Cases', () => {
    it('handles missing callback props gracefully', () => {
      const propsWithoutCallbacks = {};
      render(<AdminDashboard {...propsWithoutCallbacks} />);
      
      // Should still render without errors
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    });

    it('renders correctly with empty callback functions', () => {
      const propsWithEmptyCallbacks = {
        onEventManage: () => {},
        onCreateEvent: () => {},
        onSettingsManage: () => {},
      };
      
      render(<AdminDashboard {...propsWithEmptyCallbacks} />);
      
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
      expect(screen.getAllByTestId('card')).toHaveLength(3);
    });
  });

  describe('Component Structure', () => {
    it('has correct grid layout classes', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const container = screen.getByText('Admin Dashboard').closest('div');
      const gridContainer = container?.nextElementSibling;
      
      expect(gridContainer?.className).toContain('grid');
      expect(gridContainer?.className).toContain('grid-cols-1');
      expect(gridContainer?.className).toContain('lg:grid-cols-2');
      expect(gridContainer?.className).toContain('xl:grid-cols-3');
    });

    it('has proper spacing classes', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      // Find the main container div that wraps everything
      const mainContainer = screen.getByText('Admin Dashboard').parentElement?.parentElement;
      expect(mainContainer?.className).toContain('space-y-6');
      expect(mainContainer?.className).toContain('px-4');
      expect(mainContainer?.className).toContain('sm:px-0');
    });
  });
}); 