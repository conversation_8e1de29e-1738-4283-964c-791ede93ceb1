import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import { AdminDashboard } from './AdminDashboard';

// Mock the UI components
vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs">{children}</div>,
  TabsContent: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs-content">{children}</div>,
  TabsList: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children }: { children: React.ReactNode }) => <button data-testid="tabs-trigger">{children}</button>,
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => <div data-testid="card-content">{children}</div>,
  CardDescription: ({ children }: { children: React.ReactNode }) => <div data-testid="card-description">{children}</div>,
  CardFooter: ({ children }: { children: React.ReactNode }) => <div data-testid="card-footer">{children}</div>,
  CardHeader: ({ children }: { children: React.ReactNode }) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: { children: React.ReactNode }) => <div data-testid="card-title">{children}</div>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ 
    children, 
    onClick, 
    disabled, 
    className, 
    variant 
  }: { 
    children: React.ReactNode; 
    onClick?: () => void; 
    disabled?: boolean; 
    className?: string;
    variant?: string;
  }) => (
    <button 
      data-testid="button" 
      onClick={onClick} 
      disabled={disabled}
      className={className}
      data-variant={variant}
    >
      {children}
    </button>
  ),
}));

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  Users: () => <div data-testid="users-icon">Users</div>,
  Settings: () => <div data-testid="settings-icon">Settings</div>,
  ArrowRight: () => <div data-testid="arrow-right-icon">ArrowRight</div>,
}));

describe('AdminDashboard Component', () => {
  const defaultProps = {
    onSettingsManage: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the admin dashboard with correct title and description', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Manage events, locations, users, and bookings')).toBeInTheDocument();
    });

    it('renders all two main cards', () => {
      render(<AdminDashboard {...defaultProps} />);

      const cards = screen.getAllByTestId('card');
      expect(cards).toHaveLength(2);
    });



    it('renders Users card with correct content', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const cardTitles = screen.getAllByTestId('card-title');
      const usersCardTitle = cardTitles.find(title => title.textContent === 'Users');
      expect(usersCardTitle).toBeInTheDocument();
      expect(screen.getByText('Manage user accounts and permissions')).toBeInTheDocument();
      expect(screen.getByText('Add, edit, and remove user accounts. Configure user roles and permissions.')).toBeInTheDocument();
      expect(screen.getByTestId('users-icon')).toBeInTheDocument();
    });

    it('renders Settings card with correct content', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const cardTitles = screen.getAllByTestId('card-title');
      const settingsCardTitle = cardTitles.find(title => title.textContent === 'Settings');
      expect(settingsCardTitle).toBeInTheDocument();
      expect(screen.getByText('Configure system settings')).toBeInTheDocument();
      expect(screen.getByText('Manage global settings, pricing, and system preferences.')).toBeInTheDocument();
      expect(screen.getByTestId('settings-icon')).toBeInTheDocument();
    });
  });

  describe('Button Interactions', () => {


    it('calls onSettingsManage when Manage Settings button is clicked', async () => {
      const user = userEvent.setup();
      render(<AdminDashboard {...defaultProps} />);
      
      const settingsButton = screen.getByText('Manage Settings');
      await user.click(settingsButton);
      
      expect(defaultProps.onSettingsManage).toHaveBeenCalledTimes(1);
    });

    it('has disabled Users button with Coming Soon text', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      // Use getAllByText to get all "Coming Soon" elements and find the button
      const comingSoonElements = screen.getAllByText('Coming Soon');
      const usersButton = comingSoonElements.find(el => el.closest('button'));
      expect(usersButton?.closest('button')).toBeDisabled();
    });
  });

  describe('Responsive Design', () => {


    it('renders mobile-friendly button text for Settings', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const settingsButton = screen.getByText('Manage Settings');
      expect(settingsButton).toBeInTheDocument();
    });

    it('renders mobile-friendly button text for Coming Soon', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      // Check that both mobile and desktop versions exist
      const comingSoonElements = screen.getAllByText('Coming Soon');
      expect(comingSoonElements).toHaveLength(2); // Mobile and desktop versions
    });
  });

  describe('Icons and Visual Elements', () => {
    it('renders all required icons', () => {
      render(<AdminDashboard {...defaultProps} />);

      expect(screen.getByTestId('users-icon')).toBeInTheDocument();
      expect(screen.getByTestId('settings-icon')).toBeInTheDocument();
    });

    it('renders arrow icons on all buttons', () => {
      render(<AdminDashboard {...defaultProps} />);

      const arrowIcons = screen.getAllByTestId('arrow-right-icon');
      expect(arrowIcons).toHaveLength(1); // 1 button has arrows (Settings)
    });
  });

  describe('Card Layout and Styling', () => {


    it('applies correct CSS classes to Users card', () => {
      render(<AdminDashboard {...defaultProps} />);

      const cards = screen.getAllByTestId('card');
      const usersCard = cards[0];
      expect(usersCard.className).toContain('border-blue-200');
    });

    it('applies correct CSS classes to Settings card', () => {
      render(<AdminDashboard {...defaultProps} />);

      const cards = screen.getAllByTestId('card');
      const settingsCard = cards[1];
      expect(settingsCard.className).toContain('border-purple-200');
    });
  });

  describe('Button Styling', () => {


    it('applies correct styling to Users button', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const comingSoonElements = screen.getAllByText('Coming Soon');
      const usersButton = comingSoonElements.find(el => el.closest('button'))?.closest('button');
      expect(usersButton?.className).toContain('bg-blue-600');
      expect(usersButton?.className).toContain('hover:bg-blue-700');
    });

    it('applies correct styling to Settings button', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const settingsButton = screen.getByText('Manage Settings').closest('button');
      expect(settingsButton?.className).toContain('bg-purple-600');
      expect(settingsButton?.className).toContain('hover:bg-purple-700');
    });
  });

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toHaveTextContent('Admin Dashboard');
    });

    it('has clickable buttons with proper roles', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
      
      buttons.forEach(button => {
        expect(button).toBeInTheDocument();
      });
    });

    it('has disabled button for Users section', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const comingSoonElements = screen.getAllByText('Coming Soon');
      const usersButton = comingSoonElements.find(el => el.closest('button'))?.closest('button');
      expect(usersButton).toBeDisabled();
    });
  });

  describe('Edge Cases', () => {
    it('handles missing callback props gracefully', () => {
      const propsWithoutCallbacks = {};
      render(<AdminDashboard {...propsWithoutCallbacks} />);
      
      // Should still render without errors
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    });

    it('renders correctly with empty callback functions', () => {
      const propsWithEmptyCallbacks = {
        onSettingsManage: () => {},
      };
      
      render(<AdminDashboard {...propsWithEmptyCallbacks} />);

      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
      expect(screen.getAllByTestId('card')).toHaveLength(2);
    });
  });

  describe('Component Structure', () => {
    it('has correct grid layout classes', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      const container = screen.getByText('Admin Dashboard').closest('div');
      const gridContainer = container?.nextElementSibling;
      
      expect(gridContainer?.className).toContain('grid');
      expect(gridContainer?.className).toContain('grid-cols-1');
      expect(gridContainer?.className).toContain('lg:grid-cols-2');
    });

    it('has proper spacing classes', () => {
      render(<AdminDashboard {...defaultProps} />);
      
      // Find the main container div that wraps everything
      const mainContainer = screen.getByText('Admin Dashboard').parentElement?.parentElement;
      expect(mainContainer?.className).toContain('space-y-6');
      expect(mainContainer?.className).toContain('px-4');
      expect(mainContainer?.className).toContain('sm:px-0');
    });
  });
}); 