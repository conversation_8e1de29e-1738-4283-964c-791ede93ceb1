import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  ArrowLeft, 
  User, 
  LogOut, 
  LogIn, 
  Settings, 
  Users, 
  Database, 
  Layers, 
  FileText,
  Calendar,
  MapPin,
  Plus,
  BookOpen,
  Shield
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getErrorMessage } from '@/types/errors';

interface HeaderProps {
  onBackToEvents?: () => void;
  onViewPublicBookings?: () => void;
  onAdminLogin?: () => void;
  showBackButton?: boolean;
  showPublicBookings?: boolean;
  isAdmin?: boolean;
  userRole?: string;
}

export const Header: React.FC<HeaderProps> = ({
  onBackToEvents,
  onViewPublicBookings,
  onAdminLogin,
  showBackButton = false,
  showPublicBookings = false,
  isAdmin = false,
  userRole,
}) => {
  const session = useSession();
  const user = session?.user;
  const { userRole: currentUserRole } = useUserRole(!!user);
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  // Temporarily disable user roles to prevent hanging
  // const { data: userRoles, isLoading: rolesLoading } = useUserRoles();
  // const { data: isSuperAdmin, isLoading: superAdminLoading } = useIsSuperAdmin();
  
  // Use the userRole prop instead of hardcoded values
  const hasAdminRole = isAdmin || userRole === 'super_admin' || userRole === 'organizer';
  const primaryRole = userRole;
  
  // Authentication state
  const [showAuthModal, setShowAuthModal] = React.useState(false);
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [fullName, setFullName] = React.useState('');

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    navigate('/');
  };

  const handleAdminDashboard = () => {
    // Route to appropriate dashboard based on role
    if (userRole === 'super_admin') {
      navigate('/admin/super-admin-dashboard');
    } else if (userRole === 'organizer') {
      navigate('/admin/organizer-dashboard');
    } else {
      navigate('/admin/dashboard');
    }
  };

  const handleProfile = () => {
    navigate('/profile');
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: "Signed in successfully",
        });
        setShowAuthModal(false);
        setEmail('');
        setPassword('');
      }
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });
      
      if (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: "Account created successfully! Please check your email to verify your account.",
        });
        setShowAuthModal(false);
        setEmail('');
        setPassword('');
        setFullName('');
      }
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const dropdownRef = React.useRef<HTMLDivElement | null>(null);
  const [menuOpen, setMenuOpen] = React.useState(false);

  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      <header className="bg-primary text-cream px-6 py-6">
        <div className="max-w-3xl mx-auto flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <div className="flex items-center space-x-4">
              {showBackButton && onBackToEvents && (
                <Button
                  variant="ghost"
                  onClick={onBackToEvents}
                  className="text-cream hover:bg-primary-light p-2"
                  data-testid="back-button"
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>
              )}
              
              <Link to="/" className="flex items-center space-x-2">
                <img src="/NextRiderUpLogoOnly.png" alt="NextRiderUp logo" className="w-8 h-8" />
                <h1 className="font-playfair text-2xl md:text-3xl font-semibold tracking-tight">
                  Next Rider Up
                </h1>
              </Link>
            </div>
            
            <p className="text-gold mt-1 font-semibold">
              Built by riders, for riders
            </p>
          </div>
          
          <div className="mt-4 md:mt-0 flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
            {location.pathname !== '/about' && (
              <Link to="/about">
                <Button variant="outline" className="border-gold text-gold hover:bg-gold hover:text-primary">
                  About
                </Button>
              </Link>
            )}
            {showPublicBookings && onViewPublicBookings && (
              <Button
                variant="outline"
                onClick={onViewPublicBookings}
                className="border-gold text-gold hover:bg-gold hover:text-primary"
              >
                <Calendar className="w-4 h-4 mr-2" />
                View Bookings
              </Button>
            )}

            {user ? (
              <div className="relative" ref={dropdownRef}>
                <Button
                  variant="ghost"
                  onClick={() => setMenuOpen(prev => !prev)}
                  className="text-cream hover:bg-primary-light"
                >
                  <User className="w-4 h-4 mr-2" />
                  <div className="hidden sm:block text-left">
                    <div className="text-sm font-medium">
                      {user.user_metadata.full_name || user.email}
                    </div>
                    <div className="text-xs opacity-75 capitalize">
                      {primaryRole === 'super_admin' ? 'Super Admin' : 
                       primaryRole === 'organizer' ? 'Organizer' : 'User'}
                    </div>
                  </div>
                  <svg
                    className="w-4 h-4 ml-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </Button>

                {menuOpen && (
                  <div className="absolute right-0 mt-2 w-56 bg-white border border-cream-light rounded-lg shadow-lg z-50">
                    {/* User Info Header */}
                    <div className="px-4 py-3 border-b border-cream-light">
                      <div className="text-sm font-medium text-text-primary">{user.email}</div>
                      <div className="text-xs text-text-secondary capitalize">
                        {primaryRole === 'super_admin' ? 'Super Administrator' : 
                         primaryRole === 'organizer' ? 'Event Organizer' : 'Rider'}
                      </div>
                    </div>
                    
                    {/* Navigation Links */}
                    {(hasAdminRole || primaryRole === 'organizer') && (
                      <button
                        onClick={handleAdminDashboard}
                        className="w-full text-left px-4 py-2 text-sm text-text-primary hover:bg-cream-light transition-colors"
                      >
                        <Shield className="w-4 h-4 mr-2 inline" />
                        {userRole === 'super_admin' ? 'Super Admin Dashboard' : 
                         userRole === 'organizer' ? 'Organizer Dashboard' : 
                         'Admin Dashboard'}
                      </button>
                    )}
                    {isLoading && (
                      <div className="px-4 py-2 text-sm text-text-secondary">
                        Loading permissions...
                      </div>
                    )}
                    <button
                      onClick={handleProfile}
                      className="block w-full text-left px-4 py-2 text-sm text-text-primary hover:bg-cream-light flex items-center"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Profile
                    </button>
                    <hr className="border-cream-light my-1" />
                    <button
                      onClick={handleSignOut}
                      className="block w-full text-left px-4 py-2 text-sm text-text-primary hover:bg-cream-light"
                    >
                      Sign Out
                    </button>
                  </div>
                )}
              </div>
            ) : !isLoading ? (
              <Button
                onClick={() => setShowAuthModal(true)}
                className="bg-gold text-primary font-semibold px-6 py-3 rounded-full hover:bg-gold-hover transition-colors shadow-md focus:outline-none focus:ring-2 focus:ring-gold focus:ring-offset-2"
              >
                <LogIn className="w-4 h-4 mr-2" />
                Login
              </Button>
            ) : (
              <div className="animate-pulse bg-cream-light rounded px-4 py-2" data-testid="loading-skeleton">
                <div className="h-4 w-16 bg-cream-light rounded"></div>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Authentication Modal */}
      {showAuthModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-center text-green-800">
                Welcome to Next Rider Up
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="signin" className="space-y-4">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="signin">Sign In</TabsTrigger>
                  <TabsTrigger value="signup">Sign Up</TabsTrigger>
                </TabsList>
                
                <TabsContent value="signin">
                  <form onSubmit={handleSignIn} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password">Password</Label>
                      <Input
                        id="password"
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                      />
                    </div>
                    <Button 
                      type="submit" 
                      className="w-full" 
                      disabled={isLoading}
                      data-testid="signin-submit-button"
                    >
                      {isLoading ? 'Signing In...' : 'Sign In'}
                    </Button>
                  </form>
                </TabsContent>
                
                <TabsContent value="signup">
                  <form onSubmit={handleSignUp} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="fullName">Full Name</Label>
                      <Input
                        id="fullName"
                        type="text"
                        value={fullName}
                        onChange={(e) => setFullName(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password">Password</Label>
                      <Input
                        id="password"
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                      />
                    </div>
                    <Button 
                      type="submit" 
                      className="w-full" 
                      disabled={isLoading}
                      data-testid="signup-submit-button"
                    >
                      {isLoading ? 'Creating Account...' : 'Sign Up'}
                    </Button>
                  </form>
                </TabsContent>
              </Tabs>
              
              <div className="mt-4 text-center">
                <Button
                  variant="ghost"
                  onClick={() => setShowAuthModal(false)}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
};