import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Search, Calendar, User, Clock, MapPin, Loader2 } from 'lucide-react';
import { usePublicEntries, PublicEntryFromView } from '@/hooks/useBookings'; // Changed to usePublicEntries and PublicEntryFromView
import { useLocations } from '@/hooks/useLocations'; // Changed from useArenas
import { formatDateForDisplay } from '@/utils/dateUtils';

interface PublicBookingsProps {
  event: any;
  onBack: () => void;
}

export const PublicBookings: React.FC<PublicBookingsProps> = ({ event, onBack }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLocation, setFilterLocation] = useState('all'); // Changed from filterArena
  const [filterLevel, setFilterLevel] = useState('all');
  const [filterActivity, setFilterActivity] = useState('all');
  const [filterTest, setFilterTest] = useState('all');
  const [groupBy, setGroupBy] = useState<'time' | 'location' | 'name'>('time');

  const { data: entries, isLoading } = usePublicEntries(event.id); // Changed to usePublicEntries
  const { data: locations } = useLocations(event.id); // Changed from useArenas

  const locationNames = locations?.map(location => location.name) || []; // Changed from arenaNames

  // Extract unique levels from entries
  const levels = [...new Set(
    entries?.map(entry => entry.time_slot_level || entry.activity_level).filter(Boolean) || []
  )];

  const activityTypes = [...new Set( // Assuming activity_type is directly on time_slots or activities linked to time_slots
    entries?.map(entry => entry.activity_specific_type).filter(Boolean) || []
  )];

  const dressageTests = [...new Set(
    entries // Assuming activity_type is on time_slots.activities
      ?.filter(e => e.activity_specific_type === 'dressage')
      .map(e => e.dressage_test_label)
      .filter(Boolean) || []
  )];

  const filteredEntries = entries?.filter(entry => {
    const matchesSearch = searchTerm === '' ||
      (entry.participant_name && entry.participant_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (entry.horse_name && entry.horse_name.toLowerCase().includes(searchTerm.toLowerCase()));

    const locationName = entry.location_name;
    const matchesLocation = filterLocation === 'all' || locationName === filterLocation; // Changed from matchesArena

    const entryLevel = entry.time_slot_level || entry.activity_level;
    const matchesLevel = filterLevel === 'all' || entryLevel === filterLevel;

    const entryActivity = entry.activity_specific_type;
    const matchesActivity = filterActivity === 'all' || entryActivity === filterActivity;

    const testLabel = entry.dressage_test_label;
    const matchesTest = filterTest === 'all' || testLabel === filterTest;

    return matchesSearch && matchesLocation && matchesLevel && matchesActivity && matchesTest; // Changed matchesArena
  }) || []; // Ensure PublicEntryFromView type if applicable

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const groupEntriesByActivityDate = (entries: PublicEntryFromView[]) => { // Use specific type
    return entries.reduce((groups, entry) => {
      let groupKey: string;

      if (groupBy === 'time') {
        // Group by date/time
        groupKey = entry.time_slot_start_time
          ? new Date(entry.time_slot_start_time).toISOString().split('T')[0]
          : 'Unknown Date';
      } else if (groupBy === 'location') {
        // Group by location
        groupKey = entry.location_name || 'Unknown Location';
      } else if (groupBy === 'name') {
        // Group by first letter of participant name
        groupKey = entry.participant_name 
          ? entry.participant_name.charAt(0).toUpperCase()
          : 'Unknown';
      } else {
        // Default to time grouping
        groupKey = entry.time_slot_start_time
          ? new Date(entry.time_slot_start_time).toISOString().split('T')[0]
          : 'Unknown Date';
      }

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(entry);
      return groups;
    }, {} as Record<string, PublicEntryFromView[]>); // Use specific type
  };

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-green-600 mr-3" />
          <div className="text-lg text-gray-600">Loading entries data...</div>
        </div>
      </div>
    );
  }

  const groupedEntries = groupEntriesByActivityDate(filteredEntries);
  const sortedGroups = Object.keys(groupedEntries).sort((a, b) => {
    if (groupBy === 'time') {
      // Sort dates chronologically
      return new Date(a).getTime() - new Date(b).getTime();
    } else if (groupBy === 'location') {
      // Sort locations alphabetically
      return a.localeCompare(b);
    } else if (groupBy === 'name') {
      // Sort alphabetically by first letter
      return a.localeCompare(b);
    }
    return a.localeCompare(b);
  });

  const getGroupHeader = (groupKey: string) => {
    if (groupBy === 'time') {
      return `${formatDateForDisplay(groupKey)} - ${groupedEntries[groupKey].length} Entry${groupedEntries[groupKey].length !== 1 ? 's' : ''}`;
    } else if (groupBy === 'location') {
      return `${groupKey} - ${groupedEntries[groupKey].length} Entry${groupedEntries[groupKey].length !== 1 ? 's' : ''}`;
    } else if (groupBy === 'name') {
      const entries = groupedEntries[groupKey];
      const names = entries.map(e => e.participant_name).filter(Boolean).sort();
      if (names.length === 1) {
        return `${groupKey} (${names[0]}) - 1 Entry`;
      } else if (names.length === 2) {
        return `${groupKey} (${names[0]} - ${names[1]}) - 2 Entries`;
      } else if (names.length > 2) {
        return `${groupKey} (${names[0]} - ${names[names.length - 1]}) - ${entries.length} Entries`;
      }
      return `${groupKey} - ${groupedEntries[groupKey].length} Entry${groupedEntries[groupKey].length !== 1 ? 's' : ''}`;
    }
    return `${groupKey} - ${groupedEntries[groupKey].length} Entry${groupedEntries[groupKey].length !== 1 ? 's' : ''}`;
  };

  const getGroupIcon = () => {
    if (groupBy === 'time') {
      return <Calendar className="w-5 h-5 mr-2" />;
    } else if (groupBy === 'location') {
      return <MapPin className="w-5 h-5 mr-2" />;
    } else if (groupBy === 'name') {
      return <User className="w-5 h-5 mr-2" />;
    }
    return <Calendar className="w-5 h-5 mr-2" />;
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="flex items-center space-x-4 mb-6">
        <Button
          variant="ghost"
          onClick={onBack}
          className="text-green-700 hover:bg-green-50"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Event
        </Button>
        <h2 className="text-2xl font-bold text-green-800">
          All Entries - {event.name}
        </h2>
      </div>

      <Card className="bg-white border-green-200">
        <CardHeader>
          <CardTitle className="text-lg text-green-800 flex items-center">
            <Search className="w-5 h-5 mr-2" />
            Search & Filter Entries
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Input
                placeholder="Search by rider or horse name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div>
              <Select value={filterLocation} onValueChange={setFilterLocation}> {/* Changed from filterArena */}
                <SelectTrigger>
                  <SelectValue placeholder="Filter by location" /> {/* Changed text */}
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem> {/* Changed text */}
                  {locationNames.map((name) => ( // Changed from arenaNames
                    <SelectItem key={name} value={name}>{name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select value={filterLevel} onValueChange={setFilterLevel}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  {levels.map((level) => (
                    <SelectItem key={level} value={level}>{level}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select value={filterActivity} onValueChange={setFilterActivity}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by activity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Activities</SelectItem>
                  {activityTypes.map((type) => (
                    <SelectItem key={type} value={type}>{type.replace('_', ' ')}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select value={filterTest} onValueChange={setFilterTest}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by test" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tests</SelectItem>
                  {dressageTests.map((test) => (
                    <SelectItem key={test} value={test}>{test}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-4 text-sm text-gray-600">
            Showing {filteredEntries.length} entry{filteredEntries.length !== 1 ? 's' : ''}
            {searchTerm && ` matching "${searchTerm}"`}
            {filterLocation !== 'all' && ` in ${filterLocation}`} {/* Changed from filterArena */}
            {filterLevel !== 'all' && ` for ${filterLevel} level`}
          </div>
          <div className="mt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchTerm('');
                setFilterLocation('all'); // Changed from setFilterArena
                setFilterLevel('all');
                setFilterActivity('all');
                setFilterTest('all');
                setGroupBy('time');
              }}
            >
              Reset Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-6">
        <div className="mb-4">
          <Select value={groupBy} onValueChange={(val) => setGroupBy(val as 'time' | 'location' | 'name')}>
            <SelectTrigger className="w-fit">
              <SelectValue placeholder="Group by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="time">Group by Date/Time</SelectItem>
              <SelectItem value="location">Group by Location</SelectItem>
              <SelectItem value="name">Group by First Letter of Participant Name</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {sortedGroups.map((groupKey) => {
          const dayEntries = groupedEntries[groupKey];
          return (
            <Card key={groupKey} className="bg-white border-green-200">
              <CardHeader>
                <CardTitle className="text-lg text-green-800 flex items-center">
                  {getGroupIcon()}
                  {getGroupHeader(groupKey)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {dayEntries
                    .sort((a, b) => {
                      if (groupBy === 'time') {
                        // Sort by time slot start time within time groups
                        const timeA = a.time_slot_start_time || '00:00';
                        const timeB = b.time_slot_start_time || '00:00';
                        return timeA.localeCompare(timeB);
                      } else if (groupBy === 'location') {
                        // Sort by time within location groups
                        const timeA = a.time_slot_start_time || '00:00';
                        const timeB = b.time_slot_start_time || '00:00';
                        return timeA.localeCompare(timeB);
                      } else if (groupBy === 'name') {
                        // Sort alphabetically by participant name within name groups
                        const nameA = a.participant_name || '';
                        const nameB = b.participant_name || '';
                        return nameA.localeCompare(nameB);
                      }
                      // Default sort by time
                      const timeA = a.time_slot_start_time || '00:00';
                      const timeB = b.time_slot_start_time || '00:00';
                      return timeA.localeCompare(timeB);
                    })
                    .map((entry) => (
                      <div
                        key={entry.booking_id} // Use booking_id from the view
                        className="p-4 border border-green-200 rounded-lg hover:border-green-300 transition-colors"
                      >
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center space-x-2">
                            <User className="w-4 h-4 text-green-600" />
                            <span className="font-medium text-green-800">
                              {entry.participant_name}
                            </span>
                          </div>
                          <Badge
                            variant={entry.activity_specific_type === 'dressage' ? 'default' : 'secondary'}
                            className="text-xs bg-primary text-cream"
                          >
                            {entry.activity_specific_type?.replace('_', ' ') || 'Unknown'}
                          </Badge>
                        </div>

                        <div className="space-y-2 text-sm">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-700">Horse:</span>
                            <span>{entry.horse_name}</span>
                          </div>

                          {entry.location_name && (
                            <div className="flex items-center space-x-2">
                              <MapPin className="w-3 h-3 text-gray-500" />
                              <span className="font-medium text-gray-700">Location:</span> {/* Changed text */}
                              <span>{entry.location_name}</span>
                            </div>
                          )}

                          {entry.time_slot_start_time && entry.time_slot_end_time && (
                            <>
                              <div className="flex items-center space-x-2">
                                <Calendar className="w-3 h-3 text-gray-500" />
                                <span className="font-medium text-gray-700">Date:</span>
                                <span>{formatDateForDisplay(entry.time_slot_start_time)}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Clock className="w-3 h-3 text-gray-500" />
                                <span className="font-medium text-gray-700">Time:</span>
                                <span>
                                  {formatTime(entry.time_slot_start_time)} - {formatTime(entry.time_slot_end_time)}
                                </span>
                              </div>
                            </>
                          )}

                          {entry.activity_specific_type && (
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-gray-700">Activity:</span>
                              <span className="text-sm text-gray-800">
                                {entry.activity_specific_type.replace('_', ' ')}
                              </span>
                            </div>
                          )}

                          {(entry.time_slot_level || entry.activity_level) && (
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-gray-700">Level:</span>
                              <Badge variant="outline" className="text-xs bg-cream text-primary border-primary">
                                {entry.time_slot_level || entry.activity_level}
                              </Badge>
                            </div>
                          )}

                          {entry.activity_specific_type === 'dressage' && entry.dressage_test_label && (
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-gray-700">Test:</span>
                              <Badge variant="outline" className="text-xs bg-cream text-primary border-primary">
                                {entry.dressage_test_label}
                              </Badge>
                            </div>
                          )}
                        </div>

                        <div className="mt-3 pt-2 border-t border-gray-200">
                          <div className="flex justify-between items-center">
                            {/* Removed the entry label since it's not needed for public display */}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredEntries.length === 0 && (
        <Card className="bg-gray-50 border-gray-200">
          <CardContent className="text-center py-8">
            <p className="text-gray-600">
              {entries?.length === 0
                ? "No entries found for this event."
                : "No entries found matching your search criteria."
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
