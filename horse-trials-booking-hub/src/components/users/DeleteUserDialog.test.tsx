import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import DeleteUserDialog from './DeleteUserDialog';
import { ComprehensiveUser } from '@/hooks/useAllUsers';

// Mock the useDeleteUser hook
vi.mock('@/hooks/useAllUsers', () => ({
  useDeleteUser: vi.fn(() => ({
    mutate: vi.fn(),
    isPending: false
  }))
}));

// Mock the useToast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(() => ({
    toast: vi.fn()
  }))
}));

describe('DeleteUserDialog', () => {
  const mockUser: ComprehensiveUser = {
    auth_user_id: 'user-1',
    auth_email: '<EMAIL>',
    full_name: 'Test User',
    auth_created_at: '2023-01-01T00:00:00Z',
    user_role: 'user',
    profile_phone: '************',
    user_roles: []
  };

  const defaultProps = {
    isOpen: false,
    onOpenChange: vi.fn(),
    userToDelete: null,
    onDeleteSuccess: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Dialog Visibility', () => {
    it('renders dialog when isOpen is true and userToDelete is provided', () => {
      render(<DeleteUserDialog {...defaultProps} isOpen={true} userToDelete={mockUser} />);
      
      expect(screen.getByTestId('delete-user-dialog-content')).toBeInTheDocument();
      expect(screen.getByTestId('delete-user-dialog-title')).toBeInTheDocument();
    });

    it('does not render dialog when isOpen is false', () => {
      render(<DeleteUserDialog {...defaultProps} isOpen={false} userToDelete={mockUser} />);
      
      expect(screen.queryByTestId('delete-user-dialog-content')).not.toBeInTheDocument();
    });

    it('does not render dialog when userToDelete is null', () => {
      render(<DeleteUserDialog {...defaultProps} isOpen={true} userToDelete={null} />);
      
      expect(screen.queryByTestId('delete-user-dialog-content')).not.toBeInTheDocument();
    });
  });

  describe('User Information Display', () => {
    beforeEach(() => {
      render(<DeleteUserDialog {...defaultProps} isOpen={true} userToDelete={mockUser} />);
    });

    it('displays user name in confirmation message', () => {
      expect(screen.getByText(/Test User/)).toBeInTheDocument();
    });

    it('displays user email in confirmation message', () => {
      expect(screen.getByText(/<EMAIL>/)).toBeInTheDocument();
    });

    it('shows warning message about deletion', () => {
      expect(screen.getByTestId('delete-user-warning')).toBeInTheDocument();
      expect(screen.getByText(/This action cannot be undone/)).toBeInTheDocument();
    });

    it('shows confirmation message with user details', () => {
      expect(screen.getByText(/Are you sure you want to delete/)).toBeInTheDocument();
    });

    it('lists what will be deleted', () => {
      expect(screen.getByText('Authentication system')).toBeInTheDocument();
      expect(screen.getByText('User profile')).toBeInTheDocument();
      expect(screen.getByText('User roles')).toBeInTheDocument();
      expect(screen.getByText('All associated data')).toBeInTheDocument();
    });
  });

  describe('Dialog Actions', () => {
    beforeEach(() => {
      render(<DeleteUserDialog {...defaultProps} isOpen={true} userToDelete={mockUser} />);
    });

    it('renders confirm and cancel buttons', () => {
      expect(screen.getByTestId('confirm-delete-btn')).toBeInTheDocument();
      expect(screen.getByTestId('cancel-delete-btn')).toBeInTheDocument();
    });

    it('calls onOpenChange when cancel button is clicked', async () => {
      const user = userEvent.setup();
      const cancelButton = screen.getByTestId('cancel-delete-btn');
      
      await user.click(cancelButton);
      
      expect(defaultProps.onOpenChange).toHaveBeenCalledWith(false);
    });

    it('calls confirmDeleteUser when delete button is clicked', async () => {
      const user = userEvent.setup();
      const deleteButton = screen.getByTestId('confirm-delete-btn');
      
      await user.click(deleteButton);
      
      // The actual deletion logic is handled by the hook
      expect(deleteButton).toBeInTheDocument();
    });
  });

  describe('Button Text and Styling', () => {
    beforeEach(() => {
      render(<DeleteUserDialog {...defaultProps} isOpen={true} userToDelete={mockUser} />);
    });

    it('has correct button text', () => {
      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('Delete User')).toBeInTheDocument();
    });
  });

  describe('Layout and Styling', () => {
    it('renders with correct dialog structure', () => {
      render(<DeleteUserDialog {...defaultProps} isOpen={true} userToDelete={mockUser} />);
      
      expect(screen.getByTestId('delete-user-dialog-content')).toBeInTheDocument();
      expect(screen.getByTestId('delete-user-dialog-title')).toBeInTheDocument();
    });

    it('renders warning section with correct styling', () => {
      render(<DeleteUserDialog {...defaultProps} isOpen={true} userToDelete={mockUser} />);
      
      const warning = screen.getByTestId('delete-user-warning');
      expect(warning).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles user with missing name gracefully', () => {
      const userWithoutName = { ...mockUser, full_name: null };
      render(<DeleteUserDialog {...defaultProps} isOpen={true} userToDelete={userWithoutName} />);
      
      expect(screen.getByTestId('delete-user-warning')).toBeInTheDocument();
    });

    it('handles user with missing email gracefully', () => {
      const userWithoutEmail = { ...mockUser, auth_email: null };
      render(<DeleteUserDialog {...defaultProps} isOpen={true} userToDelete={userWithoutEmail} />);
      
      expect(screen.getByTestId('delete-user-warning')).toBeInTheDocument();
    });
  });
}); 