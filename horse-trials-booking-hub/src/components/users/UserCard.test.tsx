import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import UserCard from './UserCard';
import { ComprehensiveUser } from '@/hooks/useAllUsers';
import { useUpdateUserProfile } from '@/hooks/useAllUsers';
import { useUpdateUserRole } from '@/hooks/useUserRoles';

const mockUpdateUserProfile = vi.fn();
const mockUpdateUserRole = vi.fn();

// Mock the useUpdateUserProfile hook
vi.mock('@/hooks/useAllUsers', () => ({
  useUpdateUserProfile: vi.fn(() => ({
    mutate: mockUpdateUserProfile,
    isPending: false
  }))
}));

// Mock the useUpdateUserRole hook
vi.mock('@/hooks/useUserRoles', () => ({
  useUpdateUserRole: vi.fn(() => ({
    mutate: mockUpdateUserRole,
    isPending: false
  }))
}));

// Mock the useToast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(() => ({
    toast: vi.fn()
  }))
}));

describe('UserCard', () => {
  const mockUser: ComprehensiveUser = {
    auth_user_id: 'user-1',
    auth_email: '<EMAIL>',
    full_name: 'Test User',
    user_role: 'user',
    role_display_name: 'User',
    account_status: 'active',
    profile_status: 'complete',
    role_status: 'active',
    auth_created_at: '2023-01-01T00:00:00Z',
    last_sign_in_at: '2023-01-15T00:00:00Z',
    phone: '************',
  };

  const mockOnDelete = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Set up default mocks
    const mockUseUpdateUserProfile = vi.mocked(useUpdateUserProfile);
    const mockUseUpdateUserRole = vi.mocked(useUpdateUserRole);
    
    mockUseUpdateUserProfile.mockReturnValue({
      mutate: mockUpdateUserProfile,
      isPending: false
    });
    
    mockUseUpdateUserRole.mockReturnValue({
      mutate: mockUpdateUserRole,
      isPending: false
    });
  });

  describe('User Information Display', () => {
    it('displays user name correctly', () => {
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      expect(screen.getByTestId('user-name-user-1')).toHaveTextContent('Test User');
    });

    it('displays user email correctly', () => {
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      expect(screen.getByTestId('user-email-user-1')).toHaveTextContent('<EMAIL>');
    });

    it('displays user phone when available', () => {
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      expect(screen.getByTestId('user-phone-user-1')).toHaveTextContent('Phone: ************');
    });

    it('handles user without phone gracefully', () => {
      const userWithoutPhone = { ...mockUser, phone: null };
      render(<UserCard user={userWithoutPhone} onDelete={mockOnDelete} />);
      expect(screen.queryByTestId('user-phone-user-1')).not.toBeInTheDocument();
    });

    it('displays join date correctly', () => {
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      expect(screen.getByTestId('user-join-date-user-1')).toBeInTheDocument();
    });

    it('displays role badge correctly', () => {
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      expect(screen.getByTestId('user-role-badge-user-1')).toBeInTheDocument();
    });
  });

  describe('Edit User Functionality', () => {
    it('shows edit user button', () => {
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      expect(screen.getByTestId('edit-user-btn-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('edit-user-btn-user-1')).toHaveTextContent('Edit User');
    });

    it('enters edit mode when edit button is clicked', async () => {
      const user = userEvent.setup();
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      await user.click(screen.getByTestId('edit-user-btn-user-1'));
      
      expect(screen.getByTestId('edit-user-form-user-1')).toBeInTheDocument();
    });

    it('displays all editable fields in edit mode', async () => {
      const user = userEvent.setup();
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      await user.click(screen.getByTestId('edit-user-btn-user-1'));
      
      expect(screen.getByTestId('edit-full-name-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('edit-email-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('edit-phone-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('edit-role-select-user-1')).toBeInTheDocument();
    });

    it('pre-fills form fields with current user data', async () => {
      const user = userEvent.setup();
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      await user.click(screen.getByTestId('edit-user-btn-user-1'));
      
      expect(screen.getByTestId('edit-full-name-user-1')).toHaveValue('Test User');
      expect(screen.getByTestId('edit-email-user-1')).toHaveValue('<EMAIL>');
      expect(screen.getByTestId('edit-phone-user-1')).toHaveValue('************');
    });

    it('updates form values when user types', async () => {
      const user = userEvent.setup();
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      await user.click(screen.getByTestId('edit-user-btn-user-1'));
      
      const nameInput = screen.getByTestId('edit-full-name-user-1');
      await user.type(nameInput, 'Updated Name');
      
      expect(nameInput).toHaveValue('Test UserUpdated Name');
    });

    it('shows save and cancel buttons in edit mode', async () => {
      const user = userEvent.setup();
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      await user.click(screen.getByTestId('edit-user-btn-user-1'));
      
      expect(screen.getByTestId('save-user-btn-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('cancel-edit-btn-user-1')).toBeInTheDocument();
    });

    it('exits edit mode when cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      await user.click(screen.getByTestId('edit-user-btn-user-1'));
      expect(screen.getByTestId('edit-user-form-user-1')).toBeInTheDocument();
      
      await user.click(screen.getByTestId('cancel-edit-btn-user-1'));
      expect(screen.queryByTestId('edit-user-form-user-1')).not.toBeInTheDocument();
    });
  });

  describe('Delete Functionality', () => {
    it('shows delete button', () => {
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      expect(screen.getByTestId('delete-user-btn-user-1')).toBeInTheDocument();
    });

    it('calls onDelete when delete button is clicked', async () => {
      const user = userEvent.setup();
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      await user.click(screen.getByTestId('delete-user-btn-user-1'));
      
      expect(mockOnDelete).toHaveBeenCalledWith(mockUser);
    });
  });

  describe('Loading States', () => {
    it('shows loading state on save button when updating', async () => {
      const user = userEvent.setup();
      // Override the mock to return isPending: true before rendering
      const mockUseUpdateUserProfile = vi.mocked(useUpdateUserProfile);
      mockUseUpdateUserProfile.mockReturnValue({
        mutate: mockUpdateUserProfile,
        isPending: true
      });

      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      // Click edit button to show form
      await user.click(screen.getByTestId('edit-user-btn-user-1'));
      
      expect(screen.getByTestId('save-user-btn-user-1')).toHaveTextContent('Saving...');
      expect(screen.getByTestId('save-user-btn-user-1')).toBeDisabled();
    });
  });

  describe('Accessibility', () => {
    it('has proper form labels', async () => {
      const user = userEvent.setup();
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      // Click edit button to show form
      const editButton = screen.getByTestId('edit-user-btn-user-1');
      await user.click(editButton);
      
      expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Email')).toBeInTheDocument();
      expect(screen.getByLabelText('Phone')).toBeInTheDocument();
      expect(screen.getByTestId('edit-role-select-user-1')).toBeInTheDocument();
    });

    it('has proper button labels', () => {
      render(<UserCard user={mockUser} onDelete={mockOnDelete} />);
      
      expect(screen.getByTestId('edit-user-btn-user-1')).toHaveTextContent('Edit User');
      expect(screen.getByTestId('delete-user-btn-user-1')).toHaveTextContent('Delete');
    });
  });
}); 