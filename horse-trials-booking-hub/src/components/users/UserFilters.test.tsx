import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import UserFilters from './UserFilters';

// Mock the Select component to avoid pointer-events issues
vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }) => {
    // Extract options and testId from children
    const options = [];
    let testId = null;
    
    const processChildren = (children) => {
      if (Array.isArray(children)) {
        children.forEach(child => processChildren(child));
      } else if (children?.type?.name === 'SelectContent') {
        processChildren(children.props.children);
      } else if (children?.type?.name === 'SelectItem') {
        options.push({
          value: children.props.value,
          label: children.props.children
        });
      } else if (children?.type?.name === 'SelectTrigger') {
        testId = children.props['data-testid'];
      }
    };
    processChildren(children);

    return (
      <select 
        value={value} 
        onChange={(e) => onValueChange(e.target.value)}
        data-testid={testId}
      >
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    );
  },
  SelectContent: ({ children }) => children,
  SelectItem: ({ children, value }) => ({ type: { name: 'SelectItem' }, props: { children, value } }),
  SelectTrigger: ({ children, 'data-testid': testId }) => ({ type: { name: 'SelectTrigger' }, props: { children, 'data-testid': testId } }),
  SelectValue: ({ placeholder }) => placeholder,
}));

describe('UserFilters', () => {
  const defaultProps = {
    searchTerm: '',
    roleFilter: 'all',
    onSearchChange: vi.fn(),
    onRoleFilterChange: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Search Input', () => {
    it('renders search input with placeholder', () => {
      render(<UserFilters {...defaultProps} />);
      
      expect(screen.getByTestId('search-input')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search by name or email...')).toBeInTheDocument();
    });

    it('displays current search term', () => {
      render(<UserFilters {...defaultProps} searchTerm="test search" />);
      
      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toHaveValue('test search');
    });

    it('calls onSearchChange when input changes', async () => {
      const user = userEvent.setup();
      let searchTerm = '';
      const handleSearchChange = vi.fn((value) => {
        searchTerm = value;
        rerender(<Wrapper />);
      });
      
      const Wrapper = () => (
        <UserFilters {...defaultProps} searchTerm={searchTerm} onSearchChange={handleSearchChange} />
      );
      const { rerender } = render(<Wrapper />);
      
      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'new search');
      
      expect(handleSearchChange).toHaveBeenCalledTimes(10); // "new search" has 10 characters
      expect(handleSearchChange).toHaveBeenLastCalledWith('new search');
    });
  });

  describe('Role Filter', () => {
    it('renders role select with label', () => {
      render(<UserFilters {...defaultProps} />);
      
      expect(screen.getByText('Filter by Role')).toBeInTheDocument();
      expect(screen.getByTestId('role-filter-select')).toBeInTheDocument();
    });

    it('displays current selected role', () => {
      render(<UserFilters {...defaultProps} roleFilter="organizer" />);
      
      const roleSelect = screen.getByTestId('role-filter-select');
      expect(roleSelect).toBeInTheDocument();
    });

    it('calls onRoleFilterChange when selection changes', async () => {
      const user = userEvent.setup();
      render(<UserFilters {...defaultProps} />);
      
      const roleSelect = screen.getByTestId('role-filter-select');
      await user.selectOptions(roleSelect, 'organizer');
      
      expect(defaultProps.onRoleFilterChange).toHaveBeenCalledWith('organizer');
    });
  });

  describe('Layout and Styling', () => {
    it('renders with correct container structure', () => {
      render(<UserFilters {...defaultProps} />);
      
      expect(screen.getByTestId('filters-card')).toBeInTheDocument();
    });

    it('renders search input with correct test ID', () => {
      render(<UserFilters {...defaultProps} />);
      
      expect(screen.getByTestId('search-input')).toBeInTheDocument();
    });

    it('renders role select with correct test ID', () => {
      render(<UserFilters {...defaultProps} />);
      
      expect(screen.getByTestId('role-filter-select')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper labels for screen readers', () => {
      render(<UserFilters {...defaultProps} />);
      
      expect(screen.getByText('Filter by Role')).toBeInTheDocument();
      expect(screen.getByText('Search Users')).toBeInTheDocument();
    });

    it('has proper input associations', () => {
      render(<UserFilters {...defaultProps} />);
      
      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toHaveAttribute('id', 'search');
    });
  });
}); 