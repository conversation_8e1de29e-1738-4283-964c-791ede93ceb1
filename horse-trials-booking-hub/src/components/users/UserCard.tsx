import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Mail, Calendar, Edit, Trash2 } from 'lucide-react';
import { ComprehensiveUser } from '@/hooks/useAllUsers';
import { useUpdateUserRole } from '@/hooks/useUserRoles';
import { useUpdateUserProfile } from '@/hooks/useAllUsers';
import { getRoleBadgeVariant, getRoleBadgeClassName, getRoleDisplayName } from '@/utils/userRoleUtils';
import { useToast } from '@/hooks/use-toast';

interface UserCardProps {
  user: ComprehensiveUser;
  onDelete: (user: ComprehensiveUser) => void;
}

const UserCard: React.FC<UserCardProps> = ({ user, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    full_name: user.full_name || '',
    auth_email: user.auth_email || '',
    phone: user.phone || '',
    user_role: user.user_role || 'user'
  });
  
  const updateUserRole = useUpdateUserRole();
  const updateUserProfile = useUpdateUserProfile();
  const { toast } = useToast();

  const handleEdit = () => {
    setIsEditing(true);
    setEditData({
      full_name: user.full_name || '',
      auth_email: user.auth_email || '',
      phone: user.phone || '',
      user_role: user.user_role || 'user'
    });
  };

  const handleSave = () => {
    // Update profile information
    const profileData = {
      full_name: editData.full_name,
      email: editData.auth_email,
      phone: editData.phone,
    };

    updateUserProfile.mutate(
      { userId: user.auth_user_id, profileData },
      {
        onSuccess: () => {
          // Update role if it changed
          if (editData.user_role !== user.user_role) {
            updateUserRole.mutate(
              { userId: user.auth_user_id, role: editData.user_role as 'super_admin' | 'organizer' | 'user' },
              {
                onSuccess: () => {
                  setIsEditing(false);
                  toast({
                    title: 'User updated',
                    description: 'User information has been successfully updated.',
                  });
                },
                onError: (error) => {
                  toast({
                    title: 'Error updating role',
                    description: 'Failed to update user role.',
                    variant: 'destructive',
                  });
                }
              }
            );
          } else {
            setIsEditing(false);
            toast({
              title: 'User updated',
              description: 'User information has been successfully updated.',
            });
          }
        },
        onError: (error) => {
          toast({
            title: 'Error updating user',
            description: 'Failed to update user information.',
            variant: 'destructive',
          });
        }
      }
    );
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setEditData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card className="border-gray-200" data-testid={`user-card-${user.auth_user_id}`}>
      <CardContent className="p-4">
        {isEditing ? (
          <div className="space-y-4" data-testid={`edit-user-form-${user.auth_user_id}`}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor={`full-name-${user.auth_user_id}`}>Full Name</Label>
                <Input
                  id={`full-name-${user.auth_user_id}`}
                  value={editData.full_name}
                  onChange={(e) => handleInputChange('full_name', e.target.value)}
                  data-testid={`edit-full-name-${user.auth_user_id}`}
                />
              </div>
              <div>
                <Label htmlFor={`email-${user.auth_user_id}`}>Email</Label>
                <Input
                  id={`email-${user.auth_user_id}`}
                  type="email"
                  value={editData.auth_email}
                  onChange={(e) => handleInputChange('auth_email', e.target.value)}
                  data-testid={`edit-email-${user.auth_user_id}`}
                />
              </div>
              <div>
                <Label htmlFor={`phone-${user.auth_user_id}`}>Phone</Label>
                <Input
                  id={`phone-${user.auth_user_id}`}
                  value={editData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  data-testid={`edit-phone-${user.auth_user_id}`}
                />
              </div>
              <div>
                <Label htmlFor={`role-${user.auth_user_id}`}>Role</Label>
                <Select value={editData.user_role} onValueChange={(value) => handleInputChange('user_role', value)}>
                  <SelectTrigger data-testid={`edit-role-select-${user.auth_user_id}`}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="organizer">Organizer</SelectItem>
                    <SelectItem value="super_admin">Super Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={handleSave}
                disabled={updateUserProfile.isPending || updateUserRole.isPending}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
                data-testid={`save-user-btn-${user.auth_user_id}`}
              >
                {updateUserProfile.isPending || updateUserRole.isPending ? 'Saving...' : 'Save Changes'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                data-testid={`cancel-edit-btn-${user.auth_user_id}`}
              >
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between" data-testid={`user-info-${user.auth_user_id}`}>
            <div className="flex-1">
              <div className="flex items-center space-x-3">
                <h3 className="font-semibold text-gray-800" data-testid={`user-name-${user.auth_user_id}`}>
                  {user.full_name}
                </h3>
                <Badge 
                  variant={getRoleBadgeVariant(user)} 
                  className={getRoleBadgeClassName(user)} 
                  data-testid={`user-role-badge-${user.auth_user_id}`}
                >
                  {getRoleDisplayName(user)}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 flex items-center mt-1" data-testid={`user-email-${user.auth_user_id}`}>
                <Mail className="h-3 w-3 mr-1" />
                {user.auth_email}
              </p>
              {user.phone && (
                <p className="text-sm text-gray-600 mt-1" data-testid={`user-phone-${user.auth_user_id}`}>
                  Phone: {user.phone}
                </p>
              )}
              <div className="flex items-center mt-2" data-testid={`user-join-date-${user.auth_user_id}`}>
                <Calendar className="h-3 w-3 mr-1 text-gray-400" />
                <span className="text-xs text-gray-500">
                  Joined: {new Date(user.auth_created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                data-testid={`edit-user-btn-${user.auth_user_id}`}
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit User
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(user)}
                data-testid={`delete-user-btn-${user.auth_user_id}`}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UserCard; 