import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus } from 'lucide-react';
import { useCreateUser } from '@/hooks/useUserRoles';
import { useToast } from '@/hooks/use-toast';
import { getErrorMessage } from '@/types/errors';

interface CreateUserDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

const CreateUserDialog: React.FC<CreateUserDialogProps> = ({ isOpen, onOpenChange }) => {
  const { toast } = useToast();
  const createUser = useCreateUser();
  
  const [newUserData, setNewUserData] = useState({
    email: '',
    full_name: '',
    phone: '',
    role: 'user' as 'user' | 'organizer' | 'super_admin'
  });

  const handleCreateUser = () => {
    if (!newUserData.email || !newUserData.full_name) {
      toast({
        title: 'Missing information',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    createUser.mutate(newUserData, {
      onSuccess: () => {
        toast({
          title: 'User created',
          description: `${newUserData.full_name} has been created successfully.`,
          variant: 'default',
        });
        setNewUserData({ email: '', full_name: '', phone: '', role: 'user' });
        onOpenChange(false);
      },
      onError: (error) => {
        toast({
          title: 'Creation failed',
          description: getErrorMessage(error),
          variant: 'destructive',
        });
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange} data-testid="create-user-dialog">
      <DialogTrigger asChild>
        <Button className="bg-green-600 hover:bg-green-700" data-testid="create-user-btn">
          <Plus className="w-4 h-4 mr-2" />
          Create User
        </Button>
      </DialogTrigger>
      <DialogContent data-testid="create-user-dialog-content">
        <DialogHeader>
          <DialogTitle data-testid="create-user-dialog-title">Create New User</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> A temporary password will be generated automatically. 
              The user will receive a password reset email to set their own password.
            </p>
          </div>
          <div>
            <Label htmlFor="email">Email *</Label>
            <Input
              id="email"
              type="email"
              value={newUserData.email}
              onChange={(e) => setNewUserData({ ...newUserData, email: e.target.value })}
              placeholder="<EMAIL>"
              required
              data-testid="create-user-email-input"
            />
          </div>
          <div>
            <Label htmlFor="full_name">Full Name *</Label>
            <Input
              id="full_name"
              value={newUserData.full_name}
              onChange={(e) => setNewUserData({ ...newUserData, full_name: e.target.value })}
              placeholder="John Doe"
              required
              data-testid="create-user-name-input"
            />
          </div>
          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              value={newUserData.phone}
              onChange={(e) => setNewUserData({ ...newUserData, phone: e.target.value })}
              placeholder="(*************"
              data-testid="create-user-phone-input"
            />
          </div>
          <div>
            <Label htmlFor="role">Initial Role</Label>
            <Select value={newUserData.role} onValueChange={(value) => setNewUserData({ ...newUserData, role: value as 'super_admin' | 'organizer' | 'user' })}>
              <SelectTrigger data-testid="create-user-role-select">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="user">User</SelectItem>
                <SelectItem value="organizer">Organizer</SelectItem>
                <SelectItem value="super_admin">Super Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex space-x-2 pt-4">
            <Button
              onClick={handleCreateUser}
              disabled={createUser.isPending}
              className="bg-green-600 hover:bg-green-700"
              data-testid="create-user-submit-btn"
            >
              {createUser.isPending ? 'Creating...' : 'Create User'}
            </Button>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              data-testid="create-user-cancel-btn"
            >
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateUserDialog; 