import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import UserList from './UserList';
import { ComprehensiveUser } from '@/hooks/useAllUsers';

// Mock the UserCard component
vi.mock('./UserCard', () => ({
  default: ({ user, onDelete }: any) => (
    <div data-testid={`user-card-${user.auth_user_id}`}>
      <span data-testid={`user-name-${user.auth_user_id}`}>{user.full_name}</span>
      <button onClick={() => onDelete(user)} data-testid={`delete-${user.auth_user_id}`}>Delete</button>
    </div>
  )
}));

describe('UserList', () => {
  const mockUsers: ComprehensiveUser[] = [
    {
      auth_user_id: 'user-1',
      auth_email: '<EMAIL>',
      full_name: '<PERSON>',
      auth_created_at: '2023-01-01T00:00:00Z',
      user_role: 'user',
      profile_phone: '************',
      user_roles: []
    },
    {
      auth_user_id: 'user-2',
      auth_email: '<EMAIL>',
      full_name: 'Bob Jones',
      auth_created_at: '2023-01-02T00:00:00Z',
      user_role: 'organizer',
      profile_phone: null,
      user_roles: []
    }
  ];

  const defaultProps = {
    users: mockUsers,
    filteredUsers: mockUsers,
    isLoading: false,
    onDeleteUser: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Loading State', () => {
    it('shows loading state when isLoading is true', () => {
      render(<UserList {...defaultProps} isLoading={true} />);
      
      expect(screen.getByTestId('loading-users')).toBeInTheDocument();
      expect(screen.getByText('Loading users...')).toBeInTheDocument();
    });

    it('does not show loading state when isLoading is false', () => {
      render(<UserList {...defaultProps} isLoading={false} />);
      
      expect(screen.queryByTestId('loading-users')).not.toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    it('shows no users found when users array is empty', () => {
      render(<UserList {...defaultProps} users={[]} filteredUsers={[]} />);
      
      expect(screen.getByTestId('no-users-found')).toBeInTheDocument();
      expect(screen.getByText('No users found')).toBeInTheDocument();
      expect(screen.getByText('No user profiles have been created yet.')).toBeInTheDocument();
    });

    it('shows no matching users when filteredUsers is empty but users exist', () => {
      render(<UserList {...defaultProps} filteredUsers={[]} />);
      
      expect(screen.getByTestId('no-matching-users')).toBeInTheDocument();
      expect(screen.getByText('No matching users')).toBeInTheDocument();
      expect(screen.getByText('Try adjusting your search or filter criteria.')).toBeInTheDocument();
    });

    it('does not show empty state when users exist', () => {
      render(<UserList {...defaultProps} />);
      
      expect(screen.queryByTestId('no-users-found')).not.toBeInTheDocument();
      expect(screen.queryByTestId('no-matching-users')).not.toBeInTheDocument();
    });
  });

  describe('User Cards Rendering', () => {
    it('renders UserCard for each user', () => {
      render(<UserList {...defaultProps} />);
      
      expect(screen.getByTestId('user-card-user-1')).toBeInTheDocument();
      expect(screen.getByTestId('user-card-user-2')).toBeInTheDocument();
    });

    it('displays user names in cards', () => {
      render(<UserList {...defaultProps} />);
      
      expect(screen.getByTestId('user-name-user-1')).toHaveTextContent('Alice Smith');
      expect(screen.getByTestId('user-name-user-2')).toHaveTextContent('Bob Jones');
    });

    it('passes correct props to UserCard components', () => {
      render(<UserList {...defaultProps} />);
      
      // Check that delete buttons exist and are functional
      const deleteButton1 = screen.getByTestId('delete-user-1');
      const deleteButton2 = screen.getByTestId('delete-user-2');
      
      expect(deleteButton1).toBeInTheDocument();
      expect(deleteButton2).toBeInTheDocument();
    });

    it('calls onDeleteUser when delete button is clicked', () => {
      render(<UserList {...defaultProps} />);
      
      const deleteButton = screen.getByTestId('delete-user-1');
      deleteButton.click();
      
      expect(defaultProps.onDeleteUser).toHaveBeenCalledWith(mockUsers[0]);
    });
  });

  describe('Layout and Styling', () => {
    it('renders with correct container structure', () => {
      render(<UserList {...defaultProps} />);
      
      expect(screen.getByTestId('users-list-card')).toBeInTheDocument();
    });

    it('renders card title with user count', () => {
      render(<UserList {...defaultProps} />);
      
      expect(screen.getByTestId('card-title')).toBeInTheDocument();
      expect(screen.getByText('Users (2)')).toBeInTheDocument();
    });

    it('renders users list container', () => {
      render(<UserList {...defaultProps} />);
      
      expect(screen.getByTestId('users-list')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles single user correctly', () => {
      const singleUser = [mockUsers[0]];
      render(<UserList {...defaultProps} users={singleUser} filteredUsers={singleUser} />);
      
      expect(screen.getByTestId('user-card-user-1')).toBeInTheDocument();
      expect(screen.queryByTestId('user-card-user-2')).not.toBeInTheDocument();
      expect(screen.getByText('Users (1)')).toBeInTheDocument();
    });

    it('handles large number of users', () => {
      const manyUsers = Array.from({ length: 10 }, (_, i) => ({
        ...mockUsers[0],
        auth_user_id: `user-${i + 1}`,
        full_name: `User ${i + 1}`
      }));
      
      render(<UserList {...defaultProps} users={manyUsers} filteredUsers={manyUsers} />);
      
      // Should render all 10 users
      for (let i = 1; i <= 10; i++) {
        expect(screen.getByTestId(`user-card-user-${i}`)).toBeInTheDocument();
      }
      expect(screen.getByText('Users (10)')).toBeInTheDocument();
    });

    it('handles undefined users gracefully', () => {
      render(<UserList {...defaultProps} users={undefined} filteredUsers={[]} />);
      
      expect(screen.getByTestId('no-users-found')).toBeInTheDocument();
    });

    it('handles null users gracefully', () => {
      render(<UserList {...defaultProps} users={null as any} filteredUsers={[]} />);
      
      expect(screen.getByTestId('no-users-found')).toBeInTheDocument();
    });
  });
}); 