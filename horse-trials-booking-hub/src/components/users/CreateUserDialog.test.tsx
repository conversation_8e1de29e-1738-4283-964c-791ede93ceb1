import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import CreateUserDialog from './CreateUserDialog';

const mockMutate = vi.fn();

// Mock the useCreateUser hook
vi.mock('@/hooks/useUserRoles', () => ({
  useCreateUser: vi.fn(() => ({
    mutate: mockMutate,
    isPending: false
  }))
}));

// Mock the useToast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(() => ({
    toast: vi.fn()
  }))
}));

describe('CreateUserDialog', () => {
  const defaultProps = {
    isOpen: false,
    onOpenChange: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Dialog Visibility', () => {
    it('renders create user button when not open', () => {
      render(<CreateUserDialog {...defaultProps} isOpen={false} />);
      
      expect(screen.getByTestId('create-user-btn')).toBeInTheDocument();
      expect(screen.getByText('Create User')).toBeInTheDocument();
    });

    it('opens dialog when create user button is clicked', async () => {
      render(<CreateUserDialog {...defaultProps} isOpen={false} />);
      
      const user = userEvent.setup();
      const createButton = screen.getByTestId('create-user-btn');
      await user.click(createButton);
      
      // The dialog should open when the button is clicked
      expect(createButton).toBeInTheDocument();
    });
  });

  describe('Form Fields', () => {
    beforeEach(() => {
      render(<CreateUserDialog {...defaultProps} isOpen={true} />);
    });

    it('renders all required form fields', () => {
      expect(screen.getByTestId('create-user-email-input')).toBeInTheDocument();
      expect(screen.getByTestId('create-user-name-input')).toBeInTheDocument();
      expect(screen.getByTestId('create-user-phone-input')).toBeInTheDocument();
      expect(screen.getByTestId('create-user-role-select')).toBeInTheDocument();
    });

    it('has correct input types and placeholders', () => {
      const emailInput = screen.getByTestId('create-user-email-input');
      const nameInput = screen.getByTestId('create-user-name-input');
      const phoneInput = screen.getByTestId('create-user-phone-input');

      expect(emailInput).toHaveAttribute('type', 'email');
      expect(emailInput).toHaveAttribute('placeholder', '<EMAIL>');
      expect(nameInput).toHaveAttribute('placeholder', 'John Doe');
      expect(phoneInput).toHaveAttribute('type', 'tel');
      expect(phoneInput).toHaveAttribute('placeholder', '(*************');
    });

    it('has required attributes on mandatory fields', () => {
      const emailInput = screen.getByTestId('create-user-email-input');
      const nameInput = screen.getByTestId('create-user-name-input');

      expect(emailInput).toHaveAttribute('required');
      expect(nameInput).toHaveAttribute('required');
    });
  });

  describe('Form Interaction', () => {
    beforeEach(() => {
      render(<CreateUserDialog {...defaultProps} isOpen={true} />);
    });

    it('updates form values when user types', async () => {
      const user = userEvent.setup();
      const emailInput = screen.getByTestId('create-user-email-input');
      const nameInput = screen.getByTestId('create-user-name-input');
      const phoneInput = screen.getByTestId('create-user-phone-input');

      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      await user.clear(nameInput);
      await user.type(nameInput, 'Test User');
      await user.clear(phoneInput);
      await user.type(phoneInput, '************');

      expect(emailInput).toHaveValue('<EMAIL>');
      expect(nameInput).toHaveValue('Test User');
      expect(phoneInput).toHaveValue('************');
    });

    it('calls onOpenChange when cancel button is clicked', async () => {
      const user = userEvent.setup();
      await user.click(screen.getByTestId('create-user-cancel-btn'));
      expect(defaultProps.onOpenChange).toHaveBeenCalledWith(false);
    });
  });

  describe('Form Submission', () => {
    beforeEach(() => {
      render(<CreateUserDialog {...defaultProps} isOpen={true} />);
    });

    it('shows submit button with correct text', () => {
      expect(screen.getByTestId('create-user-submit-btn')).toBeInTheDocument();
      expect(screen.getByTestId('create-user-submit-btn')).toHaveTextContent('Create User');
    });

    it('does not submit when required fields are empty', async () => {
      const user = userEvent.setup();
      const submitButton = screen.getByTestId('create-user-submit-btn');
      
      await user.click(submitButton);
      
      // Should show validation error via toast
      expect(submitButton).toBeInTheDocument();
    });

    it('submits form with all data when required fields are filled', async () => {
      const user = userEvent.setup();
      const emailInput = screen.getByTestId('create-user-email-input');
      const nameInput = screen.getByTestId('create-user-name-input');
      const phoneInput = screen.getByTestId('create-user-phone-input');
      const submitButton = screen.getByTestId('create-user-submit-btn');

      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      await user.clear(nameInput);
      await user.type(nameInput, 'Test User');
      await user.clear(phoneInput);
      await user.type(phoneInput, '************');
      
      await user.click(submitButton);
      
      expect(mockMutate).toHaveBeenCalledWith({
        email: '<EMAIL>',
        full_name: 'Test User',
        phone: '************',
        role: 'user'
      }, expect.any(Object));
    });
  });

  describe('Layout and Styling', () => {
    it('renders with correct dialog structure', () => {
      render(<CreateUserDialog {...defaultProps} isOpen={true} />);
      
      expect(screen.getByTestId('create-user-dialog-content')).toBeInTheDocument();
      expect(screen.getByTestId('create-user-dialog-title')).toBeInTheDocument();
    });

    it('renders note about temporary password', () => {
      render(<CreateUserDialog {...defaultProps} isOpen={true} />);
      
      expect(screen.getByText(/temporary password will be generated automatically/)).toBeInTheDocument();
      expect(screen.getByText(/password reset email/)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      render(<CreateUserDialog {...defaultProps} isOpen={true} />);
    });

    it('has proper form labels', () => {
      expect(screen.getByText('Email *')).toBeInTheDocument();
      expect(screen.getByText('Full Name *')).toBeInTheDocument();
      expect(screen.getByText('Initial Role')).toBeInTheDocument();
    });

    it('has proper button labels', () => {
      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByTestId('create-user-submit-btn')).toHaveTextContent('Create User');
    });
  });
}); 