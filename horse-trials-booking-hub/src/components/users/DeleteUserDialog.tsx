import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { useDeleteUser } from '@/hooks/useAllUsers';
import { useToast } from '@/hooks/use-toast';
import { ComprehensiveUser } from '@/hooks/useAllUsers';

interface DeleteUserDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  userToDelete: ComprehensiveUser | null;
  onDeleteSuccess: () => void;
}

const DeleteUserDialog: React.FC<DeleteUserDialogProps> = ({
  isOpen,
  onOpenChange,
  userToDelete,
  onDeleteSuccess,
}) => {
  const { toast } = useToast();
  const deleteUser = useDeleteUser();

  const confirmDeleteUser = () => {
    if (!userToDelete) return;

    deleteUser.mutate(userToDelete.auth_user_id, {
      onSuccess: () => {
        toast({
          title: 'User deleted',
          description: `${userToDelete.full_name} has been permanently deleted.`,
          variant: 'default',
        });
        onDeleteSuccess();
      },
      onError: (error) => {
        toast({
          title: 'Delete failed',
          description: 'Failed to delete user. Please try again.',
          variant: 'destructive',
        });
        console.error('Delete user error:', error);
      }
    });
  };

  if (!userToDelete) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange} data-testid="delete-user-dialog">
      <DialogContent data-testid="delete-user-dialog-content">
        <DialogHeader>
          <DialogTitle data-testid="delete-user-dialog-title">Confirm Delete</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4" data-testid="delete-user-warning">
            <p className="text-sm text-red-800 font-medium">
              Are you sure you want to delete <strong>{userToDelete.full_name}</strong>?
            </p>
            <p className="text-sm text-red-700 mt-1">
              Email: {userToDelete.auth_email}
            </p>
            <p className="text-sm text-red-700 mt-2">
              This action will permanently delete the user from:
            </p>
            <ul className="text-sm text-red-700 mt-1 ml-4 list-disc">
              <li>Authentication system</li>
              <li>User profile</li>
              <li>User roles</li>
              <li>All associated data</li>
            </ul>
            <p className="text-sm text-red-800 font-medium mt-2">
              This action cannot be undone.
            </p>
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={confirmDeleteUser}
              disabled={deleteUser.isPending}
              className="bg-red-600 hover:bg-red-700"
              data-testid="confirm-delete-btn"
            >
              {deleteUser.isPending ? 'Deleting...' : 'Delete User'}
            </Button>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={deleteUser.isPending}
              data-testid="cancel-delete-btn"
            >
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteUserDialog; 