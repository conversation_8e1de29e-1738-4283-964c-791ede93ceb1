import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { User, Search } from 'lucide-react';
import { ComprehensiveUser } from '@/hooks/useAllUsers';
import UserCard from './UserCard';

interface UserListProps {
  users: ComprehensiveUser[] | undefined;
  filteredUsers: ComprehensiveUser[];
  isLoading: boolean;
  onDeleteUser: (user: ComprehensiveUser) => void;
}

const UserList: React.FC<UserListProps> = ({
  users,
  filteredUsers,
  isLoading,
  onDeleteUser,
}) => {
  return (
    <Card data-testid="users-list-card">
      <CardHeader>
        <CardTitle className="text-green-800 flex items-center" data-testid="card-title">
          <User className="h-5 w-5 mr-2" />
          Users ({filteredUsers.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8" data-testid="loading-users">Loading users...</div>
        ) : !users || users.length === 0 ? (
          <div className="text-center py-12" data-testid="no-users-found">
            <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
            <p className="text-gray-600">No user profiles have been created yet.</p>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="text-center py-12" data-testid="no-matching-users">
            <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No matching users</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
          </div>
        ) : (
          <div className="space-y-4" data-testid="users-list">
            {filteredUsers.map((user) => (
              <UserCard
                key={user.auth_user_id}
                user={user}
                onDelete={onDeleteUser}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UserList; 