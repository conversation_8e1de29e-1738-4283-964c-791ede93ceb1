import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { EventManagement } from './EventManagement';
import { useEvents, useCreateEvent, useUpdateEvent, useDeleteEvent } from '@/hooks/useEvents';
import { useLocations } from '@/hooks/useLocations';
import { useBookings } from '@/hooks/useBookings';
import { useLevels } from '@/hooks/useLevels';
import { useEventLevels } from '@/hooks/useLevels';
import { useToast } from '@/hooks/use-toast';
import type { ReactNode } from 'react';

// Mock dependencies
vi.mock('@/hooks/useEvents');
vi.mock('@/hooks/useCreateEvent');
vi.mock('@/hooks/useUpdateEvent');
vi.mock('@/hooks/useDeleteEvent');
vi.mock('@/hooks/useLocations');
vi.mock('@/hooks/useBookings');
vi.mock('@/hooks/useLevels');
vi.mock('@/hooks/useEventLevels');
vi.mock('@/hooks/use-toast');

// Mock the Select component to avoid pointer-events issues
vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange, 'data-testid': testId }) => {
    // Extract options from children
    const options = [];
    const processChildren = (children) => {
      if (Array.isArray(children)) {
        children.forEach(child => processChildren(child));
      } else if (children?.type?.name === 'SelectContent') {
        processChildren(children.props.children);
      } else if (children?.type?.name === 'SelectItem') {
        options.push({
          value: children.props.value,
          label: children.props.children
        });
      }
    };
    processChildren(children);

    return (
      <select 
        value={value} 
        onChange={(e) => onValueChange(e.target.value)}
        data-testid={testId}
      >
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    );
  },
  SelectContent: ({ children }) => children,
  SelectItem: ({ children, value }) => ({ type: { name: 'SelectItem' }, props: { children, value } }),
  SelectTrigger: ({ children }) => children,
  SelectValue: ({ placeholder }) => placeholder,
}));

const mockUseEvents = vi.mocked(useEvents);
const mockUseCreateEvent = vi.mocked(useCreateEvent);
const mockUseUpdateEvent = vi.mocked(useUpdateEvent);
const mockUseDeleteEvent = vi.mocked(useDeleteEvent);
const mockUseLocations = vi.mocked(useLocations);
const mockUseBookings = vi.mocked(useBookings);
const mockUseLevels = vi.mocked(useLevels);
const mockUseEventLevels = vi.mocked(useEventLevels);
const mockUseToast = vi.mocked(useToast);

describe('EventManagement Component', () => {
  let queryClient: QueryClient;
  const mockToast = vi.fn();
  const mockOnEventSelect = vi.fn();

  const mockEvent = {
    id: 'event1',
    name: 'Spring Horse Trials 2024',
    start_date: '2024-05-01',
    end_date: '2024-05-03',
    event_type: 'horse_trials',
    is_active: true
  };

  const mockEvents = [
    mockEvent,
    {
      id: 'event2',
      name: 'Summer Dressage Show',
      start_date: '2024-06-15',
      end_date: '2024-06-16',
      event_type: 'dressage',
      is_active: false
    }
  ];

  const mockLocations = [
    { id: 'loc1', name: 'Main Arena' },
    { id: 'loc2', name: 'Warm-up Ring' }
  ];

  const mockBookings = [
    { id: 'booking1', rider_name: 'John Doe' },
    { id: 'booking2', rider_name: 'Jane Smith' }
  ];

  const mockLevels = [
    { id: 'level1', name: 'Beginner', discipline: 'eventing' },
    { id: 'level2', name: 'Intermediate', discipline: 'eventing' },
    { id: 'level3', name: 'Advanced', discipline: 'dressage' }
  ];

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    
    vi.clearAllMocks();
    mockUseToast.mockReturnValue({ toast: mockToast });

    // Default mock implementations
    mockUseEvents.mockReturnValue({
      data: mockEvents,
      isLoading: false,
      error: null,
    } as any);

    mockUseCreateEvent.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    } as any);

    mockUseUpdateEvent.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    } as any);

    mockUseDeleteEvent.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    } as any);

    mockUseLocations.mockReturnValue({
      data: mockLocations,
      isLoading: false,
      error: null,
    } as any);

    mockUseBookings.mockReturnValue({
      data: mockBookings,
      isLoading: false,
      error: null,
    } as any);

    mockUseLevels.mockReturnValue({
      data: mockLevels,
      isLoading: false,
      error: null,
    } as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const wrapper = ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );

  const defaultProps = {
    onEventSelect: mockOnEventSelect
  };

  describe('Rendering', () => {
    it('renders component with correct title', () => {
      render(<EventManagement {...defaultProps} />, { wrapper });

      expect(screen.getByTestId('events-title')).toHaveTextContent('Events');
      expect(screen.getByTestId('create-event-button')).toBeInTheDocument();
    });

    it('displays events when data is loaded', () => {
      render(<EventManagement {...defaultProps} />, { wrapper });

      expect(screen.getByTestId('event-card-event1')).toBeInTheDocument();
      expect(screen.getByTestId('event-card-event2')).toBeInTheDocument();
      expect(screen.getByTestId('event-name-event1')).toHaveTextContent('Spring Horse Trials 2024');
      expect(screen.getByTestId('event-name-event2')).toHaveTextContent('Summer Dressage Show');
    });

    it('shows loading state', () => {
      mockUseEvents.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      expect(screen.getByText('Loading events...')).toBeInTheDocument();
    });

    it('shows empty state when no events', () => {
      mockUseEvents.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      expect(screen.getByTestId('empty-events-state')).toBeInTheDocument();
      expect(screen.getByTestId('empty-events-title')).toHaveTextContent('No events yet');
      expect(screen.getByTestId('empty-events-message')).toHaveTextContent('Create your first event to get started.');
    });
  });

  describe('Event Display', () => {
    it('displays event information correctly', () => {
      render(<EventManagement {...defaultProps} />, { wrapper });

      expect(screen.getByTestId('event-name-event1')).toHaveTextContent('Spring Horse Trials 2024');
      expect(screen.getByTestId('event-type-badge-event1')).toHaveTextContent('Horse Trials');
      expect(screen.getByTestId('event-status-event1')).toHaveTextContent('Active');
      expect(screen.getByTestId('event-dates-event1')).toHaveTextContent('5/1/2024 - 5/3/2024');
    });

    it('displays event statistics correctly', () => {
      render(<EventManagement {...defaultProps} />, { wrapper });

      expect(screen.getByTestId('event-locations-count-event1')).toHaveTextContent('2');
      expect(screen.getByTestId('event-bookings-count-event1')).toHaveTextContent('2');
    });

    it('displays inactive event status correctly', () => {
      render(<EventManagement {...defaultProps} />, { wrapper });

      expect(screen.getByTestId('event-status-event2')).toHaveTextContent('Inactive');
    });
  });

  describe('Create Event Form', () => {
    it('shows create form when Create Event button is clicked', async () => {
      const user = userEvent.setup();
      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('create-event-button'));

      expect(screen.getByTestId('create-event-form')).toBeInTheDocument();
      expect(screen.getByTestId('event-name-input')).toBeInTheDocument();
      expect(screen.getByTestId('event-type-select')).toBeInTheDocument();
      expect(screen.getByTestId('start-date-input')).toBeInTheDocument();
      expect(screen.getByTestId('end-date-input')).toBeInTheDocument();
    });

    it('creates event successfully', async () => {
      const user = userEvent.setup();
      const mockMutate = vi.fn();
      mockUseCreateEvent.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('create-event-button'));

      await user.type(screen.getByTestId('event-name-input'), 'New Event');
      await user.type(screen.getByTestId('start-date-input'), '2024-07-01');
      await user.type(screen.getByTestId('end-date-input'), '2024-07-03');

      await user.click(screen.getByTestId('create-event-submit-button'));

      expect(mockMutate).toHaveBeenCalledWith({
        event: {
          name: 'New Event',
          start_date: '2024-07-01',
          end_date: '2024-07-03',
          event_type: 'horse_trials'
        },
        levelIds: []
      }, expect.any(Object));
    });

    it('disables create button when required fields are missing', async () => {
      const user = userEvent.setup();
      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('create-event-button'));

      const createButton = screen.getByTestId('create-event-submit-button');
      expect(createButton).toBeDisabled();
    });

    it('cancels create form when Cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('create-event-button'));
      expect(screen.getByTestId('create-event-form')).toBeInTheDocument();

      await user.click(screen.getByTestId('cancel-create-event-button'));
      expect(screen.queryByTestId('create-event-form')).not.toBeInTheDocument();
    });
  });

  describe('Edit Event', () => {
    it('shows edit form when Edit button is clicked', async () => {
      const user = userEvent.setup();
      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('edit-event-event1'));

      expect(screen.getByTestId('edit-event-form-event1')).toBeInTheDocument();
      expect(screen.getByTestId('edit-event-name-event1')).toHaveValue('Spring Horse Trials 2024');
    });

    it('updates event name successfully', async () => {
      const user = userEvent.setup();
      const mockMutate = vi.fn();
      mockUseUpdateEvent.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
      } as any);

      // Use a stable reference for the event object
      const stableMockEvent = { ...mockEvent };
      mockUseEvents.mockReturnValue({
        data: [ stableMockEvent ],
        isLoading: false,
        error: null,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      // Debug: Check if edit button exists
      const editButton = screen.getByTestId('edit-event-event1');
      expect(editButton).toBeInTheDocument();

      await user.click(editButton);

      // Debug: Check if edit form is rendered
      await waitFor(() => {
        expect(screen.getByTestId('edit-event-form-event1')).toBeInTheDocument();
      });

      // Use fireEvent.change for the name input
      const nameInput = screen.getByTestId('edit-event-name-event1');
      fireEvent.change(nameInput, { target: { value: 'Updated Event Name' } });

      // Wait for the input value to be updated
      await waitFor(() => {
        expect(nameInput).toHaveValue('Updated Event Name');
      });

      await user.click(screen.getByTestId('update-event-event1'));

      // Verify the mutation was called with the updated name
      expect(mockMutate).toHaveBeenCalledWith({
        id: stableMockEvent.id,
        name: 'Updated Event Name',
        start_date: stableMockEvent.start_date, // Keep original dates
        end_date: stableMockEvent.end_date
      }, expect.any(Object));
    });

    it('cancels edit mode', async () => {
      const user = userEvent.setup();
      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('edit-event-event1'));
      expect(screen.getByTestId('edit-event-form-event1')).toBeInTheDocument();

      await user.click(screen.getByTestId('cancel-edit-event-event1'));
      expect(screen.queryByTestId('edit-event-form-event1')).not.toBeInTheDocument();
    });
  });

  describe('Delete Event', () => {
    it('shows delete confirmation dialog', async () => {
      const user = userEvent.setup();
      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('delete-event-event1'));

      await waitFor(() => {
        expect(screen.getByTestId('delete-event-dialog')).toBeInTheDocument();
        expect(screen.getByTestId('delete-event-dialog-title')).toHaveTextContent('Delete Event');
      });
    });

    it('deletes event when confirmed', async () => {
      const user = userEvent.setup();
      const mockMutate = vi.fn();
      mockUseDeleteEvent.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('delete-event-event1'));
      await user.click(screen.getByTestId('confirm-delete-event-button'));

      expect(mockMutate).toHaveBeenCalledWith('event1', expect.any(Object));
    });

    it('cancels delete when Cancel is clicked', async () => {
      const user = userEvent.setup();
      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('delete-event-event1'));

      await waitFor(() => {
        expect(screen.getByTestId('delete-event-dialog')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('cancel-delete-event-button'));

      await waitFor(() => {
        expect(screen.queryByTestId('delete-event-dialog')).not.toBeInTheDocument();
      });
    });
  });

  describe('Event Actions', () => {
    it('calls onEventSelect when Manage Event Details is clicked', async () => {
      const user = userEvent.setup();
      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('manage-event-event1'));

      expect(mockOnEventSelect).toHaveBeenCalledWith('event1');
    });

    it('shows configure levels and tests view', async () => {
      const user = userEvent.setup();
      
      // Mock useEventLevels for the configure view
      mockUseEventLevels.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('configure-event-event1'));

      expect(screen.getByText('Configure Spring Horse Trials 2024')).toBeInTheDocument();
      expect(screen.getByText('Event Levels')).toBeInTheDocument();
      expect(screen.getByText('Dressage Tests')).toBeInTheDocument();
    });

    it('returns to events list from configure view', async () => {
      const user = userEvent.setup();
      
      // Mock useEventLevels for the configure view
      mockUseEventLevels.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('configure-event-event1'));
      await user.click(screen.getByText('← Back to Events'));

      expect(screen.getByTestId('events-title')).toBeInTheDocument();
    });
  });

  describe('Event Type Handling', () => {
    it('displays correct event type labels', () => {
      render(<EventManagement {...defaultProps} />, { wrapper });

      expect(screen.getByTestId('event-type-badge-event1')).toHaveTextContent('Horse Trials');
      expect(screen.getByTestId('event-type-badge-event2')).toHaveTextContent('Dressage');
    });

    it('allows selecting different event types in create form', async () => {
      const user = userEvent.setup();
      render(<EventManagement {...defaultProps} />, { wrapper });

      // Click Create Event button to show the form
      await user.click(screen.getByTestId('create-event-button'));
      
      // Use user.selectOptions for the HTML select element
      await user.selectOptions(screen.getByTestId('event-type-select'), 'dressage');
      
      // Verify the selection was made
      expect(screen.getByTestId('event-type-select')).toHaveValue('dressage');
    });
  });

  describe('Loading States', () => {
    it('shows loading state during event creation', async () => {
      const user = userEvent.setup();
      mockUseCreateEvent.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('create-event-button'));
      await user.type(screen.getByTestId('event-name-input'), 'Test Event');

      expect(screen.getByTestId('create-event-submit-button')).toBeDisabled();
      expect(screen.getByTestId('create-event-submit-button')).toHaveTextContent('Create Event');
    });

    it('shows loading state during event update', async () => {
      const user = userEvent.setup();
      mockUseUpdateEvent.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('edit-event-event1'));

      expect(screen.getByTestId('update-event-event1')).toBeDisabled();
      expect(screen.getByTestId('update-event-event1')).toHaveTextContent('Updating...');
    });

    it('shows loading state during event deletion', async () => {
      const user = userEvent.setup();
      mockUseDeleteEvent.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      render(<EventManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('delete-event-event1'));

      await waitFor(() => {
        expect(screen.getByTestId('confirm-delete-event-button')).toBeDisabled();
        expect(screen.getByTestId('confirm-delete-event-button')).toHaveTextContent('Deleting...');
      });
    });
  });
}); 