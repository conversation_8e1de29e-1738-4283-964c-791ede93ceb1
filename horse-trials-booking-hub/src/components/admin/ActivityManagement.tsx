import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit, Trash, Clock, Target, AlertCircle, Loader2 } from 'lucide-react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useActivities, useCreateActivity, useUpdateActivity, useDeleteActivity } from '@/hooks/useActivities';
import { useEventLevels } from '@/hooks/useLevels';
import { DEFAULT_DRESSAGE_SLOT_DURATION_MINUTES, DEFAULT_SHOW_JUMPING_SLOT_DURATION_MINUTES, DEFAULT_SLOT_DURATION_MINUTES } from '@/constants/app-defaults';
import { useToast } from '@/hooks/use-toast';
import { formatDateForDisplay } from '@/utils/dateUtils';

interface ActivityManagementProps {
  locationId: string;
  locationName: string;
  eventId?: string;
  initialSelectedDate?: string;
  locationActivityType: string;
}

export const ActivityManagement: React.FC<ActivityManagementProps> = ({ 
  locationId, 
  locationName,
  eventId,
  initialSelectedDate,
  locationActivityType
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [newActivityDate, setNewActivityDate] = useState(
    initialSelectedDate || new Date().toISOString().split('T')[0]
  );
  const [editActivityDate, setEditActivityDate] = useState("");
  const [newActivity, setNewActivity] = useState({
    location_id: locationId,
    activity_type: locationActivityType,
    start_time: '09:00',
    end_time: '16:30',
    level: '',
    description: ''
  });
  const [editActivity, setEditActivity] = useState<Partial<Activity> & { id: string }>({
    id: '',
    location_id: locationId,
    activity_type: locationActivityType,
    start_time: '',
    end_time: '',
    level: '',
    description: ''
  });
  const [slotDuration, setSlotDuration] = useState(() => {
    if (selectedActivity?.slot_duration_minutes) {
      return selectedActivity.slot_duration_minutes;
    }
    
    // Set default based on activity type
    switch (locationActivityType) {
      case 'show_jumping':
        return DEFAULT_SHOW_JUMPING_SLOT_DURATION_MINUTES;
      case 'dressage':
        return DEFAULT_DRESSAGE_SLOT_DURATION_MINUTES;
      case 'cross_country':
        return DEFAULT_SLOT_DURATION_MINUTES; // Use default for cross country
      default:
        return DEFAULT_SLOT_DURATION_MINUTES;
    }
  });
  const [editingActivityId, setEditingActivityId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const { data: activities, isLoading, error } = useActivities(locationId);
  const { data: eventLevels } = useEventLevels(eventId);
  const createActivity = useCreateActivity();
  const updateActivity = useUpdateActivity();
  const deleteActivity = useDeleteActivity();

  // Filter activities by selected date if provided
  const filteredActivities = initialSelectedDate 
    ? activities?.filter(activity => activity.start_time.split('T')[0] === initialSelectedDate) || []
    : activities || [];

  // Debug logging
  console.log('ActivityManagement - initialSelectedDate:', initialSelectedDate);
  console.log('ActivityManagement - activities count:', activities?.length);
  console.log('ActivityManagement - filteredActivities count:', filteredActivities?.length);

  // Get show jumping levels from event levels (for eventing competition)
  const showJumpingLevels = eventLevels?.filter(level => {
    const discipline = level.levels?.discipline;
    const name = level.levels?.name;
    
    // For show jumping in eventing, we only want eventing levels
    return (discipline?.toLowerCase() === 'eventing' && 
           name && 
           name.trim() !== '');
  }).map(level => level.levels?.name).filter(Boolean) || [];

  // Get activity type label
  const getActivityTypeLabel = (type: string) => {
    switch (type) {
      case 'dressage':
        return 'Dressage';
      case 'show_jumping':
        return 'Show Jumping';
      case 'cross_country':
        return 'Cross Country';
      default:
        return type;
    }
  };

  // Effect to update newActivityDate if initialSelectedDate prop changes,
  // but only if the create form is not currently shown or if we want to reset it
  // when the prop changes (e.g., user picks a new date in a parent component).
  useEffect(() => {
    if (initialSelectedDate) {
      // If the form is not open, or if you always want to reflect the prop change:
      setNewActivityDate(initialSelectedDate);
    }
  }, [initialSelectedDate]);

  const handleCreateActivity = () => {
    const activityData = {
      ...newActivity,
      start_time: `${newActivityDate}T${newActivity.start_time}:00`,
      end_time: `${newActivityDate}T${newActivity.end_time}:00`,
      level: locationActivityType === 'show_jumping' ? newActivity.level : undefined,
      description: newActivity.description || undefined,
      slot_duration_minutes: slotDuration,
      activity_type: locationActivityType
    };
    
    createActivity.mutate(activityData, {
      onSuccess: () => {
        setShowCreateForm(false);
        setNewActivity({
          location_id: locationId,
          activity_type: locationActivityType,
          start_time: '09:00',
          end_time: '16:30',
          level: '',
          description: ''
        });
      }
    });
  };

  const handleEditActivity = () => {
    console.log('Updating activity with slot duration:', slotDuration);
    
    const activityData = {
      ...editActivity,
      start_time: `${editActivityDate}T${editActivity.start_time}:00`,
      end_time: `${editActivityDate}T${editActivity.end_time}:00`,
      level: locationActivityType === 'show_jumping' ? editActivity.level : undefined,
      description: editActivity.description || undefined,
      slot_duration_minutes: slotDuration,
      activity_type: locationActivityType
    };
    
    console.log('Activity data being sent to update:', activityData);
    
    updateActivity.mutate(activityData, {
      onSuccess: () => {
        setEditingActivityId(null);
        setSelectedActivity(null);
      }
    });
  };

  const startEditActivity = (activity: Activity) => {
    console.log('Editing activity with slot duration:', activity.slot_duration_minutes);
    
    const startTime = activity.start_time.split('T')[1].substring(0, 5);
    const endTime = activity.end_time.split('T')[1].substring(0, 5);
    const date = activity.start_time.split('T')[0];
    
    setEditActivity({
      id: activity.id,
      location_id: activity.location_id,
      activity_type: activity.activity_type,
      start_time: startTime,
      end_time: endTime,
      level: activity.level || '',
      description: activity.description || '',
      slot_duration_minutes: activity.slot_duration_minutes
    });
    
    setSelectedActivity(activity);
    setEditActivityDate(date);
    setSlotDuration(activity.slot_duration_minutes || 
      (activity.activity_type === 'dressage' ? DEFAULT_DRESSAGE_SLOT_DURATION_MINUTES : 
       activity.activity_type === 'show_jumping' ? DEFAULT_SHOW_JUMPING_SLOT_DURATION_MINUTES : 
       DEFAULT_SLOT_DURATION_MINUTES));
    setEditingActivityId(activity.id);
  };

  const confirmDeleteActivity = (activity: Activity) => {
    setSelectedActivity(activity);
    setDeleteDialogOpen(true);
  };

  const handleDeleteActivity = () => {
    if (selectedActivity) {
      deleteActivity.mutate(selectedActivity.id, {
        onSuccess: () => {
          setDeleteDialogOpen(false);
          setSelectedActivity(null);
        }
      });
    }
  };

  const formatDate = (dateString: string) => {
    return formatDateForDisplay(dateString);
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (error) {
    console.error('Error loading activities:', error);
    return (
      <Card className="border-red-200">
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertCircle className="w-8 h-8 mx-auto mb-2" />
            <p>Error loading activities. Please try again.</p>
            <pre className="mt-2 text-xs text-left bg-red-50 p-2 rounded overflow-auto">
              {error.message}
            </pre>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading activities...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-semibold text-green-800">
          {getActivityTypeLabel(locationActivityType)} Activities - {locationName}
          {initialSelectedDate && (
            <Badge variant="outline" className="ml-2 text-sm">
              {formatDateForDisplay(initialSelectedDate)}
            </Badge>
          )}
        </h4>
        <Button 
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="bg-green-600 hover:bg-green-700"
          size="sm"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Activity
        </Button>
      </div>

      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>New {getActivityTypeLabel(locationActivityType)} Activity</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="activity-date">Date</Label>
                <Input
                  id="activity-date"
                  type="date"
                  value={newActivityDate}
                  onChange={(e) => setNewActivityDate(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="start-time">Start Time</Label>
                <Input
                  id="start-time"
                  type="time"
                  value={newActivity.start_time}
                  onChange={(e) => setNewActivity({ ...newActivity, start_time: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="end-time">End Time</Label>
                <Input
                  id="end-time"
                  type="time"
                  value={newActivity.end_time}
                  onChange={(e) => setNewActivity({ ...newActivity, end_time: e.target.value })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="slot-duration">Slot Duration (minutes)</Label>
                <Input
                  id="slot-duration"
                  type="number"
                  value={slotDuration}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value) && value > 0) {
                      setSlotDuration(value);
                    }
                  }}
                />
              </div>

              {locationActivityType === 'show_jumping' && (
                <div>
                  <Label htmlFor="level">Level</Label>
                  <Select
                    value={newActivity.level}
                    onValueChange={(value) => setNewActivity({ ...newActivity, level: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      {showJumpingLevels.map(level => (
                        <SelectItem key={level} value={level}>{level}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="description">Description (optional)</Label>
              <Textarea
                id="description"
                value={newActivity.description}
                onChange={(e) => setNewActivity({ ...newActivity, description: e.target.value })}
                placeholder="Add any additional details..."
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button 
                variant="outline" 
                onClick={() => setShowCreateForm(false)}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleCreateActivity}
                disabled={!locationActivityType || (locationActivityType === 'show_jumping' && !newActivity.level) || createActivity.isPending}
                className="bg-green-600 hover:bg-green-700"
              >
                {createActivity.isPending ? 'Creating...' : 'Create Activity'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4">
        {filteredActivities?.map((activity) => {
          const isEditing = editingActivityId === activity.id;
          
          return (
            <Card key={activity.id} className={`border-green-200 mb-4 ${isEditing ? 'ring-2 ring-amber-300' : ''}`}>
              <CardHeader>
                {!isEditing ? (
                  // Display mode
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-green-800">{getActivityTypeLabel(activity.activity_type)}</h4>
                        {activity.level && (
                          <Badge variant="outline">{activity.level}</Badge>
                        )}
                        <Badge variant="secondary">
                          {activity.slot_duration_minutes || 6} min slots
                        </Badge>
                      </div>
                      <div className="flex items-center mt-2 text-sm text-gray-600">
                        <Clock className="w-4 h-4 mr-2" />
                        {formatDateForDisplay(activity.start_time)} {formatTime(activity.start_time)} - {formatTime(activity.end_time)}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => startEditActivity(activity)}
                        className="text-amber-600 border-amber-300 hover:bg-amber-50"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        Edit
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => confirmDeleteActivity(activity)}
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <Trash className="w-3 h-3 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Edit mode - inline form
                  <div className="w-full space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor={`slot-duration-${activity.id}`}>Slot Duration (minutes)</Label>
                        <Input
                          id={`slot-duration-${activity.id}`}
                          type="number"
                          value={slotDuration}
                          onChange={(e) => setSlotDuration(parseInt(e.target.value))}
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor={`activity-date-${activity.id}`}>Date</Label>
                        <Input
                          id={`activity-date-${activity.id}`}
                          type="date"
                          value={editActivityDate}
                          onChange={(e) => setEditActivityDate(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor={`start-time-${activity.id}`}>Start Time</Label>
                        <Input
                          id={`start-time-${activity.id}`}
                          type="time"
                          value={editActivity.start_time}
                          onChange={(e) => setEditActivity({...editActivity, start_time: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor={`end-time-${activity.id}`}>End Time</Label>
                        <Input
                          id={`end-time-${activity.id}`}
                          type="time"
                          value={editActivity.end_time}
                          onChange={(e) => setEditActivity({...editActivity, end_time: e.target.value})}
                        />
                      </div>
                    </div>
                    
                    {locationActivityType === 'show_jumping' && (
                      <div>
                        <Label htmlFor={`level-${activity.id}`}>Level</Label>
                        <Select
                          value={editActivity.level}
                          onValueChange={(value) => setEditActivity({...editActivity, level: value})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a level" />
                          </SelectTrigger>
                          <SelectContent>
                            {showJumpingLevels.length > 0 ? (
                              showJumpingLevels
                                .filter(level => level && level.trim() !== '')
                                .map((level, index) => (
                                  <SelectItem key={`${level}-${index}`} value={level}>
                                    {level}
                                  </SelectItem>
                                ))
                            ) : (
                              <SelectItem value="no-levels" disabled>
                                No show jumping levels available for this event
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                        {showJumpingLevels.length === 0 && (
                          <p className="text-sm text-amber-600 mt-1">
                            Please add show jumping levels to this event first.
                          </p>
                        )}
                      </div>
                    )}
                    
                    <div>
                      <Label htmlFor={`description-${activity.id}`}>Description (optional)</Label>
                      <Input
                        id={`description-${activity.id}`}
                        value={editActivity.description}
                        onChange={(e) => setEditActivity({...editActivity, description: e.target.value})}
                        placeholder="Activity description"
                      />
                    </div>
                    
                    <div className="flex space-x-2 pt-2">
                      <Button
                        onClick={handleEditActivity}
                        disabled={updateActivity.isPending}
                        className="bg-gold text-primary hover:bg-gold-hover"
                      >
                        {updateActivity.isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                        Update Activity
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => {
                          setEditingActivityId(null);
                          setSelectedActivity(null);
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </CardHeader>
              {activity.description && !isEditing && (
                <CardContent>
                  <p className="text-sm text-gray-600">{activity.description}</p>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      {(!filteredActivities || filteredActivities.length === 0) && (
        <Card className="bg-gray-50 border-gray-200">
          <CardContent className="text-center py-12">
            <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No activities yet</h3>
            <p className="text-gray-600 mb-4">
              {initialSelectedDate 
                ? `No activities found for ${formatDateForDisplay(initialSelectedDate)}. Create your first activity for this date.`
                : 'Create your first activity for this location.'
              }
            </p>
          </CardContent>
        </Card>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Activity</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this activity? This will also delete all associated time slots. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteActivity}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};