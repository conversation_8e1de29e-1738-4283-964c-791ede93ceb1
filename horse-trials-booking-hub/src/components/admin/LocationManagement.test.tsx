import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { LocationManagement } from './LocationManagement';
import { useLocations, useCreateLocation, useUpdateLocation, useDeleteLocation } from '@/hooks/useLocations';
import { useEvent } from '@/hooks/useEvents';
import { useEventLevels } from '@/hooks/useLevels';
import { useToast } from '@/hooks/use-toast';
import type { ReactNode } from 'react';

// Mock dependencies
vi.mock('@/hooks/useLocations');
vi.mock('@/hooks/useEvents');
vi.mock('@/hooks/useLevels');
vi.mock('@/hooks/use-toast');
vi.mock('@/integrations/supabase/client');

// Mock child components to prevent them from using unmocked hooks
vi.mock('./ActivityManagement', () => ({
  ActivityManagement: ({ locationId, locationName, eventId, initialSelectedDate, locationActivityType }) => (
    <div data-testid={`activity-management-${locationId}`}>
      Activity Management for {locationName}
    </div>
  ),
}));

vi.mock('./LocationScheduleView', () => ({
  LocationScheduleView: ({ locationId, locationName, selectedDate }) => (
    <div data-testid={`location-schedule-view-${locationId}`}>
      Schedule View for {locationName}
    </div>
  ),
}));

vi.mock('./TimeSlotManagement', () => ({
  TimeSlotManagement: ({ locationId, locationName, selectedDate, eventId }) => (
    <div data-testid={`time-slot-management-${locationId}`}>
      Time Slot Management for {locationName}
    </div>
  ),
}));

// Mock the Select component to avoid pointer-events issues
vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange, 'data-testid': testId }) => {
    // Extract options from children
    const options = [];
    const processChildren = (children) => {
      if (Array.isArray(children)) {
        children.forEach(child => processChildren(child));
      } else if (children?.type?.name === 'SelectContent') {
        processChildren(children.props.children);
      } else if (children?.type?.name === 'SelectItem') {
        options.push({
          value: children.props.value,
          label: children.props.children
        });
      }
    };
    processChildren(children);

    return (
      <select 
        value={value} 
        onChange={(e) => onValueChange(e.target.value)}
        data-testid={testId}
      >
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    );
  },
  SelectContent: ({ children }) => children,
  SelectItem: ({ children, value }) => ({ type: { name: 'SelectItem' }, props: { children, value } }),
  SelectTrigger: ({ children }) => children,
  SelectValue: ({ placeholder }) => placeholder,
}));

const mockUseLocations = vi.mocked(useLocations);
const mockUseCreateLocation = vi.mocked(useCreateLocation);
const mockUseUpdateLocation = vi.mocked(useUpdateLocation);
const mockUseDeleteLocation = vi.mocked(useDeleteLocation);
const mockUseEvent = vi.mocked(useEvent);
const mockUseEventLevels = vi.mocked(useEventLevels);
const mockUseToast = vi.mocked(useToast);

describe('LocationManagement Component', () => {
  let queryClient: QueryClient;
  const mockToast = vi.fn();
  const mockOnBack = vi.fn();

  const mockEvent = {
    id: 'event1',
    name: 'Spring Horse Trials 2024',
    start_date: '2024-05-01',
    end_date: '2024-05-03',
    event_type: 'horse_trials',
    is_active: true
  };

  const mockLocations = [
    {
      id: 'loc1',
      name: 'Main Arena',
      event_id: 'event1',
      activity_type: 'dressage',
      description: 'Primary dressage competition ring',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'loc2',
      name: 'Cross Country Course',
      event_id: 'event1',
      activity_type: 'cross_country',
      description: 'Challenging cross country course',
      created_at: '2024-01-01T00:00:00Z'
    }
  ];

  const mockEventLevels = [
    {
      levels: {
        discipline: 'eventing',
        name: 'Beginner'
      }
    },
    {
      levels: {
        discipline: 'eventing',
        name: 'Intermediate'
      }
    }
  ];

  const defaultProps = {
    eventId: 'event1',
    eventName: 'Spring Horse Trials 2024',
    onBack: mockOnBack
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    
    vi.clearAllMocks();
    mockUseToast.mockReturnValue({ toast: mockToast });

    // Default mock implementations
    mockUseLocations.mockReturnValue({
      data: mockLocations,
      isLoading: false,
      error: null,
    } as any);

    mockUseEvent.mockReturnValue({
      data: mockEvent,
      isLoading: false,
      error: null,
    } as any);

    mockUseEventLevels.mockReturnValue({
      data: mockEventLevels,
      isLoading: false,
      error: null,
    } as any);

    mockUseCreateLocation.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    } as any);

    mockUseUpdateLocation.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    } as any);

    mockUseDeleteLocation.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
    } as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const wrapper = ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );

  describe('Rendering', () => {
    it('renders component with correct title and event name', () => {
      render(<LocationManagement {...defaultProps} />, { wrapper });

      expect(screen.getByText('Location Management - Spring Horse Trials 2024')).toBeInTheDocument();
      expect(screen.getByText('← Back to Event Details')).toBeInTheDocument();
      expect(screen.getByTestId('add-location-button')).toBeInTheDocument();
    });

    it('displays selected date when provided', () => {
      render(<LocationManagement {...defaultProps} selectedDate="2024-05-01" />, { wrapper });

      expect(screen.getByText('(5/1/2024)')).toBeInTheDocument();
    });

    it('displays locations when data is loaded', () => {
      render(<LocationManagement {...defaultProps} />, { wrapper });

      expect(screen.getByText('Main Arena')).toBeInTheDocument();
      expect(screen.getByText('Cross Country Course')).toBeInTheDocument();
      expect(screen.getByText('Primary dressage competition ring')).toBeInTheDocument();
      expect(screen.getByText('Challenging cross country course')).toBeInTheDocument();
    });

    it('shows loading state', () => {
      mockUseLocations.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      expect(screen.getByText('Loading locations...')).toBeInTheDocument();
    });

    it('shows error state', () => {
      const mockError = new Error('Failed to load locations');
      mockUseLocations.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: mockError,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      expect(screen.getByText('Error')).toBeInTheDocument();
      expect(screen.getByText('Failed to load locations: Failed to load locations')).toBeInTheDocument();
    });

    it('shows empty state when no locations', () => {
      mockUseLocations.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      expect(screen.getByText('No locations yet')).toBeInTheDocument();
      expect(screen.getByText('Create your first location for this event.')).toBeInTheDocument();
    });
  });

  describe('Location Display', () => {
    it('displays location information correctly', () => {
      render(<LocationManagement {...defaultProps} />, { wrapper });

      expect(screen.getByText('Main Arena')).toBeInTheDocument();
      expect(screen.getByText('Primary dressage competition ring')).toBeInTheDocument();
      expect(screen.getByText('Dressage')).toBeInTheDocument(); // Activity type badge
    });

    it('displays activity type badges correctly', () => {
      render(<LocationManagement {...defaultProps} />, { wrapper });

      // Check for activity type badges
      const dressageBadge = screen.getByText('Dressage');
      const crossCountryBadge = screen.getByText('Cross Country');
      
      expect(dressageBadge).toBeInTheDocument();
      expect(crossCountryBadge).toBeInTheDocument();
    });

    it('shows location actions (Edit, Delete, Manage)', () => {
      render(<LocationManagement {...defaultProps} />, { wrapper });

      // Check for action buttons on each location
      const editButtons = screen.getAllByText('Edit');
      const deleteButtons = screen.getAllByText('Delete');
      const manageButtons = screen.getAllByText('Manage Activities/Schedule');

      expect(editButtons).toHaveLength(2);
      expect(deleteButtons).toHaveLength(2);
      expect(manageButtons).toHaveLength(2);
    });
  });

  describe('Create Location Form', () => {
    it('shows create form when Add Location button is clicked', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('add-location-button'));

      expect(screen.getByText('Create New Location')).toBeInTheDocument();
      expect(screen.getByTestId('location-name-input')).toBeInTheDocument();
      expect(screen.getByTestId('activity-type-select')).toBeInTheDocument();
      expect(screen.getByTestId('description-input')).toBeInTheDocument();
    });

    it('creates location successfully', async () => {
      const user = userEvent.setup();
      const mockMutate = vi.fn();
      mockUseCreateLocation.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('add-location-button'));

      await user.type(screen.getByTestId('location-name-input'), 'New Arena');
      await user.selectOptions(screen.getByTestId('activity-type-select'), 'show_jumping');
      await user.type(screen.getByTestId('description-input'), 'New jumping arena');

      await user.click(screen.getByText('Create Location'));

      expect(mockMutate).toHaveBeenCalledWith(
        {
          name: 'New Arena',
          event_id: 'event1',
          description: 'New jumping arena',
          activity_type: 'show_jumping',
        },
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });

    it('disables create button when required fields are missing', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('add-location-button'));

      const createButton = screen.getByText('Create Location');
      expect(createButton).toBeDisabled();

      await user.type(screen.getByTestId('location-name-input'), 'New Arena');
      expect(createButton).not.toBeDisabled();
    });

    it('cancels create form when Cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('add-location-button'));
      expect(screen.getByText('Create New Location')).toBeInTheDocument();

      await user.click(screen.getByText('Cancel'));
      expect(screen.queryByText('Create New Location')).not.toBeInTheDocument();
    });

    it('shows loading state during location creation', async () => {
      const user = userEvent.setup();
      mockUseCreateLocation.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('add-location-button'));
      await user.type(screen.getByTestId('location-name-input'), 'New Arena');

      const createButton = screen.getByText('Create Location');
      expect(createButton).toBeDisabled();
      expect(screen.getByTestId('create-location-loading')).toBeInTheDocument();
    });
  });

  describe('Edit Location', () => {
    it('shows edit form when Edit button is clicked', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      const editButtons = screen.getAllByText('Edit');
      await user.click(editButtons[0]); // Click edit on first location

      expect(screen.getByTestId('edit-location-name-loc1')).toBeInTheDocument();
      expect(screen.getByTestId('edit-activity-type-select-loc1')).toBeInTheDocument();
      expect(screen.getByTestId('edit-description-input-loc1')).toBeInTheDocument();
    });

    it('updates location name successfully', async () => {
      const user = userEvent.setup();
      const mockMutate = vi.fn();
      mockUseUpdateLocation.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
      } as any);

      // Use a stable reference for the locations
      const stableMockLocations = [...mockLocations];
      mockUseLocations.mockReturnValue({
        data: stableMockLocations,
        isLoading: false,
        error: null,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      const editButtons = screen.getAllByText('Edit');
      await user.click(editButtons[0]);

      const nameInput = screen.getByTestId('edit-location-name-loc1');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Arena Name');

      await user.click(screen.getByText('Update Location'));

      expect(mockMutate).toHaveBeenCalledWith(
        {
          id: 'loc1',
          name: 'Updated Arena Name',
          event_id: 'event1',
          description: 'Primary dressage competition ring',
          activity_type: 'dressage',
        },
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });

    it('cancels edit mode', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      const editButtons = screen.getAllByText('Edit');
      await user.click(editButtons[0]);

      expect(screen.getByTestId('edit-location-name-loc1')).toBeInTheDocument();

      await user.click(screen.getByText('Cancel'));
      expect(screen.queryByTestId('edit-location-name-loc1')).not.toBeInTheDocument();
    });

    it('shows loading state during location update', async () => {
      const user = userEvent.setup();
      mockUseUpdateLocation.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      const editButtons = screen.getAllByText('Edit');
      await user.click(editButtons[0]);

      const updateButton = screen.getByText('Update Location');
      expect(updateButton).toBeDisabled();
      expect(screen.getByTestId('update-location-loading')).toBeInTheDocument();
    });
  });

  describe('Delete Location', () => {
    it('shows delete confirmation dialog', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      const deleteButtons = screen.getAllByText('Delete');
      await user.click(deleteButtons[0]);

      expect(screen.getByText('Delete Location')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to delete this location?')).toBeInTheDocument();
      expect(screen.getByText(/This will permanently remove Main Arena/)).toBeInTheDocument();
    });

    it('deletes location when confirmed', async () => {
      const user = userEvent.setup();
      const mockMutate = vi.fn();
      mockUseDeleteLocation.mockReturnValue({
        mutate: mockMutate,
        isPending: false,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      const deleteButtons = screen.getAllByText('Delete');
      await user.click(deleteButtons[0]); // Click delete on first location

      // Click the confirm delete button in the dialog
      await user.click(screen.getByRole('button', { name: 'Delete' }));

      expect(mockMutate).toHaveBeenCalledWith(
        {
          locationId: 'loc1',
          eventId: 'event1',
        },
        expect.objectContaining({
          onSuccess: expect.any(Function),
        })
      );
    });

    it('cancels delete when Cancel is clicked', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      const deleteButtons = screen.getAllByText('Delete');
      await user.click(deleteButtons[0]);

      expect(screen.getByText('Delete Location')).toBeInTheDocument();

      await user.click(screen.getByText('Cancel'));
      expect(screen.queryByText('Delete Location')).not.toBeInTheDocument();
    });

    it('shows loading state during location deletion', async () => {
      const user = userEvent.setup();
      mockUseDeleteLocation.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      const deleteButtons = screen.getAllByText('Delete');
      await user.click(deleteButtons[0]);

      // The delete button should be disabled and show loading
      const confirmDeleteButton = screen.getByTestId('confirm-delete-location-button');
      expect(confirmDeleteButton).toBeDisabled();
      expect(screen.getByTestId('delete-location-loading')).toBeInTheDocument();
    });
  });

  describe('Location Expansion', () => {
    it('expands location to show tabs when Manage button is clicked', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      const manageButtons = screen.getAllByText('Manage Activities/Schedule');
      await user.click(manageButtons[0]);

      expect(screen.getByText('Activities')).toBeInTheDocument();
      expect(screen.getByText('Schedule View')).toBeInTheDocument();
      expect(screen.getByText('Time Slots')).toBeInTheDocument();
    });

    it('collapses location when Hide Details is clicked', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      const manageButtons = screen.getAllByText('Manage Activities/Schedule');
      await user.click(manageButtons[0]);

      expect(screen.getByText('Activities')).toBeInTheDocument();

      await user.click(screen.getByText('Hide Details'));
      expect(screen.queryByText('Activities')).not.toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('calls onBack when Back button is clicked', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByText('← Back to Event Details'));
      expect(mockOnBack).toHaveBeenCalled();
    });
  });

  describe('Event Type Handling', () => {
    it('displays correct activity type options for horse trials event', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      // Open create form to see activity type options
      await user.click(screen.getByTestId('add-location-button'));

      const activitySelect = screen.getByTestId('activity-type-select');
      expect(activitySelect).toBeInTheDocument();

      // Check that the options are rendered (they would be in the select element)
      const options = activitySelect.querySelectorAll('option');
      expect(options.length).toBeGreaterThan(0);
    });

    it('allows selecting different activity types in create form', async () => {
      const user = userEvent.setup();
      render(<LocationManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('add-location-button'));
      
      await user.selectOptions(screen.getByTestId('activity-type-select'), 'show_jumping');
      
      expect(screen.getByTestId('activity-type-select')).toHaveValue('show_jumping');
    });
  });

  describe('Loading States', () => {
    it('shows loading state during location creation', async () => {
      const user = userEvent.setup();
      mockUseCreateLocation.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      await user.click(screen.getByTestId('add-location-button'));
      await user.type(screen.getByTestId('location-name-input'), 'New Arena');

      expect(screen.getByTestId('create-location-loading')).toBeInTheDocument();
    });

    it('shows loading state during location update', async () => {
      const user = userEvent.setup();
      mockUseUpdateLocation.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      const editButtons = screen.getAllByText('Edit');
      await user.click(editButtons[0]);

      expect(screen.getByTestId('update-location-loading')).toBeInTheDocument();
    });

    it('shows loading state during location deletion', async () => {
      const user = userEvent.setup();
      mockUseDeleteLocation.mockReturnValue({
        mutate: vi.fn(),
        isPending: true,
      } as any);

      render(<LocationManagement {...defaultProps} />, { wrapper });

      const deleteButtons = screen.getAllByText('Delete');
      await user.click(deleteButtons[0]);

      // The delete button should be disabled and show loading
      const confirmDeleteButton = screen.getByTestId('confirm-delete-location-button');
      expect(confirmDeleteButton).toBeDisabled();
      expect(screen.getByTestId('delete-location-loading')).toBeInTheDocument();
    });
  });
}); 