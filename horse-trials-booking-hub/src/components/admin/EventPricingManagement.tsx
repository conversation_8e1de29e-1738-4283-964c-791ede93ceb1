import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Plus, Edit, Trash, DollarSign, Settings, AlertCircle, PlusCircle, Edit2, Trash2 } from 'lucide-react';
import { useEventPricing, useCreateEventPricing, useUpdateEventPricing, useDeleteEventPricing, EventPrice } from '@/hooks/useEventPricing';
import { usePricingSettings } from '@/hooks/usePricingSettings';
import { Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';

interface EventPricingManagementProps {
  eventId: string;
}

const activityTypes = ['dressage', 'show_jumping', 'cross_country'];

export const EventPricingManagement: React.FC<EventPricingManagementProps> = ({ eventId }) => {
  const { data: pricing, isLoading, error } = useEventPricing(eventId);
  const createPricing = useCreateEventPricing();
  const updatePricing = useUpdateEventPricing();
  const deletePricing = useDeleteEventPricing();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentPrice, setCurrentPrice] = useState<Partial<EventPrice> | null>(null);

  const isSaving = createPricing.isLoading || updatePricing.isLoading;

  const handleOpenDialog = (price: Partial<EventPrice> | null = null) => {
    setCurrentPrice(price);
    setIsDialogOpen(true);
  };

  const handleSave = async () => {
    if (!currentPrice) return;

    const formData = {
      activity_type: currentPrice.activity_type!,
      price: Number(currentPrice.price) || 0,
    };

    if (currentPrice.id) {
      // Update - pass id, eventId, and updates separately
      await new Promise((resolve) => {
        updatePricing.mutate(
          { id: currentPrice.id, eventId, ...formData },
          {
            onSuccess: () => {
              setIsDialogOpen(false);
              setCurrentPrice(null);
              resolve(null);
            },
          }
        );
      });
    } else {
      // Create - pass eventId and pricing data
      await new Promise((resolve) => {
        createPricing.mutate(
          { eventId, ...formData },
          {
            onSuccess: () => {
              setIsDialogOpen(false);
              setCurrentPrice(null);
              resolve(null);
            },
          }
        );
      });
    }
  };
  
  const handleDelete = (id: string) => {
    deletePricing.mutate({ id, eventId });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">Error loading pricing: {error.message}</div>;
  }

  return (
    <div data-testid="event-pricing-management">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            <CardTitle>Event Pricing</CardTitle>
          </div>
          <Button size="sm" onClick={() => handleOpenDialog({})}>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Pricing
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {pricing && pricing.length > 0 ? (
              pricing.map((price) => (
                <div key={price.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-semibold capitalize">{price.activity_type.replace('_', ' ')}</h4>
                    <p className="text-sm text-gray-500">${price.price.toFixed(2)}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">Event-specific</Badge>
                    <Button variant="ghost" size="icon" onClick={() => handleOpenDialog(price)} data-testid="edit-pricing">
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <AlertDialog>
                        <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="icon" className="text-red-500 hover:text-red-600" data-testid="delete-pricing">
                                <Trash2 className="h-4 w-4" />
                            </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                            <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete this pricing entry.
                            </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(price.id)}>Delete</AlertDialogAction>
                            </AlertDialogFooter>
                        </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No pricing found</h3>
                <p className="mt-1 text-sm text-gray-500">Get started by adding a price for an activity.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle data-testid="dialog-title">{currentPrice?.id ? 'Edit' : 'Add'} Pricing</DialogTitle>
            <DialogDescription>
              Set the price for a specific activity at this event.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="activity_type">Activity Type</Label>
              <Select
                value={currentPrice?.activity_type}
                onValueChange={(value) => setCurrentPrice(p => ({ ...p, activity_type: value as 'dressage' | 'show_jumping' | 'cross_country' }))}
              >
                <SelectTrigger id="activity_type" data-testid="activity-type-select">
                  <SelectValue placeholder="Select an activity" />
                </SelectTrigger>
                <SelectContent>
                  {activityTypes.map(type => (
                    <SelectItem key={type} value={type} className="capitalize">{type.replace('_', ' ')}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="price">Price</Label>
              <Input
                id="price"
                type="number"
                placeholder="e.g., 40"
                value={currentPrice?.price || ''}
                onChange={(e) => setCurrentPrice(p => ({ ...p, price: parseFloat(e.target.value) }))}
                data-testid="price-input"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)} disabled={isSaving} data-testid="cancel-pricing-btn">Cancel</Button>
            <Button onClick={handleSave} disabled={isSaving} data-testid="save-pricing-btn">
              {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" data-testid="save-pricing-loading" />}
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}; 