import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Plus, Trash2, BookOpen } from 'lucide-react';
import { useDressageTests, useEventDressageTests, useCreateEventDressageTest, useDeleteEventDressageTest } from '@/hooks/useDressageTests';
import { useLevels } from '@/hooks/useLevels';

interface EventDressageTestsManagementProps {
  eventId: string;
  eventName: string;
  selectedDate?: string;
}

export const EventDressageTestsManagement: React.FC<EventDressageTestsManagementProps> = ({
  eventId,
  eventName,
  selectedDate
}) => {
  const [selectedTestId, setSelectedTestId] = useState('');
  const [selectedLevelFilter, setSelectedLevelFilter] = useState('all');

  const { data: dressageLevels } = useLevels('dressage');
  const { data: allTests } = useDressageTests(selectedLevelFilter === 'all' ? undefined : selectedLevelFilter);
  const { data: eventTests } = useEventDressageTests(eventId);
  const createEventTest = useCreateEventDressageTest();
  const deleteEventTest = useDeleteEventDressageTest();

  const availableTests = allTests?.filter(test => 
    test.id && test.id.trim() !== '' &&
    !eventTests?.some(eventTest => eventTest.test_id === test.id)
  );

  const handleAddTest = () => {
    if (selectedTestId && selectedTestId.trim() !== '') {
      createEventTest.mutate({
        event_id: eventId,
        test_id: selectedTestId,
        is_enabled: true
      }, {
        onSuccess: () => {
          setSelectedTestId('');
        }
      });
    }
  };

  const handleRemoveTest = (eventTestId: string) => {
    deleteEventTest.mutate(eventTestId);
  };

  return (
    <div data-testid="event-dressage-tests-management">
      <Card className="border-green-200">
        <CardHeader>
          <CardTitle className="text-lg text-green-800 flex items-center">
            <BookOpen className="w-5 h-5 mr-2" />
            Dressage Tests - {eventName}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="level-filter">Filter by Level</Label>
              <Select value={selectedLevelFilter} onValueChange={setSelectedLevelFilter}>
                <SelectTrigger data-testid="level-filter-select">
                  <SelectValue placeholder="All levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All levels</SelectItem>
                  {dressageLevels?.filter(level => level.id && level.id.trim() !== '').map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      {level.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="test-select">Add Dressage Test</Label>
              <Select value={selectedTestId} onValueChange={setSelectedTestId}>
                <SelectTrigger data-testid="test-select-trigger">
                  <SelectValue placeholder="Select a test" />
                </SelectTrigger>
                <SelectContent>
                  {availableTests?.map((test) => (
                    <SelectItem key={test.id} value={test.id} data-testid={`test-option-${test.id}`}>
                      {test.label} {test.levels?.name && `(${test.levels.name})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button 
                onClick={handleAddTest}
                disabled={!selectedTestId || selectedTestId.trim() === '' || createEventTest.isPending}
                className="bg-green-600 hover:bg-green-700"
                data-testid="add-test-button"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Test
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-green-800">Current Event Tests</h4>
            {eventTests && eventTests.length > 0 ? (
              <div className="grid grid-cols-1 gap-2">
                {eventTests.map((eventTest) => (
                  <div key={eventTest.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline" className="text-green-700">
                        {eventTest.dressage_test_library?.label}
                      </Badge>
                      {eventTest.dressage_test_library?.levels?.name && (
                        <span className="text-sm text-gray-600">
                          {eventTest.dressage_test_library.levels.name}
                        </span>
                      )}
                      {eventTest.dressage_test_library?.organization && (
                        <span className="text-xs text-gray-500">
                          {eventTest.dressage_test_library.organization}
                        </span>
                      )}
                      {eventTest.dressage_test_library?.description && (
                        <span className="text-xs text-gray-500">
                          {eventTest.dressage_test_library.description}
                        </span>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveTest(eventTest.id)}
                      className="text-red-600 border-red-300 hover:bg-red-50"
                      data-testid={`remove-test-button-${eventTest.id}`}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 text-gray-500">
                <BookOpen className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p>No dressage tests added to this event yet.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
