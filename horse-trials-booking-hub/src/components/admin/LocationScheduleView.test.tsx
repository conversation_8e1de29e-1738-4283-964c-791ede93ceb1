import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { LocationScheduleView } from './LocationScheduleView';

// Mock the hooks
vi.mock('@/hooks/useLocationSchedule', () => ({
  useLocationSchedule: vi.fn(),
}));

// Mock the UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => <div data-testid="card-content">{children}</div>,
  CardHeader: ({ children }: { children: React.ReactNode }) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-title" className={className}>{children}</div>
  ),
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant, className }: { children: React.ReactNode; variant?: string; className?: string }) => (
    <span data-testid="badge" data-variant={variant} className={className}>{children}</span>
  ),
}));

vi.mock('@/components/ui/skeleton', () => ({
  Skeleton: ({ className }: { className?: string }) => <div data-testid="skeleton" className={className} />,
}));

vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, defaultValue }: { children: React.ReactNode; defaultValue: string }) => (
    <div data-testid="tabs" data-default-value={defaultValue}>{children}</div>
  ),
  TabsList: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <button data-testid={`tab-${value}`} data-value={value}>{children}</button>
  ),
  TabsContent: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <div data-testid={`tab-content-${value}`} data-value={value}>{children}</div>
  ),
}));

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  Clock: () => <div data-testid="clock-icon">Clock</div>,
  Calendar: () => <div data-testid="calendar-icon">Calendar</div>,
  Coffee: () => <div data-testid="coffee-icon">Coffee</div>,
  Activity: () => <div data-testid="activity-icon">Activity</div>,
  Loader2: () => <div data-testid="loader-icon">Loader2</div>,
}));

// Import the mocked hook after mocking
import { useLocationSchedule } from '@/hooks/useLocationSchedule';
const mockUseLocationSchedule = vi.mocked(useLocationSchedule);

describe('LocationScheduleView', () => {
  const mockLocationId = 'location-123';
  const mockLocationName = 'Main Arena';
  const mockSelectedDate = '2024-01-15';

  // Define mockSchedule and mockScheduleStats for reuse
  const mockSchedule = [
    {
      id: 'activity-1',
      start_time: '2024-01-15T09:00:00Z',
      end_time: '2024-01-15T10:00:00Z',
      type: 'activity',
      activity_type: 'dressage',
      level: 'Intro',
      description: 'Basic dressage test',
      duration_minutes: 60,
    },
    {
      id: 'break-1',
      start_time: '2024-01-15T10:00:00Z',
      end_time: '2024-01-15T10:15:00Z',
      type: 'break',
      duration_minutes: 15,
    },
    {
      id: 'activity-2',
      start_time: '2024-01-15T10:15:00Z',
      end_time: '2024-01-15T11:15:00Z',
      type: 'activity',
      activity_type: 'show_jumping',
      level: 'Prelim',
      description: 'Show jumping course',
      duration_minutes: 60,
    },
  ];

  const mockScheduleStats = {
    activity_count: 2,
    activity_minutes: 120,
    break_count: 1,
    start_time: '2024-01-15T09:00:00Z',
    end_time: '2024-01-15T11:15:00Z',
  };

  const mockFormatTime = vi.fn((time: string) => {
    const date = new Date(time);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  });

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseLocationSchedule.mockReturnValue({
      schedule: mockSchedule,
      scheduleStats: mockScheduleStats,
      formatTime: mockFormatTime,
      isLoading: false,
      error: null,
    });
  });

  describe('Rendering', () => {
    it('renders the component with location name', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText(`${mockLocationName} Schedule`)).toBeInTheDocument();
    });

    it('displays loading state when data is loading', () => {
      mockUseLocationSchedule.mockReturnValue({
        schedule: null,
        scheduleStats: null,
        formatTime: mockFormatTime,
        isLoading: true,
        error: null,
      });

      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('Loading schedule...')).toBeInTheDocument();
      expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
    });

    it('displays error state when there is an error', () => {
      const mockError = new Error('Failed to load schedule');
      mockUseLocationSchedule.mockReturnValue({
        schedule: null,
        scheduleStats: null,
        formatTime: mockFormatTime,
        isLoading: false,
        error: mockError,
      });

      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('Error Loading Schedule')).toBeInTheDocument();
      expect(screen.getByText('Failed to load location schedule: Failed to load schedule')).toBeInTheDocument();
    });

    it('displays empty state when no schedule data', () => {
      mockUseLocationSchedule.mockReturnValue({
        schedule: [],
        scheduleStats: null,
        formatTime: mockFormatTime,
        isLoading: false,
        error: null,
      });

      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('No Activities Scheduled')).toBeInTheDocument();
      expect(screen.getByText('No activities have been scheduled for this location yet.')).toBeInTheDocument();
      expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
    });
  });

  describe('Schedule Statistics', () => {
    it('displays schedule statistics correctly', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('2')).toBeInTheDocument(); // Activity count
      expect(screen.getByText('Activities')).toBeInTheDocument();
      expect(screen.getByText('2h')).toBeInTheDocument(); // Activity time (120 minutes = 2h)
      expect(screen.getByText('Activity Time')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument(); // Break count
      expect(screen.getByText('Breaks')).toBeInTheDocument();
    });

    it('displays schedule time range when available', () => {
      mockUseLocationSchedule.mockReturnValue({
        schedule: mockSchedule,
        scheduleStats: mockScheduleStats,
        formatTime: mockFormatTime,
        isLoading: false,
        error: null,
      });

      render(
        <LocationScheduleView 
          locationId={mockLocationId} 
          selectedDate={mockSelectedDate}
        />
      );
      
      // Use getAllByTestId to get all clock icons and check the first one
      const clockIcons = screen.getAllByTestId('clock-icon');
      expect(clockIcons[0]).toBeInTheDocument();
      expect(mockFormatTime).toHaveBeenCalledWith('2024-01-15T09:00:00Z');
      expect(mockFormatTime).toHaveBeenCalledWith('2024-01-15T11:15:00Z');
    });

    it('handles duration formatting correctly', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      // Check for 2h (120 minutes)
      expect(screen.getByText('2h')).toBeInTheDocument();
    });
  });

  describe('Schedule Items', () => {
    it('renders activity items correctly', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('dressage - Intro')).toBeInTheDocument();
      expect(screen.getByText('Basic dressage test')).toBeInTheDocument();
      expect(screen.getByText('show jumping - Prelim')).toBeInTheDocument();
      expect(screen.getByText('Show jumping course')).toBeInTheDocument();
    });

    it('renders break items correctly', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('Break')).toBeInTheDocument();
    });

    it('displays activity type badges with correct styling', () => {
      mockUseLocationSchedule.mockReturnValue({
        schedule: mockSchedule,
        scheduleStats: mockScheduleStats,
        formatTime: mockFormatTime,
        isLoading: false,
        error: null,
      });

      render(
        <LocationScheduleView 
          locationId={mockLocationId} 
          selectedDate={mockSelectedDate}
        />
      );
      
      // Check for activity duration badges - use getAllByText to handle multiple elements
      const oneHourBadges = screen.getAllByText('1h');
      expect(oneHourBadges.length).toBeGreaterThan(0);
      expect(screen.getByText('15 min')).toBeInTheDocument(); // 15 minutes
    });

    it('displays time information for each item', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(mockFormatTime).toHaveBeenCalledWith('2024-01-15T09:00:00Z');
      expect(mockFormatTime).toHaveBeenCalledWith('2024-01-15T10:00:00Z');
      expect(mockFormatTime).toHaveBeenCalledWith('2024-01-15T10:15:00Z');
      expect(mockFormatTime).toHaveBeenCalledWith('2024-01-15T11:15:00Z');
    });
  });

  describe('Date Filtering', () => {
    it('displays schedule for selected date when provided', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
          selectedDate={mockSelectedDate}
        />
      );
      
      expect(screen.getByText('Monday, January 15, 2024')).toBeInTheDocument();
    });

    it('displays all dates when no selected date is provided', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('Monday, January 15, 2024')).toBeInTheDocument();
    });

    it('groups schedule items by date correctly', () => {
      const multiDateSchedule = [
        {
          id: 'activity-1',
          start_time: '2024-01-15T09:00:00Z',
          end_time: '2024-01-15T10:00:00Z',
          type: 'activity' as const,
          activity_type: 'dressage',
          level: 'Intro',
          description: 'Day 1 activity',
          duration_minutes: 60,
        },
        {
          id: 'activity-2',
          start_time: '2024-01-16T09:00:00Z',
          end_time: '2024-01-16T10:00:00Z',
          type: 'activity' as const,
          activity_type: 'show_jumping',
          level: 'Prelim',
          description: 'Day 2 activity',
          duration_minutes: 60,
        },
      ];

      mockUseLocationSchedule.mockReturnValue({
        schedule: multiDateSchedule,
        scheduleStats: mockScheduleStats,
        formatTime: mockFormatTime,
        isLoading: false,
        error: null,
      });

      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('Monday, January 15, 2024')).toBeInTheDocument();
      expect(screen.getByText('Tuesday, January 16, 2024')).toBeInTheDocument();
      expect(screen.getByText('Day 1 activity')).toBeInTheDocument();
      expect(screen.getByText('Day 2 activity')).toBeInTheDocument();
    });
  });

  describe('Duration Formatting', () => {
    it('formats durations correctly for different time periods', () => {
      const durationTestData = [
        {
          id: 'item-1',
          type: 'activity',
          activity_type: 'dressage',
          level: null,
          description: 'Basic dressage test',
          start_time: '2024-01-15T09:00:00Z',
          end_time: '2024-01-15T09:30:00Z',
          duration_minutes: 30,
        },
        {
          id: 'item-2',
          type: 'activity',
          activity_type: 'show_jumping',
          level: null,
          description: 'Show jumping course',
          start_time: '2024-01-15T10:00:00Z',
          end_time: '2024-01-15T12:00:00Z',
          duration_minutes: 120,
        },
        {
          id: 'item-3',
          type: 'activity',
          activity_type: 'cross_country',
          level: null,
          description: 'Cross country course',
          start_time: '2024-01-15T13:00:00Z',
          end_time: '2024-01-15T14:30:00Z',
          duration_minutes: 90,
        },
      ];

      mockUseLocationSchedule.mockReturnValue({
        schedule: durationTestData,
        scheduleStats: mockScheduleStats,
        formatTime: mockFormatTime,
        isLoading: false,
        error: null,
      });

      render(
        <LocationScheduleView 
          locationId="loc-1" 
          selectedDate="2024-01-15"
        />
      );
      
      expect(screen.getByText('30 min')).toBeInTheDocument();
      const twoHourBadges = screen.getAllByText('2h');
      expect(twoHourBadges.length).toBeGreaterThan(0);
      expect(screen.getByText('1h 30m')).toBeInTheDocument();
    });
  });

  describe('Activity Type Display', () => {
    it('formats activity type names correctly', () => {
      const scheduleWithDifferentTypes = [
        {
          id: 'activity-1',
          start_time: '2024-01-15T09:00:00Z',
          end_time: '2024-01-15T10:00:00Z',
          type: 'activity' as const,
          activity_type: 'show_jumping',
          level: 'Intro',
          description: 'Show jumping test',
          duration_minutes: 60,
        },
        {
          id: 'activity-2',
          start_time: '2024-01-15T10:00:00Z',
          end_time: '2024-01-15T11:00:00Z',
          type: 'activity' as const,
          activity_type: 'cross_country',
          level: 'Prelim',
          description: 'Cross country test',
          duration_minutes: 60,
        },
      ];

      mockUseLocationSchedule.mockReturnValue({
        schedule: scheduleWithDifferentTypes,
        scheduleStats: mockScheduleStats,
        formatTime: mockFormatTime,
        isLoading: false,
        error: null,
      });

      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('show jumping - Intro')).toBeInTheDocument();
      expect(screen.getByText('cross country - Prelim')).toBeInTheDocument();
    });

    it('handles activities without level information', () => {
      const scheduleWithoutLevel = [
        {
          id: 'activity-1',
          start_time: '2024-01-15T09:00:00Z',
          end_time: '2024-01-15T10:00:00Z',
          type: 'activity' as const,
          activity_type: 'dressage',
          description: 'Dressage test',
          duration_minutes: 60,
        },
      ];

      mockUseLocationSchedule.mockReturnValue({
        schedule: scheduleWithoutLevel,
        scheduleStats: mockScheduleStats,
        formatTime: mockFormatTime,
        isLoading: false,
        error: null,
      });

      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('dressage')).toBeInTheDocument();
    });
  });

  describe('UI Element Regression Testing', () => {
    it('ensures all critical UI elements are present', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      // Header elements
      expect(screen.getByText(`${mockLocationName} Schedule`)).toBeInTheDocument();
      
      // Statistics elements
      expect(screen.getByText('Activities')).toBeInTheDocument();
      expect(screen.getByText('Activity Time')).toBeInTheDocument();
      expect(screen.getByText('Breaks')).toBeInTheDocument();
      
      // Schedule items
      expect(screen.getByText('dressage - Intro')).toBeInTheDocument();
      expect(screen.getByText('Break')).toBeInTheDocument();
    });

    it('maintains proper card structure', () => {
      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('card-header')).toBeInTheDocument();
      expect(screen.getByTestId('card-content')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles network errors gracefully', () => {
      const networkError = new Error('Network error');
      mockUseLocationSchedule.mockReturnValue({
        schedule: null,
        scheduleStats: null,
        formatTime: mockFormatTime,
        isLoading: false,
        error: networkError,
      });

      render(
        <LocationScheduleView
          locationId={mockLocationId}
          locationName={mockLocationName}
        />
      );
      
      expect(screen.getByText('Error Loading Schedule')).toBeInTheDocument();
      expect(screen.getByText('Failed to load location schedule: Network error')).toBeInTheDocument();
    });

    it('handles empty schedule data gracefully', () => {
      mockUseLocationSchedule.mockReturnValue({
        schedule: [],
        scheduleStats: null,
        formatTime: mockFormatTime,
        isLoading: false,
        error: null,
      });

      render(
        <LocationScheduleView 
          locationId="loc-1" 
          selectedDate="2024-01-15"
        />
      );
      
      expect(screen.getByText('No Activities Scheduled')).toBeInTheDocument();
      expect(screen.getByText('No activities have been scheduled for this location yet.')).toBeInTheDocument();
    });
  });
}); 