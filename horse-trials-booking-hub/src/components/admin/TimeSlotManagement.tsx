import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clock, User, Target, Calendar, AlertCircle, PawPrint, RefreshCw, Plus, Trash, Loader2 } from 'lucide-react';
import { useTimeSlots } from '@/hooks/useTimeSlots';
import { useActivities } from '@/hooks/useActivities';
import { useBookings } from '@/hooks/useBookings';
import { useRegenerateLocationTimeSlots } from '@/hooks/useTimeSlots';
import { useGenerateTimeSlots } from '@/hooks/useTimeSlots';
import { useDeleteTimeSlotsForDate } from '@/hooks/useTimeSlots';
import { AlertD<PERSON>og, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { formatDateForDisplay } from '@/utils/dateUtils';

interface TimeSlotManagementProps {
  locationId: string;
  locationName: string;
  selectedDate?: string;
}

interface TimeSlotWithBreak {
  type: 'slot' | 'break';
  slot?: any;
  startTime: string;
  endTime: string;
  breakDuration?: number;
}

const formatLocalTime = (isoString: string): string => {
  const date = new Date(isoString);
  return date.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit'
  });
};

const getLocalDate = (isoString: string): Date => {
  return new Date(isoString);
};

export const TimeSlotManagement: React.FC<TimeSlotManagementProps> = ({ 
  locationId,
  locationName,
  selectedDate
}) => {
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'generate' | 'regenerate' | 'additional'>('generate');
  const [selectedActivity, setSelectedActivity] = useState<string>('');

  const { data: timeSlots, isLoading: timeSlotsLoading, error: timeSlotsError } = useTimeSlots(locationId);
  const { data: activities, isLoading: activitiesLoading } = useActivities(locationId);
  const { data: bookings } = useBookings(locationId);

  const regenerateTimeSlots = useRegenerateLocationTimeSlots();
  const generateTimeSlots = useGenerateTimeSlots();
  const deleteTimeSlotsForDate = useDeleteTimeSlotsForDate();

  // Filter time slots by selected date
  const filteredTimeSlots = useMemo(() => {
    if (!timeSlots || !selectedDate) return timeSlots || [];
    
    return timeSlots.filter(slot => {
      const slotDate = slot.start_time.split('T')[0];
      return slotDate === selectedDate;
    });
  }, [timeSlots, selectedDate]);

  const handleRegenerateTimeSlots = () => {
    regenerateTimeSlots.mutate({ 
      locationId,
      selectedDate: selectedDate || undefined 
    });
    setConfirmDialogOpen(false);
  };

  const handleGenerateAdditionalSlots = () => {
    if (!selectedActivity) return;
    
    const activity = activities?.find(a => a.id === selectedActivity);
    if (!activity) return;
    
    generateTimeSlots.mutate({
      activityId: selectedActivity,
      slotDurationMinutes: activity.slot_duration_minutes
    });
    setConfirmDialogOpen(false);
  };

  const openDialog = (mode: 'generate' | 'regenerate' | 'additional') => {
    console.log('openDialog called with mode:', mode);
    console.log('Current confirmDialogOpen state:', confirmDialogOpen);
    // Force close first, then open to ensure clean state
    setConfirmDialogOpen(false);
    setTimeout(() => {
      setDialogMode(mode);
      setConfirmDialogOpen(true);
      console.log('Set confirmDialogOpen to true after reset');
    }, 10);
  };

  const closeDialog = () => {
    console.log('closeDialog called');
    setConfirmDialogOpen(false);
  };

  const getBookingForSlot = (slotId: string) => {
    return bookings?.find(booking => booking.time_slot_id === slotId);
  };

  // Debug logging
  console.log('TimeSlotManagement render:', {
    locationId,
    locationName,
    selectedDate,
    timeSlotsLoading,
    activitiesLoading,
    timeSlotsCount: timeSlots?.length || 0,
    activitiesCount: activities?.length || 0,
    activities: activities?.map(a => ({ id: a.id, type: a.activity_type, level: a.level, start_time: a.start_time })),
    filteredTimeSlotsCount: filteredTimeSlots?.length || 0,
    confirmDialogOpen,
    dialogMode
  });

  // Force reset dialog state if it's stuck
  useEffect(() => {
    if (confirmDialogOpen) {
      console.log('Dialog is stuck open, forcing reset');
      setConfirmDialogOpen(false);
    }
  }, []);

  console.log('About to check loading states...');

  if (timeSlotsLoading || activitiesLoading) {
    console.log('Showing loading state');
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg text-blue-800">{locationName} Time Slots</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600 mr-2" />
            Loading time slots...
          </div>
        </CardContent>
      </Card>
    );
  }

  console.log('About to check error state...');

  if (timeSlotsError) {
    console.log('Showing error state');
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-lg text-red-800">Error Loading Time Slots</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600">Failed to load time slots. Please try again.</p>
        </CardContent>
      </Card>
    );
  }

  console.log('About to check empty state...');

  if (!filteredTimeSlots || filteredTimeSlots.length === 0) {
    console.log('Showing empty state - no time slots');
    return (
      <Card className="border-amber-200">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-lg text-amber-800">
            No Time Slots Available
            {selectedDate && (
              <span className="text-sm font-normal text-gray-600 ml-2">
                for {formatDateForDisplay(selectedDate)}
              </span>
            )}
          </CardTitle>
          {activities && activities.length > 0 && (
            <Button 
              variant="outline" 
              className="text-blue-600 border-blue-300 hover:bg-blue-50"
              onClick={() => {
                console.log('Generate Time Slots button clicked');
                openDialog('generate');
              }}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Generate Time Slots
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Clock className="h-12 w-12 text-amber-400 mx-auto mb-4" />
            <p className="text-gray-600">
              {selectedDate 
                ? `No time slots found for ${formatDateForDisplay(selectedDate)}.`
                : 'No time slots have been generated for this location yet.'
              }
            </p>
            {activities && activities.length > 0 ? (
              <p className="text-sm text-gray-500 mt-2">
                Click "Generate Time Slots" to create slots for all activities.
              </p>
            ) : (
              <div className="text-sm text-red-500 mt-2">
                <p>No activities found for this location.</p>
                <p>Add activities first, then generate time slots.</p>
                <p className="text-xs text-gray-400 mt-1">Activities count: {activities?.length || 0}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  console.log('About to render main component...');

  const sortedTimeSlots = [...filteredTimeSlots].sort((a, b) => 
    getLocalDate(a.start_time).getTime() - getLocalDate(b.start_time).getTime()
  );

  const timeSlotsWithBreaks: TimeSlotWithBreak[] = [];
  
  for (let i = 0; i < sortedTimeSlots.length; i++) {
    const currentSlot = sortedTimeSlots[i];
    
    timeSlotsWithBreaks.push({
      type: 'slot',
      slot: currentSlot,
      startTime: currentSlot.start_time,
      endTime: currentSlot.end_time
    });
    
    if (i < sortedTimeSlots.length - 1) {
      const nextSlot = sortedTimeSlots[i + 1];
      const currentEndTime = getLocalDate(currentSlot.end_time);
      const nextStartTime = getLocalDate(nextSlot.start_time);
      
      const breakDurationMs = nextStartTime.getTime() - currentEndTime.getTime();
      const breakDurationMinutes = Math.round(breakDurationMs / 60000);
      
      if (breakDurationMinutes > 1) {
        timeSlotsWithBreaks.push({
          type: 'break',
          startTime: currentSlot.end_time,
          endTime: nextSlot.start_time,
          breakDuration: breakDurationMinutes
        });
      }
    }
  }

  const timeSlotsByDate: Record<string, TimeSlotWithBreak[]> = {};
  
  timeSlotsWithBreaks.forEach(item => {
    const date = formatDateForDisplay(item.startTime.split('T')[0]);
    if (!timeSlotsByDate[date]) {
      timeSlotsByDate[date] = [];
    }
    timeSlotsByDate[date].push(item);
  });

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 
      ? `${hours}h ${remainingMinutes}m` 
      : `${hours}h`;
  };

  return (
    <>
      {/* Debug info */}
      <div style={{ 
        position: 'fixed', 
        top: '10px', 
        right: '10px', 
        background: 'yellow', 
        padding: '10px', 
        zIndex: 9999,
        border: '2px solid red'
      }}>
        Debug: Dialog open = {confirmDialogOpen.toString()}<br/>
        Mode = {dialogMode}<br/>
        Activities = {activities?.length || 0}
      </div>

      {/* Simple test */}
      <div style={{
        position: 'fixed',
        top: '100px',
        right: '10px',
        background: 'green',
        color: 'white',
        padding: '10px',
        zIndex: 9999
      }}>
        Simple test - always visible
      </div>

      {/* Test dialog */}
      {confirmDialogOpen && (
        <div style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          background: 'white',
          border: '3px solid blue',
          padding: '20px',
          zIndex: 10000,
          minWidth: '300px'
        }}>
          <h3>TEST DIALOG - {dialogMode}</h3>
          <p>This is a test dialog to see if dialogs work at all.</p>
          <button onClick={() => setConfirmDialogOpen(false)}>Close</button>
        </div>
      )}

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-lg text-blue-800">
            {locationName} Time Slots
            {selectedDate && (
              <Badge variant="outline" className="ml-2 text-sm">
                {formatDateForDisplay(selectedDate)}
              </Badge>
            )}
          </CardTitle>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              className="text-green-600 border-green-300 hover:bg-green-50"
              onClick={() => openDialog('additional')}
              disabled={generateTimeSlots.isPending || regenerateTimeSlots.isPending || !activities?.length}
            >
              <Plus className="w-4 h-4 mr-2" />
              Generate Additional Slots
            </Button>
            <Button 
              variant="outline" 
              className="text-blue-600 border-blue-300 hover:bg-blue-50"
              onClick={() => openDialog('regenerate')}
              disabled={generateTimeSlots.isPending || regenerateTimeSlots.isPending}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${regenerateTimeSlots.isPending ? 'animate-spin' : ''}`} />
              Regenerate All Slots
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {Object.entries(timeSlotsByDate).map(([date, items]) => (
              <div key={date} className="space-y-2">
                <div className="flex items-center mb-2">
                  <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                  <h4 className="font-medium text-blue-800">{date}</h4>
                  <Badge variant="outline" className="ml-2 text-blue-600">
                    {items.filter(item => item.type === 'slot').length} slots
                  </Badge>
                </div>
                
                <div className="space-y-1">
                  {items.map((item, index) => (
                    item.type === 'slot' ? (
                      <div 
                        key={`slot-${index}`}
                        className={`p-3 rounded-md border ${
                          item.slot?.is_booked 
                            ? 'bg-amber-50 border-amber-200' 
                            : 'bg-green-50 border-green-200'
                        }`}
                      >
                        <div className="flex-grow">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <Clock className="h-4 w-4 mr-2 text-gray-600" />
                              <span className="font-medium">
                                {formatLocalTime(item.startTime)} - {formatLocalTime(item.endTime)}
                              </span>
                            </div>
                            
                            {item.slot?.is_booked ? (
                              <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">
                                Booked
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-green-600">
                                Available
                              </Badge>
                            )}
                          </div>
                          
                          {item.slot?.level && (
                            <div className="mt-1 text-sm flex items-center">
                              <Target className="h-3 w-3 mr-1 text-gray-500" />
                              <span className="text-gray-600">Level: {item.slot.level}</span>
                            </div>
                          )}

                          {item.slot?.is_booked && (() => {
                            const booking = getBookingForSlot(item.slot.id);
                            return booking ? (
                              <div className="mt-2 p-2 bg-white rounded border border-amber-200">
                                <div className="flex items-center mb-1">
                                  <User className="h-3 w-3 mr-1 text-amber-600" />
                                  <span className="text-sm font-medium text-amber-800">
                                    {booking.participant_name}
                                  </span>
                                </div>
                                <div className="text-xs text-gray-600 space-y-1">
                                  <div>Horse: {booking.horse_name}</div>
                                  <div>Email: {booking.payer_email}</div>
                                  {booking.activity_description && (
                                    <div>Activity: {booking.activity_description}</div>
                                  )}
                                  {booking.dressage_test_label && (
                                    <div className="text-blue-700 font-medium">
                                      Test: {booking.dressage_test_label}
                                      {booking.dressage_level_name && 
                                        ` (${booking.dressage_level_name})`
                                      }
                                    </div>
                                  )}
                                </div>
                              </div>
                            ) : null;
                          })()}
                        </div>
                      </div>
                    ) : (
                      <div 
                        key={`break-${index}`}
                        className="p-2 border-l-2 border-blue-300 bg-blue-50 rounded-r-md flex items-center"
                      >
                        <div className="ml-2 text-sm text-blue-600 flex items-center">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          <span>Break: {formatLocalTime(item.startTime)} - {formatLocalTime(item.endTime)}</span>
                          <Badge variant="outline" className="ml-2 text-blue-600">
                            {formatDuration(item.breakDuration || 0)}
                          </Badge>
                        </div>
                      </div>
                    )
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      <AlertDialog open={confirmDialogOpen} onOpenChange={(open) => {
        console.log('AlertDialog onOpenChange called with:', open);
        setConfirmDialogOpen(open);
      }}>
        {console.log('AlertDialog render - open:', confirmDialogOpen, 'mode:', dialogMode)}
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {dialogMode === 'generate' && 'Generate Time Slots'}
              {dialogMode === 'regenerate' && 'Regenerate All Time Slots'}
              {dialogMode === 'additional' && 'Generate Additional Time Slots'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {dialogMode === 'regenerate' && timeSlots.some(slot => slot.is_booked) ? (
                <div className="text-red-600">
                  Cannot regenerate time slots because some slots are already booked.
                </div>
              ) : dialogMode === 'regenerate' ? (
                <>
                  This will delete all existing time slots (for the selected date, if any, or all dates) and create new ones based on the activities for this location.
                </>
              ) : dialogMode === 'additional' ? (
                <>
                  Select an activity to generate additional time slots for:
                  <select
                    className="mt-2 w-full p-2 border rounded"
                    value={selectedActivity || ''}
                    onChange={(e) => setSelectedActivity(e.target.value)}
                  >
                    <option value="">Select an activity...</option>
                    {activities?.map(activity => (
                      <option key={activity.id} value={activity.id}>
                        {formatDateForDisplay(activity.start_time.split('T')[0])} - {activity.activity_type} {activity.level ? `(${activity.level})` : ''}
                      </option>
                    ))}
                  </select>
                </>
              ) : (
                <>
                  This will create time slots for all activities in this location using their specified slot durations.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={closeDialog}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={dialogMode === 'regenerate' ? handleRegenerateTimeSlots : 
                      dialogMode === 'additional' ? handleGenerateAdditionalSlots :
                      handleRegenerateTimeSlots}
              disabled={(dialogMode === 'regenerate' && timeSlots.some(slot => slot.is_booked)) ||
                       (dialogMode === 'additional' && !selectedActivity)}
            >
              {dialogMode === 'generate' && 'Generate'}
              {dialogMode === 'regenerate' && 'Regenerate'}
              {dialogMode === 'additional' && 'Generate Additional'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
