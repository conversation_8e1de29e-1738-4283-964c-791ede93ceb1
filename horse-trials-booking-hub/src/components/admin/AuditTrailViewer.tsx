import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar, Search, Filter, Eye, Clock, User, Database, AlertCircle } from 'lucide-react';
import { useFilteredAuditTrail, formatAuditRecord, getChangeDescription, AuditRecord } from '@/hooks/useAuditTrail';
import { useToast } from '@/hooks/use-toast';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRole } from '@/hooks/useUserRoles';

interface AuditTrailViewerProps {
  recordId?: string;
  tableName?: string;
  userId?: string;
}

export const AuditTrailViewer: React.FC<AuditTrailViewerProps> = ({ 
  recordId, 
  tableName, 
  userId 
}) => {
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);
  const { toast } = useToast();
  
  const [filters, setFilters] = useState({
    table_name: tableName || '',
    user_id: userId || '',
    action: '' as 'INSERT' | 'UPDATE' | 'DELETE' | '',
    date_from: '',
    date_to: '',
    limit: 50
  });

  const [selectedRecord, setSelectedRecord] = useState<AuditRecord | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const { data: auditRecords, isLoading, error } = useFilteredAuditTrail({
    ...filters,
    action: filters.action || undefined
  });

  const isSuperAdmin = userRole === 'super_admin';

  if (!isSuperAdmin) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center text-gray-500">
            <AlertCircle className="h-8 w-8 mr-2" />
            <span>Only super admins can access audit trails</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleViewDetails = (record: AuditRecord) => {
    setSelectedRecord(record);
    setShowDetails(true);
  };

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'object') return JSON.stringify(value, null, 2);
    return String(value);
  };

  const getFieldDiff = (oldValues: any, newValues: any, changedFields: string[]) => {
    if (!oldValues || !newValues) return null;

    return changedFields.map(field => ({
      field,
      oldValue: formatValue(oldValues[field]),
      newValue: formatValue(newValues[field])
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="h-5 w-5 mr-2" />
            Audit Trail
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            View detailed audit logs of all changes made to the system. Only super admins can access this information.
          </p>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="table-filter">Table</Label>
              <Select value={filters.table_name} onValueChange={(value) => handleFilterChange('table_name', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All tables" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All tables</SelectItem>
                  <SelectItem value="events">Events</SelectItem>
                  <SelectItem value="time_slots">Time Slots</SelectItem>
                  <SelectItem value="bookings">Bookings</SelectItem>
                  <SelectItem value="events_organizers">Event Organizers</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="action-filter">Action</Label>
              <Select value={filters.action} onValueChange={(value) => handleFilterChange('action', value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="All actions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All actions</SelectItem>
                  <SelectItem value="INSERT">Created</SelectItem>
                  <SelectItem value="UPDATE">Updated</SelectItem>
                  <SelectItem value="DELETE">Deleted</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="date-from">Date From</Label>
              <Input
                type="date"
                value={filters.date_from}
                onChange={(e) => handleFilterChange('date_from', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="date-to">Date To</Label>
              <Input
                type="date"
                value={filters.date_to}
                onChange={(e) => handleFilterChange('date_to', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="limit">Limit</Label>
              <Select value={String(filters.limit)} onValueChange={(value) => handleFilterChange('limit', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="25">25 records</SelectItem>
                  <SelectItem value="50">50 records</SelectItem>
                  <SelectItem value="100">100 records</SelectItem>
                  <SelectItem value="200">200 records</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button 
                onClick={() => setFilters({
                  table_name: '',
                  user_id: '',
                  action: '',
                  date_from: '',
                  date_to: '',
                  limit: 50
                })}
                variant="outline"
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audit Records */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Audit Records</span>
            {auditRecords && (
              <Badge variant="secondary">
                {auditRecords.length} records
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading && (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center p-8 text-red-600">
              <AlertCircle className="h-8 w-8 mr-2" />
              <span>Error loading audit records: {error.message}</span>
            </div>
          )}

          {auditRecords && auditRecords.length === 0 && (
            <div className="flex items-center justify-center p-8 text-gray-500">
              <span>No audit records found</span>
            </div>
          )}

          {auditRecords && auditRecords.length > 0 && (
            <div className="space-y-4">
              {auditRecords.map((record) => {
                const formattedRecord = formatAuditRecord(record);
                return (
                  <div key={record.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge className={formattedRecord.actionColor}>
                            {formattedRecord.actionLabel}
                          </Badge>
                          <span className="text-sm text-gray-600">
                            {getChangeDescription(record)}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-500" />
                            <span>{record.user_email || 'Unknown'}</span>
                          </div>
                          <div className="flex items-center">
                            <Database className="h-4 w-4 mr-2 text-gray-500" />
                            <span>{record.table_name}</span>
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2 text-gray-500" />
                            <span>{formattedRecord.formattedDate}</span>
                          </div>
                        </div>

                        {record.changed_fields && record.changed_fields.length > 0 && (
                          <div className="mt-2">
                            <span className="text-sm text-gray-600">Changed fields: </span>
                            <span className="text-sm font-medium">{formattedRecord.changedFieldsText}</span>
                          </div>
                        )}

                        {record.reason && (
                          <div className="mt-2">
                            <span className="text-sm text-gray-600">Reason: </span>
                            <span className="text-sm font-medium">{record.reason}</span>
                          </div>
                        )}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(record)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Details
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Details Modal */}
      {showDetails && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Audit Record Details</h3>
              <Button variant="outline" onClick={() => setShowDetails(false)}>
                Close
              </Button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="font-medium">Action</Label>
                  <p>{selectedRecord.action}</p>
                </div>
                <div>
                  <Label className="font-medium">Table</Label>
                  <p>{selectedRecord.table_name}</p>
                </div>
                <div>
                  <Label className="font-medium">Record ID</Label>
                  <p className="font-mono text-sm">{selectedRecord.record_id}</p>
                </div>
                <div>
                  <Label className="font-medium">User</Label>
                  <p>{selectedRecord.user_email || 'Unknown'}</p>
                </div>
                <div>
                  <Label className="font-medium">User Role</Label>
                  <p>{selectedRecord.user_role || 'Unknown'}</p>
                </div>
                <div>
                  <Label className="font-medium">Timestamp</Label>
                  <p>{new Date(selectedRecord.created_at).toLocaleString()}</p>
                </div>
              </div>

              {selectedRecord.reason && (
                <div>
                  <Label className="font-medium">Reason</Label>
                  <p>{selectedRecord.reason}</p>
                </div>
              )}

              {selectedRecord.action === 'UPDATE' && selectedRecord.changed_fields && (
                <div>
                  <Label className="font-medium">Changes</Label>
                  <div className="mt-2 space-y-2">
                    {getFieldDiff(selectedRecord.old_values, selectedRecord.new_values, selectedRecord.changed_fields)?.map((diff, index) => (
                      <div key={index} className="border rounded p-3 bg-gray-50">
                        <div className="font-medium text-sm">{diff.field}</div>
                        <div className="grid grid-cols-2 gap-4 mt-1">
                          <div>
                            <span className="text-xs text-red-600">Old:</span>
                            <pre className="text-xs mt-1 bg-red-50 p-2 rounded">{diff.oldValue}</pre>
                          </div>
                          <div>
                            <span className="text-xs text-green-600">New:</span>
                            <pre className="text-xs mt-1 bg-green-50 p-2 rounded">{diff.newValue}</pre>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {(selectedRecord.action === 'INSERT' || selectedRecord.action === 'DELETE') && (
                <div>
                  <Label className="font-medium">
                    {selectedRecord.action === 'INSERT' ? 'New Values' : 'Deleted Values'}
                  </Label>
                  <pre className="mt-2 bg-gray-50 p-4 rounded text-sm overflow-x-auto">
                    {formatValue(selectedRecord.action === 'INSERT' ? selectedRecord.new_values : selectedRecord.old_values)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 