
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { usePricingSettings, useUpdatePricingSettings, PricingSettings } from '@/hooks/usePricingSettings';
import { Loader2, DollarSign, Clock } from 'lucide-react';

export const ActivityPricingSettings = () => {
  const { data: pricingSettings, isLoading } = usePricingSettings();
  const updatePricingSettings = useUpdatePricingSettings();
  const [formData, setFormData] = useState<PricingSettings>({
    dressage_price: 25,
    show_jumping_price: 30,
    cross_country_price: 35,
    currency_symbol: '$',
    cart_retention_minutes: 15
  });

  useEffect(() => {
    if (pricingSettings) {
      setFormData(pricingSettings);
    }
  }, [pricingSettings]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await updatePricingSettings.mutateAsync(formData);
  };

  const handleInputChange = (field: keyof PricingSettings, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="w-6 h-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <DollarSign className="w-5 h-5" />
          <span>Activity Pricing & Cart Settings</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Pricing Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Activity Pricing</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="currency_symbol">Currency Symbol</Label>
                <Input
                  id="currency_symbol"
                  value={formData.currency_symbol}
                  onChange={(e) => handleInputChange('currency_symbol', e.target.value)}
                  placeholder="$"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="dressage_price">Dressage Price</Label>
                <Input
                  id="dressage_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.dressage_price}
                  onChange={(e) => handleInputChange('dressage_price', parseFloat(e.target.value) || 0)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="show_jumping_price">Show Jumping Price</Label>
                <Input
                  id="show_jumping_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.show_jumping_price}
                  onChange={(e) => handleInputChange('show_jumping_price', parseFloat(e.target.value) || 0)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="cross_country_price">Cross Country Price</Label>
                <Input
                  id="cross_country_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.cross_country_price}
                  onChange={(e) => handleInputChange('cross_country_price', parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>
          </div>

          {/* Cart Settings Section */}
          <div className="space-y-4 border-t pt-4">
            <h3 className="text-lg font-medium flex items-center space-x-2">
              <Clock className="w-5 h-5" />
              <span>Cart Settings</span>
            </h3>
            <div className="space-y-2">
              <Label htmlFor="cart_retention_minutes">Cart Retention Time (minutes)</Label>
              <Input
                id="cart_retention_minutes"
                type="number"
                min="1"
                max="120"
                value={formData.cart_retention_minutes}
                onChange={(e) => handleInputChange('cart_retention_minutes', parseInt(e.target.value) || 15)}
              />
              <p className="text-sm text-gray-600">
                How long items stay in the cart before expiring (1-120 minutes)
              </p>
            </div>
          </div>

          <Button 
            type="submit" 
            disabled={updatePricingSettings.isPending}
            className="w-full"
          >
            {updatePricingSettings.isPending ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Settings'
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
