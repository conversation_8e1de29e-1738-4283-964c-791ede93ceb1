import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { UserManagement } from './UserManagement';

// Mock hooks
vi.mock('@/hooks/useUserRoles', () => ({
  useAllUsers: vi.fn(),
  useUpdateUserRole: vi.fn(),
  useDeleteUser: vi.fn(),
}));

// Mock UI components for stability
vi.mock('@/components/ui/card', () => ({
  Card: ({ children }: { children: React.ReactNode }) => <div data-testid="card">{children}</div>,
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => <div data-testid="card-content" className={className}>{children}</div>,
  CardHeader: ({ children }: { children: React.ReactNode }) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children, className }: { children: React.ReactNode; className?: string }) => <div data-testid="card-title" className={className}>{children}</div>,
}));
vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className }: { children: React.ReactNode; className?: string }) => <span data-testid="badge" className={className}>{children}</span>,
}));
vi.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => <button data-testid="button" {...props}>{children}</button>,
}));
vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => <select data-testid="select" value={value} onChange={e => onValueChange(e.target.value)}>{children}</select>,
  SelectTrigger: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  SelectValue: () => <option data-testid="select-value" />, // Not rendered directly
  SelectContent: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  SelectItem: ({ children, value }: { children: React.ReactNode; value: string }) => <option value={value}>{children}</option>,
}));
vi.mock('lucide-react', () => ({
  Users: () => <div data-testid="users-icon">Users</div>,
  User: () => <div data-testid="user-icon">User</div>,
  Trash2: () => <div data-testid="trash-icon">Trash</div>,
}));
vi.mock('@/components/ui/alert-dialog', () => ({
  AlertDialog: ({ children, open }: any) => open ? <div data-testid="alert-dialog">{children}</div> : null,
  AlertDialogAction: ({ children, ...props }: any) => <button data-testid="alert-action" {...props}>{children}</button>,
  AlertDialogCancel: ({ children, ...props }: any) => <button data-testid="alert-cancel" {...props}>{children}</button>,
  AlertDialogContent: ({ children }: any) => <div data-testid="alert-content">{children}</div>,
  AlertDialogDescription: ({ children }: any) => <div data-testid="alert-description">{children}</div>,
  AlertDialogFooter: ({ children }: any) => <div data-testid="alert-footer">{children}</div>,
  AlertDialogHeader: ({ children }: any) => <div data-testid="alert-header">{children}</div>,
  AlertDialogTitle: ({ children }: any) => <div data-testid="alert-title">{children}</div>,
}));

import { useAllUsers, useUpdateUserRole, useDeleteUser } from '@/hooks/useUserRoles';
const mockUseAllUsers = vi.mocked(useAllUsers);
const mockUseUpdateUserRole = vi.mocked(useUpdateUserRole);
const mockUseDeleteUser = vi.mocked(useDeleteUser);

describe('UserManagement', () => {
  const mockUsers = [
    {
      id: '1',
      full_name: 'Alice Smith',
      email: '<EMAIL>',
      phone: '************',
      user_roles: [{ role: 'user' }],
    },
    {
      id: '2',
      full_name: 'Bob Jones',
      email: '<EMAIL>',
      phone: '',
      user_roles: [{ role: 'organizer' }],
    },
    {
      id: '3',
      full_name: '',
      email: '<EMAIL>',
      phone: '',
      user_roles: [{ role: 'super_admin' }],
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseUpdateUserRole.mockReturnValue({ mutate: vi.fn() });
    mockUseDeleteUser.mockReturnValue({ mutate: vi.fn(), isPending: false });
  });

  it('renders loading state', () => {
    mockUseAllUsers.mockReturnValue({ data: undefined, isLoading: true });
    render(<UserManagement />);
    expect(screen.getByText('Loading users...')).toBeInTheDocument();
  });

  it('renders empty state', () => {
    mockUseAllUsers.mockReturnValue({ data: [], isLoading: false });
    render(<UserManagement />);
    expect(screen.getByText('No users found')).toBeInTheDocument();
  });

  it('renders a list of users with correct info and roles', () => {
    mockUseAllUsers.mockReturnValue({ data: mockUsers, isLoading: false });
    render(<UserManagement />);
    expect(screen.getByText('Alice Smith')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('************')).toBeInTheDocument();
    expect(screen.getByText('Rider')).toBeInTheDocument();
    expect(screen.getByText('Event Organizer')).toBeInTheDocument();
    // Use getAllByText to handle multiple elements with the same text
    const superAdminElements = screen.getAllByText('Super Administrator');
    expect(superAdminElements.length).toBeGreaterThan(0);
  });

  it('calls updateUserRole.mutate when changing a user role', async () => {
    const mutate = vi.fn();
    mockUseAllUsers.mockReturnValue({ data: mockUsers, isLoading: false });
    mockUseUpdateUserRole.mockReturnValue({ mutate });
    render(<UserManagement />);
    const selects = screen.getAllByTestId('select');
    await userEvent.selectOptions(selects[0], 'organizer');
    expect(mutate).toHaveBeenCalledWith({ userId: '1', role: 'organizer' });
  });

  it('shows the correct role selected in the dropdown', () => {
    mockUseAllUsers.mockReturnValue({ data: mockUsers, isLoading: false });
    render(<UserManagement />);
    const selects = screen.getAllByTestId('select');
    expect(selects[0].value).toBe('user');
    expect(selects[1].value).toBe('organizer');
    expect(selects[2].value).toBe('super_admin');
  });

  it('opens and closes the delete confirmation dialog', async () => {
    const user = userEvent.setup();
    mockUseAllUsers.mockReturnValue({ data: mockUsers, isLoading: false });
    render(<UserManagement />);
    const deleteButtons = screen.getAllByTestId('button');
    // The last button in each user row is the delete button
    await user.click(deleteButtons[2]);
    expect(screen.getByTestId('alert-dialog')).toBeInTheDocument();
    // Cancel closes the dialog
    await user.click(screen.getByTestId('alert-cancel'));
    await waitFor(() => {
      expect(screen.queryByTestId('alert-dialog')).not.toBeInTheDocument();
    });
  });

  it('calls deleteUser.mutate when confirming delete', async () => {
    const user = userEvent.setup();
    const mutate = vi.fn();
    mockUseAllUsers.mockReturnValue({ data: mockUsers, isLoading: false });
    mockUseDeleteUser.mockReturnValue({ mutate, isPending: false });
    render(<UserManagement />);
    const deleteButtons = screen.getAllByTestId('button');
    await user.click(deleteButtons[2]);
    expect(screen.getByTestId('alert-dialog')).toBeInTheDocument();
    await user.click(screen.getByTestId('alert-action'));
    expect(mutate).toHaveBeenCalledWith('3', expect.anything());
  });

  it('disables delete button and shows loading when deleting', async () => {
    const user = userEvent.setup();
    mockUseAllUsers.mockReturnValue({ data: mockUsers, isLoading: false });
    mockUseDeleteUser.mockReturnValue({ mutate: vi.fn(), isPending: true });
    render(<UserManagement />);
    const deleteButtons = screen.getAllByTestId('button');
    await user.click(deleteButtons[2]);
    const actionButton = screen.getByTestId('alert-action');
    expect(actionButton).toBeDisabled();
    expect(actionButton).toHaveTextContent('Deleting...');
  });

  it('renders all critical UI elements', () => {
    mockUseAllUsers.mockReturnValue({ data: mockUsers, isLoading: false });
    render(<UserManagement />);
    expect(screen.getByTestId('card')).toBeInTheDocument();
    expect(screen.getByTestId('card-header')).toBeInTheDocument();
    expect(screen.getByTestId('card-title')).toBeInTheDocument();
    expect(screen.getAllByTestId('badge').length).toBe(3);
    expect(screen.getAllByTestId('select').length).toBe(3);
    expect(screen.getAllByTestId('button').length).toBeGreaterThanOrEqual(3);
  });
}); 