import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, Calendar, Coffee, Activity as ActivityIcon, Loader2 } from 'lucide-react';
import { useLocationSchedule } from '@/hooks/useLocationSchedule'; // Ensure this path is correct
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';

interface LocationScheduleViewProps {
  locationId: string;
  locationName: string;
  selectedDate?: string;
}

export const LocationScheduleView: React.FC<LocationScheduleViewProps> = ({
  locationId,
  locationName,
  selectedDate,
}) => {
  const { schedule, scheduleStats, formatTime, isLoading, error } = useLocationSchedule(locationId, selectedDate);

  // Group schedule items by date
  const scheduleByDate = useMemo(() => {
    if (!schedule || schedule.length === 0) return {};
    const grouped: Record<string, any[]> = {};
    schedule.forEach(item => {
      const dateObj = new Date(item.start_time);
      const year = dateObj.getUTCFullYear();
      const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0'); // months are 0-indexed
      const day = String(dateObj.getUTCDate()).padStart(2, '0');
      const date = `${year}-${month}-${day}`;
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(item);
    });
    return grouped;
  }, [schedule]);

  const sortedDates = useMemo(() => {
    return Object.keys(scheduleByDate).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
  }, [scheduleByDate]);

  const formatDate = (dateString: string) => {
    const dateWithTime = `${dateString}T12:00:00Z`; // Use noon UTC to avoid timezone shifts affecting the date
    const date = new Date(dateWithTime);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      timeZone: 'UTC'
    });
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours}h ${remainingMinutes}m`
      : `${hours}h`;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg text-green-800">Location Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-green-600 mr-2" />
            Loading schedule...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-lg text-red-800">Error Loading Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600">Failed to load location schedule: {error.message}</p>
        </CardContent>
      </Card>
    );
  }

  if (!schedule || schedule.length === 0) {
    return (
      <Card className="border-amber-200">
        <CardHeader>
          <CardTitle className="text-lg text-amber-800">No Activities Scheduled</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Calendar className="h-12 w-12 text-amber-400 mx-auto mb-4" />
            <p className="text-gray-600">No activities have been scheduled for this location yet.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg text-green-800">
            {locationName} Schedule
          </CardTitle>
          {scheduleStats.start_time && scheduleStats.end_time && (
            <div className="text-sm text-gray-600 flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {formatTime(scheduleStats.start_time)} - {formatTime(scheduleStats.end_time)}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-green-50 p-3 rounded text-center">
            <div className="text-xl font-semibold text-green-800">
              {scheduleStats.activity_count}
            </div>
            <div className="text-xs text-gray-600">Activities</div>
          </div>
          <div className="bg-amber-50 p-3 rounded text-center">
            <div className="text-xl font-semibold text-amber-800">
              {formatDuration(scheduleStats.activity_minutes)}
            </div>
            <div className="text-xs text-gray-600">Activity Time</div>
          </div>
          <div className="bg-blue-50 p-3 rounded text-center">
            <div className="text-xl font-semibold text-blue-800">
              {scheduleStats.break_count}
            </div>
            <div className="text-xs text-gray-600">Breaks</div>
          </div>
        </div>

        {selectedDate ? (
          <>
            <div className="mb-2">
              <h3 className="text-sm font-medium text-gray-700">{formatDate(selectedDate)}</h3>
            </div>
            <div className="space-y-2">
              {scheduleByDate[selectedDate].map((item) => (
                <ScheduleItem
                  key={item.id}
                  item={item}
                  formatTime={formatTime}
                  formatDuration={formatDuration}
                />
              ))}
            </div>
          </>
        ) : (
          <>
            {sortedDates.map(date => (
              <div key={date}>
                <div className="mb-2">
                  <h3 className="text-sm font-medium text-gray-700">{formatDate(date)}</h3>
                </div>
                <div className="space-y-2">
                  {scheduleByDate[date].map((item) => (
                    <ScheduleItem
                      key={item.id}
                      item={item}
                      formatTime={formatTime}
                      formatDuration={formatDuration}
                    />
                  ))}
                </div>
              </div>
            ))}
          </>
        )}
      </CardContent>
    </Card>
  );
};

const ScheduleItem: React.FC<{ item: any, formatTime: (time: string) => string, formatDuration: (minutes: number) => string }> = ({ item, formatTime, formatDuration }) => {
  return (
    <div
      className={`p-3 rounded-md flex items-start ${
        item.type === 'activity'
          ? 'bg-green-50 border-l-4 border-green-500'
          : 'bg-blue-50 border-l-4 border-blue-400'
      }`}
    >
      <div className="mr-3 mt-1">
        {item.type === 'activity' ? (
          <ActivityIcon className="h-5 w-5 text-green-600" />
        ) : (
          <Coffee className="h-5 w-5 text-blue-500" />
        )}
      </div>
      <div className="flex-grow">
        <div className="flex justify-between items-start">
          <div>
            {item.type === 'activity' ? (
              <>
                <div className="font-medium text-green-800">
                  {item.activity_type?.replace('_', ' ')}
                  {item.level && ` - ${item.level}`}
                </div>
                {item.description && (
                  <div className="text-sm text-gray-600 mt-1">
                    {item.description}
                  </div>
                )}
              </>
            ) : (
              <div className="font-medium text-blue-800">
                Break
              </div>
            )}
          </div>
          <Badge variant={item.type === 'activity' ? 'default' : 'outline'} className={
            item.type === 'activity' ? 'bg-green-100 text-green-800 hover:bg-green-100' : 'text-blue-600'
          }>
            {formatDuration(item.duration_minutes)}
          </Badge>
        </div>
        <div className="text-xs text-gray-500 mt-2 flex items-center">
          <Clock className="h-3 w-3 mr-1" />
          {formatTime(item.start_time)} - {formatTime(item.end_time)}
        </div>
      </div>
    </div>
  );
};