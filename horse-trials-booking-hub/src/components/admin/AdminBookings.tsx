import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Calendar, Search, Trash2, User, Clock, Target, Mail, Phone, Edit, Filter, MapPin, AlertCircle, Loader2, Download } from 'lucide-react';
import { useBookings, useDeleteBooking, useUpdateBooking } from '@/hooks/useBookings';
import { useLocations } from '@/hooks/useLocations';
import { useEventDressageTests } from '@/hooks/useDressageTests';
import { useEventLevels } from '@/hooks/useLevels';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';
import { formatDateForDisplay } from '@/utils/dateUtils';

interface AdminBookingsProps {
  event: any;
  selectedDate?: string;
}

export const AdminBookings: React.FC<AdminBookingsProps> = ({ event, selectedDate }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLocation, setFilterLocation] = useState('all');
  const [filterLevel, setFilterLevel] = useState('all');
  const [filterActivity, setFilterActivity] = useState('all');
  const [filterTest, setFilterTest] = useState('all');
  const [bookingToDelete, setBookingToDelete] = useState<string | null>(null);
  const [editingBooking, setEditingBooking] = useState<any>(null);
  const [groupBy, setGroupBy] = useState<'time' | 'location' | 'name' | 'paid'>('location');
  const [editForm, setEditForm] = useState({
    participant_name: '',
    horse_name: '',
    payer_name: '',
    payer_email: '',
    event_dressage_test_id: 'none'
  });

  const { data: bookings, isLoading: bookingsLoading } = useBookings(event.id);
  const { data: locations } = useLocations(event.id);
  const { data: eventDressageTests } = useEventDressageTests(event.id);
  const { data: eventLevels } = useEventLevels(event.id);
  const deleteBooking = useDeleteBooking();
  const updateBooking = useUpdateBooking();

  // Extract unique values for filters
  const locationNames = locations?.map(location => location.name) || [];
  const levels = [...new Set(
    bookings?.map(booking => booking.time_slot_level || booking.activity_level).filter(Boolean) || []
  )];
  const activityTypes = [...new Set(
    bookings?.map(booking => booking.activity_specific_type).filter(Boolean) || []
  )];
  const dressageTests = [...new Set(
    bookings
      ?.filter(b => b.activity_specific_type === 'dressage')
      .map(b => b.dressage_test_label)
      .filter(Boolean) || []
  )];

  const filteredBookings = bookings?.filter(booking => {
    const matchesSearch = !searchTerm || 
      booking.participant_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.horse_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.payer_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.payer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.booking_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.stripe_payment_id?.toLowerCase().includes(searchTerm.toLowerCase());

    const locationName = booking.location_name;
    const matchesLocation = filterLocation === 'all' || locationName === filterLocation;

    const bookingLevel = booking.time_slot_level || booking.activity_level;
    const matchesLevel = filterLevel === 'all' || bookingLevel === filterLevel;

    const bookingActivity = booking.activity_specific_type;
    const matchesActivity = filterActivity === 'all' || bookingActivity === filterActivity;

    const testLabel = booking.dressage_test_label;
    const matchesTest = filterTest === 'all' || testLabel === filterTest;

    // Filter by selected date if provided
    const matchesDate = !selectedDate || 
      (booking.time_slot_start_time && 
       new Date(booking.time_slot_start_time).toISOString().split('T')[0] === selectedDate);
    
    return matchesSearch && matchesLocation && matchesLevel && matchesActivity && matchesTest && matchesDate;
  }) || [];

  const handleDeleteBooking = async (bookingId: string) => {
    await deleteBooking.mutateAsync(bookingId);
    setBookingToDelete(null);
  };

  const handleEditBooking = (booking: any) => {
    console.log('=== EDIT BOOKING DEBUG ===');
    console.log('Full booking object:', booking);
    
    // Log all possible dressage test related fields
    console.log('event_dressage_test_link_id:', booking.event_dressage_test_link_id);
    console.log('event_dressage_test_id:', booking.event_dressage_test_id);
    console.log('dressage_test_id:', booking.dressage_test_id);
    console.log('All booking keys:', Object.keys(booking));
    
    // Try multiple possible field names for the dressage test ID
    const dressageTestId = 
      booking.event_dressage_test_link_id ||
      booking.event_dressage_test_id ||
      booking.dressage_test_id ||
      null;

    console.log('Selected dressageTestId:', dressageTestId);
    
    setEditingBooking(booking);
    setEditForm({
      participant_name: booking.participant_name || '',
      horse_name: booking.horse_name || '',
      payer_name: booking.payer_name || '',
      payer_email: booking.payer_email || '',
      event_dressage_test_id: dressageTestId ? String(dressageTestId) : 'none'
    });
    
    console.log('Set editForm.event_dressage_test_id to:', dressageTestId ? String(dressageTestId) : 'none');
  };

  const handleUpdateBooking = async () => {
    if (!editingBooking) return;
    await updateBooking.mutateAsync({
      bookingId: editingBooking.booking_id,
      updates: {
        participant_name: editForm.participant_name,
        horse_name: editForm.horse_name,
        payer_name: editForm.payer_name,
        payer_email: editForm.payer_email,
        event_dressage_test_id: editForm.event_dressage_test_id === 'none' ? null : editForm.event_dressage_test_id
      }
    });
    setEditingBooking(null);
    setEditForm({
      participant_name: '',
      horse_name: '',
      payer_name: '',
      payer_email: '',
      event_dressage_test_id: 'none'
    });
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const groupBookingsByType = (bookings: any[]) => {
    return bookings.reduce((groups, booking) => {
      let groupKey: string;

      if (groupBy === 'time') {
        // Group by date/time
        groupKey = booking.time_slot_start_time
          ? new Date(booking.time_slot_start_time).toISOString().split('T')[0]
          : 'Unknown Date';
      } else if (groupBy === 'location') {
        // Group by location
        groupKey = booking.location_name || 'Unknown Location';
      } else if (groupBy === 'name') {
        // Group by first letter of participant name
        groupKey = booking.participant_name 
          ? booking.participant_name.charAt(0).toUpperCase()
          : 'Unknown';
      } else if (groupBy === 'paid') {
        // Group by payment status
        groupKey = booking.payment_status || 'unpaid';
      } else {
        // Default to location grouping
        groupKey = booking.location_name || 'Unknown Location';
      }

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(booking);
      return groups;
    }, {} as Record<string, any[]>);
  };

  const sortedGroups = Object.keys(groupBookingsByType(filteredBookings)).sort((a, b) => {
    if (groupBy === 'time') {
      // Sort dates chronologically
      return new Date(a).getTime() - new Date(b).getTime();
    } else if (groupBy === 'location') {
      // Sort locations alphabetically
      return a.localeCompare(b);
    } else if (groupBy === 'name') {
      // Sort alphabetically by first letter
      return a.localeCompare(b);
    } else if (groupBy === 'paid') {
      // Sort payment status in logical order
      const statusOrder = { 'paid': 1, 'pending': 2, 'unpaid': 3, 'failed': 4 };
      return (statusOrder[a as keyof typeof statusOrder] || 5) - (statusOrder[b as keyof typeof statusOrder] || 5);
    }
    return a.localeCompare(b);
  });

  const getGroupHeader = (groupKey: string) => {
    const groupBookings = groupBookingsByType(filteredBookings)[groupKey];
    if (groupBy === 'time') {
      return `${formatDateForDisplay(groupKey)} - ${groupBookings.length} Booking${groupBookings.length !== 1 ? 's' : ''}`;
    } else if (groupBy === 'location') {
      return `${groupKey} - ${groupBookings.length} Booking${groupBookings.length !== 1 ? 's' : ''}`;
    } else if (groupBy === 'name') {
      const entries = groupBookings;
      const names = entries.map(e => e.participant_name).filter(Boolean).sort();
      if (names.length === 1) {
        return `${groupKey} (${names[0]}) - 1 Entry`;
      } else if (names.length === 2) {
        return `${groupKey} (${names[0]} - ${names[1]}) - 2 Entries`;
      } else if (names.length > 2) {
        return `${groupKey} (${names[0]} - ${names[names.length - 1]}) - ${entries.length} Entries`;
      }
      return `${groupKey} - ${groupBookings.length} Entry${groupBookings.length !== 1 ? 's' : ''}`;
    } else if (groupBy === 'paid') {
      const statusDisplay = groupKey.charAt(0).toUpperCase() + groupKey.slice(1);
      return `${statusDisplay} - ${groupBookings.length} Booking${groupBookings.length !== 1 ? 's' : ''}`;
    }
    return `${groupKey} - ${groupBookings.length} Entry${groupBookings.length !== 1 ? 's' : ''}`;
  };

  const getGroupIcon = () => {
    if (groupBy === 'time') {
      return <Calendar className="w-5 h-5 mr-2" />;
    } else if (groupBy === 'location') {
      return <MapPin className="w-5 h-5 mr-2" />;
    } else if (groupBy === 'name') {
      return <User className="w-5 h-5 mr-2" />;
    } else if (groupBy === 'paid') {
      return <Target className="w-5 h-5 mr-2" />;
    }
    return <MapPin className="w-5 h-5 mr-2" />;
  };

  return (
    <div className="space-y-6">
      <Card className="bg-white border-green-200">
        <CardHeader>
          <CardTitle className="text-xl text-green-800 flex items-center">
            <Calendar className="w-6 h-6 mr-2" />
            Booking Management - {event.name}
            {selectedDate && (
              <Badge variant="outline" className="ml-3 text-sm">
                {new Date(selectedDate).toLocaleDateString()}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filters */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Search by participant, horse, payer info, booking code, or payment details..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <div>
                <Select value={filterLocation} onValueChange={setFilterLocation}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    {locationNames.map((name) => (
                      <SelectItem key={name} value={name}>{name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Select value={filterLevel} onValueChange={setFilterLevel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    {levels.map((level) => (
                      <SelectItem key={level} value={level}>{level}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Select value={filterActivity} onValueChange={setFilterActivity}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by activity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Activities</SelectItem>
                    {activityTypes.map((type) => (
                      <SelectItem key={type} value={type}>{type.replace('_', ' ')}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Select value={filterTest} onValueChange={setFilterTest}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by test" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Tests</SelectItem>
                    {dressageTests.map((test) => (
                      <SelectItem key={test} value={test}>{test}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mt-4 text-sm text-gray-600">
              Showing {filteredBookings.length} booking{filteredBookings.length !== 1 ? 's' : ''}
              {searchTerm && ` matching "${searchTerm}"`}
              {filterLocation !== 'all' && ` in ${filterLocation}`}
              {filterLevel !== 'all' && ` for ${filterLevel} level`}
              {selectedDate && (
                <div className="text-sm text-gray-600">
                  Showing bookings for {formatDateForDisplay(selectedDate)}
                </div>
              )}
            </div>
            <div className="mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm('');
                  setFilterLocation('all');
                  setFilterLevel('all');
                  setFilterActivity('all');
                  setFilterTest('all');
                  setGroupBy('location');
                }}
              >
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-6">
        <div className="mb-4">
          <Select value={groupBy} onValueChange={(val) => setGroupBy(val as 'time' | 'location' | 'name' | 'paid')}>
            <SelectTrigger className="w-fit">
              <SelectValue placeholder="Group by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="time">Group by Date/Time</SelectItem>
              <SelectItem value="location">Group by Location</SelectItem>
              <SelectItem value="name">Group by First Letter of Participant Name</SelectItem>
              <SelectItem value="paid">Group by Payment Status</SelectItem>
            </SelectContent>
          </Select>
        </div>

      <Card className="bg-white border-green-200">
        <CardHeader>
          <CardTitle className="text-lg text-green-800">
            Bookings ({filteredBookings.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {bookingsLoading ? (
            <div className="text-center py-8">Loading bookings...</div>
          ) : filteredBookings.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || filterLocation !== 'all' || filterLevel !== 'all' || filterActivity !== 'all' || filterTest !== 'all' 
                ? 'No bookings match your search criteria.' 
                : 'No bookings found for this event.'}
            </div>
          ) : (
            <div className="space-y-4">
              {sortedGroups.map((groupKey) => {
                const groupBookings = groupBookingsByType(filteredBookings)[groupKey];
                return (
                  <div key={groupKey} className="space-y-4">
                    <div className="flex items-center gap-2 mb-2">
                      {getGroupIcon()}
                      <h3 className="text-lg font-medium text-green-800">{getGroupHeader(groupKey)}</h3>
                    </div>
                    {groupBookings
                      .sort((a, b) => {
                        if (groupBy === 'time') {
                          // Sort by time slot start time within time groups
                          const timeA = a.time_slot_start_time || '00:00';
                          const timeB = b.time_slot_start_time || '00:00';
                          return timeA.localeCompare(timeB);
                        } else if (groupBy === 'location') {
                          // Sort by time within location groups
                          const timeA = a.time_slot_start_time || '00:00';
                          const timeB = b.time_slot_start_time || '00:00';
                          return timeA.localeCompare(timeB);
                        } else if (groupBy === 'name') {
                          // Sort alphabetically by participant name within name groups
                          const nameA = a.participant_name || '';
                          const nameB = b.participant_name || '';
                          return nameA.localeCompare(nameB);
                        } else if (groupBy === 'paid') {
                          // Sort by time within payment status groups
                          const timeA = a.time_slot_start_time || '00:00';
                          const timeB = b.time_slot_start_time || '00:00';
                          return timeA.localeCompare(timeB);
                        }
                        // Default sort by time
                        const timeA = a.time_slot_start_time || '00:00';
                        const timeB = b.time_slot_start_time || '00:00';
                        return timeA.localeCompare(timeB);
                      })
                      .map((booking) => (
                        <div key={booking.booking_id} className="border border-green-200 rounded-lg p-4">
                          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                            <div className="flex-1">
                              <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                                <h3 className="font-medium text-green-800">{booking.participant_name}</h3>
                                <Badge className={getPaymentStatusColor(booking.payment_status || 'pending')}>
                                  {booking.payment_status || 'pending'}
                                </Badge>
                                {booking.booking_code && (
                                  <Badge variant="outline" className="text-xs">
                                    #{booking.booking_code.slice(-8)}
                                  </Badge>
                                )}
                              </div>

                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-gray-600">
                                <div className="flex items-center">
                                  <User className="w-4 h-4 mr-1" />
                                  Horse: {booking.horse_name}
                                </div>
                                
                                {booking.time_slot_start_time && (
                                  <div className="flex items-center">
                                    <Clock className="w-4 h-4 mr-1" />
                                    {new Date(booking.time_slot_start_time).toLocaleString()}
                                  </div>
                                )}
                                
                                {booking.location_name && (
                                  <div className="flex items-center">
                                    <Target className="w-4 h-4 mr-1" />
                                    {booking.location_name}
                                  </div>
                                )}
                              </div>

                              {booking.activity_description && (
                                <div className="mt-2 text-sm text-gray-700">
                                  <strong>Activity:</strong> {booking.activity_description}
                                </div>
                              )}

                              {booking.dressage_test_label && (
                                <div className="mt-2 text-sm text-blue-700">
                                  <strong>Test:</strong> {booking.dressage_test_label}
                                  {booking.dressage_level_name && ` (${booking.dressage_level_name})`}
                                </div>
                              )}

                              {booking.activity_specific_type === 'show_jumping' && (
                                <div className="mt-2 text-sm text-amber-700">
                                  <strong>Level:</strong> {booking.time_slot_level || booking.activity_level}
                                </div>
                              )}

                              {/* Payment Information */}
                              {(booking.total_price || booking.stripe_payment_id || booking.booking_created_at || booking.payer_email || booking.payer_name || booking.booking_code) && (
                                <div className="mt-2 text-sm text-gray-700 border-t border-gray-200 pt-2">
                                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
                                    {booking.payer_name && (
                                      <div>
                                        <strong>Payer:</strong> {booking.payer_name}
                                      </div>
                                    )}
                                    {booking.payer_email && (
                                      <div>
                                        <strong>Email:</strong> {booking.payer_email}
                                      </div>
                                    )}
                                    {booking.booking_code && (
                                      <div>
                                        <strong>Booking Code:</strong> {booking.booking_code}
                                      </div>
                                    )}
                                    {booking.total_price && (
                                      <div>
                                        <strong>Total Price:</strong> ${booking.total_price.toFixed(2)}
                                      </div>
                                    )}
                                    {booking.stripe_payment_id && (
                                      <div>
                                        <strong>Stripe ID:</strong> {booking.stripe_payment_id.slice(-12)}
                                      </div>
                                    )}
                                    {booking.booking_created_at && (
                                      <div>
                                        <strong>Booked:</strong> {formatDateForDisplay(booking.booking_created_at)}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>

                            <div className="flex sm:flex-col gap-2">
                              <Dialog open={!!editingBooking} onOpenChange={(open) => { if (!open) setEditingBooking(null); }}>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-blue-600 border-blue-300 hover:bg-blue-50"
                                    onClick={() => handleEditBooking(booking)}
                                  >
                                    <Edit className="w-4 h-4 mr-1" />
                                    Edit
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="sm:max-w-[425px]">
                                  <DialogHeader>
                                    <DialogTitle>Edit Booking</DialogTitle>
                                    <DialogDescription>
                                      Update the booking details for {booking.participant_name}.
                                    </DialogDescription>
                                  </DialogHeader>
                                  {/* Booking context info */}
                                  {editingBooking && (
                                    <div className="mb-4 p-3 rounded bg-cream-light border border-cream-dark text-sm space-y-1">
                                      <div><strong>Location:</strong> {editingBooking.location_name || 'N/A'}</div>
                                      <div><strong>Time:</strong> {editingBooking.time_slot_start_time
                                        ? `${new Date(editingBooking.time_slot_start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
                                        : 'N/A'}</div>
                                      <div><strong>Activity:</strong> {editingBooking.activity_specific_type || 'N/A'}</div>
                                    </div>
                                  )}
                                  <div className="grid gap-4 py-4">
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label htmlFor="participant_name" className="text-right">
                                        Participant
                                      </Label>
                                      <Input
                                        id="participant_name"
                                        value={editForm.participant_name}
                                        onChange={(e) => setEditForm({...editForm, participant_name: e.target.value})}
                                        className="col-span-3"
                                      />
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label htmlFor="horse_name" className="text-right">
                                        Horse
                                      </Label>
                                      <Input
                                        id="horse_name"
                                        value={editForm.horse_name}
                                        onChange={(e) => setEditForm({...editForm, horse_name: e.target.value})}
                                        className="col-span-3"
                                      />
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label htmlFor="payer_name" className="text-right">
                                        Payer Name
                                      </Label>
                                      <Input
                                        id="payer_name"
                                        value={editForm.payer_name}
                                        onChange={(e) => setEditForm({...editForm, payer_name: e.target.value})}
                                        className="col-span-3"
                                      />
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label htmlFor="payer_email" className="text-right">
                                        Payer Email
                                      </Label>
                                      <Input
                                        id="payer_email"
                                        value={editForm.payer_email}
                                        onChange={(e) => setEditForm({...editForm, payer_email: e.target.value})}
                                        className="col-span-3"
                                      />
                                    </div>
                                    {/* Dressage test dropdown if applicable */}
                                    {editingBooking?.activity_specific_type && editingBooking.activity_specific_type.toLowerCase().trim() === 'dressage' && (
                                      <>
                                        {console.log('=== DRESSAGE DROPDOWN DEBUG ===')}
                                        {console.log('editForm.event_dressage_test_id:', editForm.event_dressage_test_id)}
                                        {console.log('eventDressageTests:', eventDressageTests)}
                                        {console.log('Dropdown options:', eventDressageTests?.map(e => ({ id: e.id, label: e.dressage_test_library?.label })))}
                                        <div className="grid grid-cols-4 items-center gap-4">
                                          <Label htmlFor="dressage_test" className="text-right">
                                            Dressage Test
                                          </Label>
                                          <Select
                                            value={editForm.event_dressage_test_id}
                                            onValueChange={(value) => setEditForm({...editForm, event_dressage_test_id: value})}
                                          >
                                            <SelectTrigger className="col-span-3">
                                              <SelectValue placeholder="Select a test" />
                                            </SelectTrigger>
                                            <SelectContent>
                                              <SelectItem value="none">No test</SelectItem>
                                              {eventDressageTests?.map((eventTest) => (
                                                <SelectItem key={eventTest.id} value={String(eventTest.id)}>
                                                  {eventTest.dressage_test_library?.label}
                                                  {eventTest.dressage_test_library?.levels_library &&
                                                    ` (${eventTest.dressage_test_library.levels_library.name})`
                                                  }
                                                </SelectItem>
                                              ))}
                                            </SelectContent>
                                          </Select>
                                        </div>
                                      </>
                                    )}
                                  </div>
                                  <DialogFooter>
                                    <Button variant="outline" onClick={() => setEditingBooking(null)}>
                                      Cancel
                                    </Button>
                                    <Button
                                      className="bg-gold text-primary hover:bg-gold-hover"
                                      onClick={handleUpdateBooking}
                                      disabled={updateBooking.isPending}
                                    >
                                      {updateBooking.isPending ? 'Updating...' : 'Update Booking'}
                                    </Button>
                                  </DialogFooter>
                                </DialogContent>
                              </Dialog>

                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-red-600 border-red-300 hover:bg-red-50"
                                    disabled={deleteBooking.isPending}
                                  >
                                    <Trash2 className="w-4 h-4 mr-1" />
                                    {deleteBooking.isPending ? 'Deleting...' : 'Delete'}
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Delete Booking</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to delete this booking for {booking.participant_name}? This action cannot be undone.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDeleteBooking(booking.booking_id)}
                                      className="bg-red-600 hover:bg-red-700"
                                    >
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </div>
  );
};
