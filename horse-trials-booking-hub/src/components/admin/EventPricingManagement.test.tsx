import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { EventPricingManagement } from './EventPricingManagement';
import { useEventPricing, useCreateEventPricing, useUpdateEventPricing, useDeleteEventPricing } from '@/hooks/useEventPricing';
import { usePricingSettings } from '@/hooks/usePricingSettings';

// Mock the hooks
vi.mock('@/hooks/useEventPricing');
vi.mock('@/hooks/usePricingSettings');

// Mock the Select component to avoid Radix UI issues
vi.mock('@/components/ui/select', () => {
  const React = require('react');
  return {
    Select: ({ children, value, onValueChange, ...props }: any) => {
      const [internalValue, setInternalValue] = React.useState(value || '');
      React.useEffect(() => { setInternalValue(value || ''); }, [value]);
      return (
        <select
          value={internalValue}
          onChange={e => {
            setInternalValue(e.target.value);
            onValueChange?.(e.target.value);
          }}
          data-testid={props['data-testid'] || 'activity-type-select'}
        >
          {children}
        </select>
      );
    },
    SelectContent: ({ children }: any) => <>{children}</>,
    SelectItem: ({ children, value }: any) => <option value={value}>{children}</option>,
    SelectTrigger: ({ children, ...props }: any) => {
      const { 'data-testid': _omit, ...rest } = props;
      return <div {...rest}>{children}</div>;
    },
    SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
  };
});

const mockUseEventPricing = vi.mocked(useEventPricing);
const mockUseCreateEventPricing = vi.mocked(useCreateEventPricing);
const mockUseUpdateEventPricing = vi.mocked(useUpdateEventPricing);
const mockUseDeleteEventPricing = vi.mocked(useDeleteEventPricing);
const mockUsePricingSettings = vi.mocked(usePricingSettings);

const mockPricing = [
  {
    id: '1',
    event_id: 'event-1',
    activity_type: 'dressage',
    price: 45.00,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    event_id: 'event-1',
    activity_type: 'show_jumping',
    price: 55.00,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

const createMockMutation = (isLoading = false, isError = false, error = null) => ({
  mutate: vi.fn(),
  isLoading,
  isError,
  error,
  isSuccess: false,
  data: null,
  reset: vi.fn()
});

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('EventPricingManagement', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mocks
    mockUseEventPricing.mockReturnValue({
      data: mockPricing,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    });

    mockUseCreateEventPricing.mockReturnValue(createMockMutation());
    mockUseUpdateEventPricing.mockReturnValue(createMockMutation());
    mockUseDeleteEventPricing.mockReturnValue(createMockMutation());
    mockUsePricingSettings.mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    });
  });

  describe('Rendering', () => {
    it('renders the component with title and add button', () => {
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      expect(screen.getByText('Event Pricing')).toBeInTheDocument();
      expect(screen.getByText('Add Pricing')).toBeInTheDocument();
    });

    it('displays pricing items when data is available', () => {
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      expect(screen.getByText('dressage')).toBeInTheDocument();
      expect(screen.getByText('show jumping')).toBeInTheDocument();
      expect(screen.getByText('$45.00')).toBeInTheDocument();
      expect(screen.getByText('$55.00')).toBeInTheDocument();
    });

    it('shows empty state when no pricing data', () => {
      mockUseEventPricing.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
        refetch: vi.fn()
      });

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      expect(screen.getByText('No pricing found')).toBeInTheDocument();
      expect(screen.getByText('Get started by adding a price for an activity.')).toBeInTheDocument();
    });

    it('shows loading state', () => {
      mockUseEventPricing.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
        refetch: vi.fn()
      });

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      // Check for the loading spinner by its class
      expect(document.querySelector('.animate-spin')).toBeInTheDocument();
    });

    it('shows error state', () => {
      mockUseEventPricing.mockReturnValue({
        data: null,
        isLoading: false,
        error: { message: 'Failed to load pricing' },
        refetch: vi.fn()
      });

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      expect(screen.getByText('Error loading pricing: Failed to load pricing')).toBeInTheDocument();
    });
  });

  describe('Dialog Interactions', () => {
    it('opens add dialog when Add Pricing button is clicked', async () => {
      const user = userEvent.setup();
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const addButtons = screen.getAllByText('Add Pricing');
      await user.click(addButtons[0]);
      
      expect(screen.getByTestId('dialog-title')).toHaveTextContent('Add Pricing');
      expect(screen.getByText('Set the price for a specific activity at this event.')).toBeInTheDocument();
    });

    it('opens edit dialog when edit button is clicked', async () => {
      const user = userEvent.setup();
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const editButtons = screen.getAllByTestId('edit-pricing');
      await user.click(editButtons[0]);
      
      expect(screen.getByTestId('dialog-title')).toHaveTextContent('Edit Pricing');
    });

    it('closes dialog when cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const addButtons = screen.getAllByText('Add Pricing');
      await user.click(addButtons[0]);
      expect(screen.getByTestId('dialog-title')).toHaveTextContent('Add Pricing');
      
      await user.click(screen.getByTestId('cancel-pricing-btn'));
      
      await waitFor(() => {
        expect(screen.queryByTestId('dialog-title')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Interactions', () => {
    it('allows selecting activity type', async () => {
      const user = userEvent.setup();
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const addButtons = screen.getAllByText('Add Pricing');
      await user.click(addButtons[0]);
      
      const activitySelect = screen.getByTestId('activity-type-select');
      await user.selectOptions(activitySelect, 'dressage');
      
      expect(activitySelect).toHaveValue('dressage');
    });

    it('allows entering price', async () => {
      const user = userEvent.setup();
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const addButtons = screen.getAllByText('Add Pricing');
      await user.click(addButtons[0]);
      
      const priceInput = screen.getByTestId('price-input');
      await user.type(priceInput, '50');
      
      expect(priceInput).toHaveValue(50);
    });

    it('submits form with correct data', async () => {
      const user = userEvent.setup();
      const mockCreateMutation = createMockMutation();
      mockUseCreateEventPricing.mockReturnValue(mockCreateMutation as any);

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const addButtons = screen.getAllByText('Add Pricing');
      await user.click(addButtons[0]);
      
      const activitySelect = screen.getByTestId('activity-type-select');
      await user.selectOptions(activitySelect, 'dressage');
      
      const priceInput = screen.getByTestId('price-input');
      await user.type(priceInput, '50');
      
      await user.click(screen.getByTestId('save-pricing-btn'));
      
      expect(mockCreateMutation.mutate).toHaveBeenCalledWith(
        {
          eventId: 'event-1',
          activity_type: 'dressage',
          price: 50
        },
        expect.any(Object)
      );
    });

    it('updates existing pricing', async () => {
      const user = userEvent.setup();
      const mockUpdateMutation = createMockMutation();
      mockUseUpdateEventPricing.mockReturnValue(mockUpdateMutation as any);

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const editButtons = screen.getAllByTestId('edit-pricing');
      await user.click(editButtons[0]);
      
      const priceInput = screen.getByTestId('price-input');
      await user.clear(priceInput);
      await user.type(priceInput, '60');
      
      await user.click(screen.getByTestId('save-pricing-btn'));
      
      expect(mockUpdateMutation.mutate).toHaveBeenCalledWith(
        {
          id: '1',
          eventId: 'event-1',
          activity_type: 'dressage',
          price: 60
        },
        expect.any(Object)
      );
    });

    it('deletes pricing when confirmed', async () => {
      const user = userEvent.setup();
      const mockDeleteMutation = createMockMutation();
      mockUseDeleteEventPricing.mockReturnValue(mockDeleteMutation as any);

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const deleteButtons = screen.getAllByTestId('delete-pricing');
      await user.click(deleteButtons[0]);
      
      await user.click(screen.getByText('Delete'));
      
      expect(mockDeleteMutation.mutate).toHaveBeenCalledWith({
        id: '1',
        eventId: 'event-1'
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading state during create', async () => {
      const user = userEvent.setup();
      mockUseCreateEventPricing.mockReturnValue(createMockMutation(true) as any);

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const addButtons = screen.getAllByText('Add Pricing');
      await user.click(addButtons[0]);
      await user.selectOptions(screen.getByTestId('activity-type-select'), 'dressage');
      await user.type(screen.getByTestId('price-input'), '50');
      await user.click(screen.getByTestId('save-pricing-btn'));
      
      const saveBtn = screen.getByTestId('save-pricing-btn');
      expect(saveBtn).toBeDisabled();
    });

    it('shows loading state during update', async () => {
      const user = userEvent.setup();
      mockUseUpdateEventPricing.mockReturnValue(createMockMutation(true) as any);

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const editButtons = screen.getAllByTestId('edit-pricing');
      await user.click(editButtons[0]);
      await user.type(screen.getByTestId('price-input'), '60');
      await user.click(screen.getByTestId('save-pricing-btn'));
      
      const saveBtn = screen.getByTestId('save-pricing-btn');
      expect(saveBtn).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('handles create mutation error gracefully', async () => {
      const user = userEvent.setup();
      mockUseCreateEventPricing.mockReturnValue(createMockMutation(false, true, new Error('Create failed')) as any);

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const addButtons = screen.getAllByText('Add Pricing');
      await user.click(addButtons[0]);
      await user.click(screen.getByTestId('save-pricing-btn'));
      
      // Component should still be functional despite mutation error
      expect(screen.getByTestId('dialog-title')).toBeInTheDocument();
    });

    it('handles update mutation error gracefully', async () => {
      const user = userEvent.setup();
      mockUseUpdateEventPricing.mockReturnValue(createMockMutation(false, true, new Error('Update failed')) as any);

      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const editButtons = screen.getAllByTestId('edit-pricing');
      await user.click(editButtons[0]);
      await user.click(screen.getByTestId('save-pricing-btn'));
      
      // Component should still be functional despite mutation error
      expect(screen.getByTestId('dialog-title')).toBeInTheDocument();
    });
  });

  describe('UI Regression', () => {
    it('displays activity types with proper formatting', () => {
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      expect(screen.getByText('dressage')).toBeInTheDocument();
      expect(screen.getByText('show jumping')).toBeInTheDocument();
    });

    it('displays prices with proper formatting', () => {
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      expect(screen.getByText('$45.00')).toBeInTheDocument();
      expect(screen.getByText('$55.00')).toBeInTheDocument();
    });

    it('shows event-specific badge for pricing items', () => {
      renderWithQueryClient(<EventPricingManagement eventId="event-1" />);
      
      const badges = screen.getAllByText('Event-specific');
      expect(badges).toHaveLength(2);
    });
  });
}); 