import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Plus, Edit, Trash, X } from 'lucide-react';
import { useEvents, useCreateEvent, useUpdateEvent, Event } from '@/hooks/useEvents';
import { useLocations } from '@/hooks/useLocations'; // Changed from useArenas
import { useBookings } from '@/hooks/useBookings';
import { useLevels } from '@/hooks/useLevels';
import { EventLevelsManagement } from './EventLevelsManagement';
import { EventDressageTestsManagement } from './EventDressageTestsManagement';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface EventManagementProps {
  onEventSelect: (eventId: string) => void;
}

const formatDateForDisplay = (dateString) => {
  if (!dateString) return '';

  console.log(`Formatting date for display: ${dateString}`);

  // Extract just the date part if there's a time component
  const datePart = dateString.split('T')[0];

  // Check if it's in YYYY-MM-DD format
  const dateRegex = /^(\d{4})-(\d{2})-(\d{2})$/;
  const match = datePart.match(dateRegex);

  if (match) {
    const [_, year, month, day] = match;
    // Convert to MM/DD/YYYY format
    // Remove leading zeros but ensure it's treated as a number
    const formattedMonth = parseInt(month, 10);
    const formattedDay = parseInt(day, 10);

    console.log(`Parsed date parts: year=${year}, month=${formattedMonth}, day=${formattedDay}`);

    return `${formattedMonth}/${formattedDay}/${year}`;
  }

  console.error(`Date string not in expected format: ${dateString}`);
  return dateString;
};

const formatDateForStorage = (dateString) => {
  if (!dateString) return '';

  console.log(`Formatting date for storage: ${dateString}`);

  // If it's already in YYYY-MM-DD format, return as is
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    console.log(`Date is already in YYYY-MM-DD format: ${dateString}`);
    return dateString;
  }

  // Parse the date string
  const parts = dateString.split('-');
  if (parts.length !== 3) {
    console.error(`Invalid date format: ${dateString}`);
    return '';
  }

  // Ensure the date is in YYYY-MM-DD format
  const year = parts[0];
  const month = parts[1].padStart(2, '0');
  const day = parts[2].padStart(2, '0');

  const formattedDate = `${year}-${month}-${day}`;
  console.log(`Formatted date for storage: ${formattedDate}`);

  return formattedDate;
};

// Super detailed logging function to understand exactly what's happening
const logDateDetails = (label, dateString) => {
  console.log(`=== ${label} ===`);
  console.log(`Raw value: "${dateString}"`);
  console.log(`Type: ${typeof dateString}`);
  console.log(`Length: ${dateString?.length}`);

  // Log each character and its code
  if (dateString) {
    console.log('Character by character:');
    for (let i = 0; i < dateString.length; i++) {
      console.log(`  Pos ${i}: '${dateString[i]}' (code: ${dateString.charCodeAt(i)})`);
    }
  }

  // Try different parsing methods
  try {
    const dateObj = new Date(dateString);
    console.log(`As Date object: ${dateObj}`);
    console.log(`isValid: ${!isNaN(dateObj.getTime())}`);
    console.log(`UTC string: ${dateObj.toUTCString()}`);
    console.log(`ISO string: ${dateObj.toISOString()}`);
    console.log(`Local string: ${dateObj.toString()}`);
    console.log(`getTimezoneOffset: ${dateObj.getTimezoneOffset()}`);
  } catch (e) {
    console.log(`Error creating Date object: ${e.message}`);
  }

  console.log('---');
};

export const EventManagement: React.FC<EventManagementProps> = ({ onEventSelect }) => {
  console.log('🔴🔴🔴 EVENT MANAGEMENT COMPONENT LOADED 🔴🔴🔴');

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingEventId, setEditingEventId] = useState<string | null>(null);
  const [selectedEventForConfig, setSelectedEventForConfig] = useState<Event | null>(null);
  const [newEvent, setNewEvent] = useState({
    name: '',
    start_date: '',
    end_date: '',
    schooling_start_date: '',
    schooling_end_date: '',
    event_type: 'horse_trials' as const
  });
  const [editEvent, setEditEvent] = useState({
    name: '',
    start_date: '',
    end_date: '',
    schooling_start_date: '',
    schooling_end_date: ''
  });

  const [selectedLevels, setSelectedLevels] = useState<string[]>([]);
  const [selectedDiscipline, setSelectedDiscipline] = useState('all');

  const { data: events, isLoading } = useEvents();
  const { data: allLevels } = useLevels();
  const createEvent = useCreateEvent();
  const updateEvent = useUpdateEvent();

  // Filter levels by discipline
  const filteredLevels = allLevels?.filter(level => 
    selectedDiscipline === 'all' || level.discipline === selectedDiscipline
  ) || [];

  // Get available levels (not already selected)
  const availableLevels = filteredLevels.filter(level => 
    !selectedLevels.includes(level.id)
  );

  // Get selected level objects
  const selectedLevelObjects = allLevels?.filter(level => 
    selectedLevels.includes(level.id)
  ) || [];

  const handleAddLevel = (levelId: string) => {
    if (!selectedLevels.includes(levelId)) {
      setSelectedLevels([...selectedLevels, levelId]);
    }
  };

  const handleRemoveLevel = (levelId: string) => {
    setSelectedLevels(selectedLevels.filter(id => id !== levelId));
  };

  // Add this effect to debug event dates
  useEffect(() => {
    if (events && events.length > 0) {
      console.log('===== DEBUG - EVENT DATES =====');
      events.forEach(event => {
        console.log(`Event: ${event.name}`);
        console.log(`Raw start_date: ${event.start_date}`);
        console.log(`Raw end_date: ${event.end_date}`);
        console.log(`Raw schooling_start_date: ${event.schooling_start_date}`);
        console.log(`Raw schooling_end_date: ${event.schooling_end_date}`);
        console.log(`Formatted start_date: ${formatDateForDisplay(event.start_date)}`);
        console.log(`Formatted end_date: ${formatDateForDisplay(event.end_date)}`);
        console.log(`Formatted schooling_start_date: ${formatDateForDisplay(event.schooling_start_date)}`);
        console.log(`Formatted schooling_end_date: ${formatDateForDisplay(event.schooling_end_date)}`);
        console.log('---');
      });
    }
  }, [events]);

  // Add this effect to debug event dates in detail
  useEffect(() => {
    if (events && events.length > 0) {
      console.log('===== DETAILED DATE DEBUG =====');
      events.forEach(event => {
        console.log(`Event: ${event.name}`);

        // Log raw date strings
        console.log(`Raw start_date: "${event.start_date}" (type: ${typeof event.start_date})`);
        console.log(`Raw end_date: "${event.end_date}" (type: ${typeof event.end_date})`);

        // Try parsing with Date and show results
        if (event.start_date) {
          const startDate = new Date(event.start_date);
          console.log(`start_date as Date: ${startDate}`);
          console.log(`start_date toISOString: ${startDate.toISOString()}`);
          console.log(`start_date getDate: ${startDate.getDate()}`);
          console.log(`start_date getMonth: ${startDate.getMonth() + 1}`);
          console.log(`start_date getFullYear: ${startDate.getFullYear()}`);
          console.log(`start_date getTimezoneOffset: ${startDate.getTimezoneOffset()}`);
        }

        // Try our new formatter
        console.log(`Formatted with new function: ${formatDateForDisplay(event.start_date)}`);

        console.log('---');
      });
    }
  }, [events]);

  // Add this to your component
  useEffect(() => {
    if (events && events.length > 0) {
      console.log('======= SUPER DETAILED DATE ANALYSIS =======');
      const event = events[0]; // Just analyze the first event

      logDateDetails('start_date', event.start_date);
      logDateDetails('end_date', event.end_date);
      logDateDetails('schooling_start_date', event.schooling_start_date);
      logDateDetails('schooling_end_date', event.schooling_end_date);
    }
  }, [events]);

  const handleCreateEvent = () => {
    createEvent.mutate({
      event: newEvent,
      levelIds: selectedLevels
    }, {
      onSuccess: () => {
        setShowCreateForm(false);
        setNewEvent({
          name: '',
          start_date: '',
          end_date: '',
          schooling_start_date: '',
          schooling_end_date: '',
          event_type: 'horse_trials'
        });
        setSelectedLevels([]);
        setSelectedDiscipline('all');
      }
    });
  };

  const handleEditClick = (event: Event) => {
    setEditingEventId(event.id);
    setEditEvent({
      name: event.name,
      start_date: event.start_date,
      end_date: event.end_date,
      schooling_start_date: event.schooling_start_date,
      schooling_end_date: event.schooling_end_date
    });
  };

  const handleCancelEdit = () => {
    setEditingEventId(null);
    setEditEvent({
      name: '',
      start_date: '',
      end_date: '',
      schooling_start_date: '',
      schooling_end_date: ''
    });
  };

  const handleUpdateEvent = () => {
    if (!editingEventId) return;

    updateEvent.mutate({
      id: editingEventId,
      ...editEvent
    }, {
      onSuccess: () => {
        setEditingEventId(null);
        setEditEvent({
          name: '',
          start_date: '',
          end_date: '',
          schooling_start_date: '',
          schooling_end_date: ''
        });
      }
    });
  };

  const getEventTypeLabel = (eventType: string) => {
    switch (eventType) {
      case 'horse_trials':
        return 'Horse Trials';
      case 'dressage':
        return 'Dressage';
      case 'hunter_jumper':
        return 'Hunter/Jumper';
      default:
        return eventType;
    }
  };

  const getEventTypeBadgeColor = (eventType: string) => {
    switch (eventType) {
      case 'horse_trials':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'dressage':
        return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'hunter_jumper':
        return 'bg-orange-100 text-orange-800 border-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const EventCard: React.FC<{ event: Event }> = ({ event }) => {
    console.log(`🔵🔵🔵 EVENT CARD RENDERING FOR: ${event.name} 🔵🔵🔵`);
    console.log('EVENT DATA:', event);

    const { data: locations } = useLocations(event.id); // Changed from arenas to locations
    const { data: bookings } = useBookings(event.id);
    const isEditing = editingEventId === event.id;

    // Store the correct dates from the edit form
    const [correctDates, setCorrectDates] = useState({
      start_date: '',
      end_date: '',
      schooling_start_date: '',
      schooling_end_date: ''
    });

    // When the component mounts or the event changes, update the correct dates
    useEffect(() => {
      if (event) {
        // These are the raw dates from the API, which are correct in the edit form
        setCorrectDates({
          start_date: event.start_date,
          end_date: event.end_date,
          schooling_start_date: event.schooling_start_date,
          schooling_end_date: event.schooling_end_date
        });

        console.log('Stored correct dates for display:');
        console.log(`start_date: ${event.start_date}`);
        console.log(`end_date: ${event.end_date}`);
        console.log(`schooling_start_date: ${event.schooling_start_date}`);
        console.log(`schooling_end_date: ${event.schooling_end_date}`);
      }
    }, [event]);

    // Simple function to format dates as MM/DD/YYYY without using Date object
    const formatDateString = (dateStr) => {
      if (!dateStr) {
        console.log(`⚠️ formatDateString called with empty value`);
        return '';
      }

      console.log(`🟡 FORMATTING DATE: "${dateStr}"`);

      // Extract just the date part (YYYY-MM-DD)
      const datePart = dateStr.split('T')[0];
      console.log(`  Date part: "${datePart}"`);

      // Split into year, month, day
      const [year, month, day] = datePart.split('-');
      console.log(`  Parts: year="${year}", month="${month}", day="${day}"`);

      // Return as MM/DD/YYYY with no leading zeros
      const result = `${parseInt(month, 10)}/${parseInt(day, 10)}/${year}`;
      console.log(`  RESULT: "${result}"`);

      return result;
    };

    console.log(`🟢🟢🟢 DATES THAT WILL BE DISPLAYED FOR ${event.name}:`);
    console.log(`Competition: ${formatDateString(correctDates.start_date)} - ${formatDateString(correctDates.end_date)}`);
    console.log(`Schooling: ${formatDateString(correctDates.schooling_start_date)} - ${formatDateString(correctDates.schooling_end_date)}`);

    return (
      <div className="flex flex-col">
        <div>i am here</div>
        <Card className="border-green-200">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-lg text-green-800">{event.name}</CardTitle>
                  <Badge variant="outline" className={getEventTypeBadgeColor(event.event_type)}>
                    {getEventTypeLabel(event.event_type)}
                  </Badge>
                  {event.is_active ? (
                    <Badge variant="outline" className="text-green-700 border-green-300">
                      Active
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-gray-400 border-gray-300">
                      Inactive
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-green-600" />
                  <span className="font-medium">Competition:</span>
                </div>
                <div className="ml-6 text-sm bg-gray-50 px-2 py-1 rounded">
                  {(() => {
                    const startFormatted = formatDateString(correctDates.start_date);
                    const endFormatted = formatDateString(correctDates.end_date);
                    console.log(`RENDER - Competition dates: ${startFormatted} - ${endFormatted}`);
                    return `${startFormatted} - ${endFormatted}`;
                  })()}
                </div>

                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-amber-600" />
                  <span className="font-medium">Schooling:</span>
                </div>
                <div className="ml-6 text-sm bg-gray-50 px-2 py-1 rounded">
                  {(() => {
                    const startFormatted = formatDateString(correctDates.schooling_start_date);
                    const endFormatted = formatDateString(correctDates.schooling_end_date);
                    console.log(`RENDER - Schooling dates: ${startFormatted} - ${endFormatted}`);
                    return `${startFormatted} - ${endFormatted}`;
                  })()}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-green-50 rounded">
                  <div className="text-2xl font-bold text-green-800">
                    {locations?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600">Locations</div>
                </div>
                <div className="text-center p-3 bg-amber-50 rounded">
                  <div className="text-2xl font-bold text-amber-800">
                    {bookings?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600">Bookings</div>
                </div>
              </div>

              <Button
                onClick={() => onEventSelect(event.id)}
                className="w-full bg-green-600 hover:bg-green-700 mb-2"
              >
                Manage Event Details
              </Button>
              <Button
                onClick={() => setSelectedEventForConfig(event)}
                variant="outline"
                className="w-full border-blue-500 text-blue-700 hover:bg-blue-50 mb-2"
              >
                Configure Levels & Tests
              </Button>
              <Button
                onClick={() => handleEditClick(event)}
                variant="outline"
                className="w-full border-amber-500 text-amber-700 hover:bg-amber-50"
                disabled={isEditing}
              >
                <Edit className="h-4 w-4 mr-2" /> {isEditing ? 'Editing...' : 'Edit Event'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {isEditing && (
          <Card className="border-amber-200 mt-2 mb-6 shadow-md">
            <CardHeader className="py-3">
              <CardTitle className="text-sm text-amber-800">Edit {event.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-name">Event Name</Label>
                  <Input
                    id="edit-name"
                    value={editEvent.name}
                    onChange={(e) => setEditEvent({ ...editEvent, name: e.target.value })}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-start-date">Start Date</Label>
                    <Input
                      id="edit-start-date"
                      type="date"
                      value={editEvent.start_date}
                      onChange={(e) => setEditEvent({ ...editEvent, start_date: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-end-date">End Date</Label>
                    <Input
                      id="edit-end-date"
                      type="date"
                      value={editEvent.end_date}
                      onChange={(e) => setEditEvent({ ...editEvent, end_date: e.target.value })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-schooling-start">Schooling Start Date</Label>
                    <Input
                      id="edit-schooling-start"
                      type="date"
                      value={editEvent.schooling_start_date}
                      onChange={(e) => setEditEvent({ ...editEvent, schooling_start_date: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-schooling-end">Schooling End Date</Label>
                    <Input
                      id="edit-schooling-end"
                      type="date"
                      value={editEvent.schooling_end_date}
                      onChange={(e) => setEditEvent({ ...editEvent, schooling_end_date: e.target.value })}
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-2">
                  <Button
                    variant="outline"
                    onClick={handleCancelEdit}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleUpdateEvent}
                    className="bg-gold text-primary hover:bg-gold-hover"
                    disabled={updateEvent.isPending}
                  >
                    {updateEvent.isPending ? 'Updating...' : 'Update Event'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  if (selectedEventForConfig) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => setSelectedEventForConfig(null)}
            className="text-green-700 hover:bg-green-50"
          >
            ← Back to Events
          </Button>
          <h3 className="text-xl font-semibold text-green-800">
            Configure {selectedEventForConfig.name}
          </h3>
        </div>

        <Tabs defaultValue="levels" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="levels">Event Levels</TabsTrigger>
            <TabsTrigger value="tests">Dressage Tests</TabsTrigger>
          </TabsList>

          <TabsContent value="levels">
            <EventLevelsManagement
              eventId={selectedEventForConfig.id}
              eventName={selectedEventForConfig.name}
            />
          </TabsContent>

          <TabsContent value="tests">
            <EventDressageTestsManagement
              eventId={selectedEventForConfig.id}
              eventName={selectedEventForConfig.name}
            />
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading events...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold text-green-800">Events</h3>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-green-600 hover:bg-green-700"
        >
          <Plus className="h-4 w-4 mr-2" /> Create Event
        </Button>
      </div>

      {showCreateForm && (
        <Card className="border-green-200">
          <CardHeader>
            <CardTitle className="text-green-800">Create New Event</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Event Name</Label>
              <Input
                id="name"
                value={newEvent.name}
                onChange={(e) => setNewEvent({ ...newEvent, name: e.target.value })}
                placeholder="Enter event name"
              />
            </div>

            <div>
              <Label htmlFor="event-type">Event Type</Label>
              <Select
                value={newEvent.event_type}
                onValueChange={(value: 'dressage' | 'horse_trials' | 'hunter_jumper') => 
                  setNewEvent({ ...newEvent, event_type: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select event type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="horse_trials">Horse Trials</SelectItem>
                  <SelectItem value="dressage">Dressage</SelectItem>
                  <SelectItem value="hunter_jumper">Hunter/Jumper</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="start_date">Competition Start</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={newEvent.start_date}
                  onChange={(e) => setNewEvent({ ...newEvent, start_date: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="end_date">Competition End</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={newEvent.end_date}
                  onChange={(e) => setNewEvent({ ...newEvent, end_date: e.target.value })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="schooling_start_date">Schooling Start</Label>
                <Input
                  id="schooling_start_date"
                  type="date"
                  value={newEvent.schooling_start_date}
                  onChange={(e) => setNewEvent({ ...newEvent, schooling_start_date: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="schooling_end_date">Schooling End</Label>
                <Input
                  id="schooling_end_date"
                  type="date"
                  value={newEvent.schooling_end_date}
                  onChange={(e) => setNewEvent({ ...newEvent, schooling_end_date: e.target.value })}
                />
              </div>
            </div>

            {/* Event Levels Section */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-700 border-b pb-2">Event Levels</h4>
              
              {/* Discipline Filter */}
              <div className="space-y-2">
                <Label htmlFor="discipline-filter">Filter by Discipline</Label>
                <Select value={selectedDiscipline} onValueChange={setSelectedDiscipline}>
                  <SelectTrigger>
                    <SelectValue placeholder="All disciplines" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All disciplines</SelectItem>
                    <SelectItem value="dressage">Dressage</SelectItem>
                    <SelectItem value="eventing">Eventing</SelectItem>
                    <SelectItem value="show_jumping">Show Jumping</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Add Level Dropdown */}
              <div className="space-y-2">
                <Label htmlFor="level-select">Add Level</Label>
                <Select onValueChange={handleAddLevel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a level to add" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableLevels.map((level) => (
                      <SelectItem key={level.id} value={level.id}>
                        {level.name} ({level.discipline})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Selected Levels */}
              {selectedLevelObjects.length > 0 && (
                <div className="space-y-2">
                  <Label>Selected Levels</Label>
                  <div className="flex flex-wrap gap-2">
                    {selectedLevelObjects.map((level) => (
                      <Badge 
                        key={level.id} 
                        variant="outline" 
                        className="text-green-700 border-green-300 flex items-center gap-1"
                      >
                        {level.name} ({level.discipline})
                        <button
                          onClick={() => handleRemoveLevel(level.id)}
                          className="ml-1 hover:text-red-600"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="flex space-x-2">
              <Button
                onClick={handleCreateEvent}
                disabled={!newEvent.name || createEvent.isPending}
                className="bg-green-600 hover:bg-green-700"
              >
                Create Event
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowCreateForm(false);
                  setSelectedLevels([]);
                  setSelectedDiscipline('all');
                }}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {events?.map((event) => (
          <EventCard key={event.id} event={event} />
        ))}
      </div>

      {!events || events.length === 0 && (
        <Card className="bg-gray-50 border-gray-200">
          <CardContent className="text-center py-12">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No events yet</h3>
            <p className="text-gray-600 mb-4">Create your first event to get started.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
