import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Plus, Edit, Trash, X } from 'lucide-react';
import { useEvents, useCreateEvent, useUpdateEvent, Event, useDeleteEvent } from '@/hooks/useEvents';
import { useLocations } from '@/hooks/useLocations'; // Changed from useArenas
import { useBookings } from '@/hooks/useBookings';
import { useLevels } from '@/hooks/useLevels';
import { EventLevelsManagement } from './EventLevelsManagement';
import { EventDressageTestsManagement } from './EventDressageTestsManagement';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

interface EventManagementProps {
  onEventSelect: (eventId: string) => void;
}

const formatDateForDisplay = (dateString) => {
  if (!dateString) return '';

  console.log(`Formatting date for display: ${dateString}`);

  // Extract just the date part if there's a time component
  const datePart = dateString.split('T')[0];

  // Check if it's in YYYY-MM-DD format
  const dateRegex = /^(\d{4})-(\d{2})-(\d{2})$/;
  const match = datePart.match(dateRegex);

  if (match) {
    const [_, year, month, day] = match;
    // Convert to MM/DD/YYYY format
    // Remove leading zeros but ensure it's treated as a number
    const formattedMonth = parseInt(month, 10);
    const formattedDay = parseInt(day, 10);

    console.log(`Parsed date parts: year=${year}, month=${formattedMonth}, day=${formattedDay}`);

    return `${formattedMonth}/${formattedDay}/${year}`;
  }

  console.error(`Date string not in expected format: ${dateString}`);
  return dateString;
};

const formatDateForStorage = (dateString) => {
  if (!dateString) return '';

  console.log(`Formatting date for storage: ${dateString}`);

  // If it's already in YYYY-MM-DD format, return as is
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    console.log(`Date is already in YYYY-MM-DD format: ${dateString}`);
    return dateString;
  }

  // Parse the date string
  const parts = dateString.split('-');
  if (parts.length !== 3) {
    console.error(`Invalid date format: ${dateString}`);
    return '';
  }

  // Ensure the date is in YYYY-MM-DD format
  const year = parts[0];
  const month = parts[1].padStart(2, '0');
  const day = parts[2].padStart(2, '0');

  const formattedDate = `${year}-${month}-${day}`;
  console.log(`Formatted date for storage: ${formattedDate}`);

  return formattedDate;
};

// Super detailed logging function to understand exactly what's happening
const logDateDetails = (label, dateString) => {
  console.log(`=== ${label} ===`);
  console.log(`Raw value: "${dateString}"`);
  console.log(`Type: ${typeof dateString}`);
  console.log(`Length: ${dateString?.length}`);

  // Log each character and its code
  if (dateString) {
    console.log('Character by character:');
    for (let i = 0; i < dateString.length; i++) {
      console.log(`  Pos ${i}: '${dateString[i]}' (code: ${dateString.charCodeAt(i)})`);
    }
  }

  // Try different parsing methods
  try {
    const dateObj = new Date(dateString);
    console.log(`As Date object: ${dateObj}`);
    console.log(`isValid: ${!isNaN(dateObj.getTime())}`);
    console.log(`UTC string: ${dateObj.toUTCString()}`);
    console.log(`ISO string: ${dateObj.toISOString()}`);
    console.log(`Local string: ${dateObj.toString()}`);
    console.log(`getTimezoneOffset: ${dateObj.getTimezoneOffset()}`);
  } catch (e) {
    console.log(`Error creating Date object: ${e.message}`);
  }

  console.log('---');
};

export const EventManagement: React.FC<EventManagementProps> = ({ onEventSelect }) => {
  console.log('🔴🔴🔴 EVENT MANAGEMENT COMPONENT LOADED 🔴🔴🔴');

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingEventId, setEditingEventId] = useState<string | null>(null);
  const [selectedEventForConfig, setSelectedEventForConfig] = useState<Event | null>(null);
  const [newEvent, setNewEvent] = useState({
    name: '',
    start_date: '',
    end_date: '',
    event_type: 'horse_trials' as const
  });
  const [editEvent, setEditEvent] = useState({
    name: '',
    start_date: '',
    end_date: ''
  });

  const [selectedLevels, setSelectedLevels] = useState<string[]>([]);
  const [selectedDiscipline, setSelectedDiscipline] = useState('all');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [eventToDelete, setEventToDelete] = useState<Event | null>(null);

  const { data: events, isLoading } = useEvents();
  const { data: allLevels } = useLevels();
  const createEvent = useCreateEvent();
  const updateEvent = useUpdateEvent();
  const deleteEvent = useDeleteEvent();

  // Filter levels by discipline
  const filteredLevels = allLevels?.filter(level => 
    selectedDiscipline === 'all' || level.discipline === selectedDiscipline
  ) || [];

  // Get available levels (not already selected)
  const availableLevels = filteredLevels.filter(level => 
    !selectedLevels.includes(level.id)
  );

  // Get selected level objects
  const selectedLevelObjects = allLevels?.filter(level => 
    selectedLevels.includes(level.id)
  ) || [];

  const handleAddLevel = (levelId: string) => {
    if (!selectedLevels.includes(levelId)) {
      setSelectedLevels([...selectedLevels, levelId]);
    }
  };

  const handleRemoveLevel = (levelId: string) => {
    setSelectedLevels(selectedLevels.filter(id => id !== levelId));
  };

  // Add this effect to debug event dates
  useEffect(() => {
    if (events && events.length > 0) {
      console.log('===== DEBUG - EVENT DATES =====');
      events.forEach(event => {
        console.log(`Event: ${event.name}`);
        console.log(`Raw start_date: ${event.start_date}`);
        console.log(`Raw end_date: ${event.end_date}`);
        console.log(`Formatted start_date: ${formatDateForDisplay(event.start_date)}`);
        console.log(`Formatted end_date: ${formatDateForDisplay(event.end_date)}`);
        console.log('---');
      });
    }
  }, [events]);

  // Add this effect to debug event dates in detail
  useEffect(() => {
    if (events && events.length > 0) {
      console.log('===== DETAILED DATE DEBUG =====');
      events.forEach(event => {
        console.log(`Event: ${event.name}`);

        // Log raw date strings
        console.log(`Raw start_date: "${event.start_date}" (type: ${typeof event.start_date})`);
        console.log(`Raw end_date: "${event.end_date}" (type: ${typeof event.end_date})`);

        // Try parsing with Date and show results
        if (event.start_date) {
          const startDate = new Date(event.start_date);
          console.log(`start_date as Date: ${startDate}`);
          console.log(`start_date toISOString: ${startDate.toISOString()}`);
          console.log(`start_date getDate: ${startDate.getDate()}`);
          console.log(`start_date getMonth: ${startDate.getMonth() + 1}`);
          console.log(`start_date getFullYear: ${startDate.getFullYear()}`);
          console.log(`start_date getTimezoneOffset: ${startDate.getTimezoneOffset()}`);
        }

        // Try our new formatter
        console.log(`Formatted with new function: ${formatDateForDisplay(event.start_date)}`);

        console.log('---');
      });
    }
  }, [events]);

  // Add this to your component
  useEffect(() => {
    if (events && events.length > 0) {
      console.log('======= SUPER DETAILED DATE ANALYSIS =======');
      const event = events[0]; // Just analyze the first event

      logDateDetails('start_date', event.start_date);
      logDateDetails('end_date', event.end_date);
    }
  }, [events]);

  const handleCreateEvent = () => {
    createEvent.mutate({
      event: newEvent,
      levelIds: selectedLevels
    }, {
      onSuccess: () => {
        setShowCreateForm(false);
        setNewEvent({
          name: '',
          start_date: '',
          end_date: '',
          event_type: 'horse_trials'
        });
        setSelectedLevels([]);
        setSelectedDiscipline('all');
      }
    });
  };

  const handleEditClick = (event: Event) => {
    setEditingEventId(event.id);
    setEditEvent({
      name: event.name,
      start_date: event.start_date,
      end_date: event.end_date
    });
  };

  const handleCancelEdit = () => {
    setEditingEventId(null);
    setEditEvent({
      name: '',
      start_date: '',
      end_date: ''
    });
  };

  const handleUpdateEvent = () => {
    if (!editingEventId) return;

    const eventData = {
      id: editingEventId,
      name: editEvent.name,
      start_date: formatDateForStorage(editEvent.start_date),
      end_date: formatDateForStorage(editEvent.end_date)
    };

    updateEvent.mutate(eventData, {
      onSuccess: () => {
        setEditingEventId(null);
        setEditEvent({
          name: '',
          start_date: '',
          end_date: ''
        });
      }
    });
  };

  const handleDeleteClick = (event: Event) => {
    setEventToDelete(event);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (!eventToDelete) return;

    deleteEvent.mutate(eventToDelete.id, {
      onSuccess: () => {
        setDeleteDialogOpen(false);
        setEventToDelete(null);
      }
    });
  };

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setEventToDelete(null);
  };

  const getEventTypeLabel = (eventType: string) => {
    switch (eventType) {
      case 'horse_trials':
        return 'Horse Trials';
      case 'dressage':
        return 'Dressage';
      case 'hunter_jumper':
        return 'Hunter/Jumper';
      default:
        return eventType;
    }
  };

  const getEventTypeBadgeColor = (eventType: string) => {
    switch (eventType) {
      case 'horse_trials':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'dressage':
        return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'hunter_jumper':
        return 'bg-orange-100 text-orange-800 border-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const EventCard: React.FC<{ event: Event; deleteEvent: (event: Event) => void }> = ({ event, deleteEvent }) => {
    console.log(`🔵🔵🔵 EVENT CARD RENDERING FOR: ${event.name} 🔵🔵🔵`);
    console.log('EVENT DATA:', event);

    const { data: locations } = useLocations(event.id); // Changed from arenas to locations
    const { data: bookings } = useBookings(event.id);
    const isEditing = editingEventId === event.id;

    // Store the correct dates from the edit form
    const [correctDates, setCorrectDates] = useState({
      start_date: '',
      end_date: ''
    });

    // When the component mounts or the event changes, update the correct dates
    useEffect(() => {
      if (event) {
        // These are the raw dates from the API, which are correct in the edit form
        setCorrectDates({
          start_date: event.start_date,
          end_date: event.end_date
        });

        console.log('Stored correct dates for display:');
        console.log(`start_date: ${event.start_date}`);
        console.log(`end_date: ${event.end_date}`);
      }
    }, [event]);

    // Simple function to format dates as MM/DD/YYYY without using Date object
    const formatDateString = (dateStr) => {
      if (!dateStr) {
        console.log(`⚠️ formatDateString called with empty value`);
        return '';
      }

      console.log(`🟡 FORMATTING DATE: "${dateStr}"`);

      // Extract just the date part (YYYY-MM-DD)
      const datePart = dateStr.split('T')[0];
      console.log(`  Date part: "${datePart}"`);

      // Split into year, month, day
      const [year, month, day] = datePart.split('-');
      console.log(`  Parts: year="${year}", month="${month}", day="${day}"`);

      // Return as MM/DD/YYYY with no leading zeros
      const result = `${parseInt(month, 10)}/${parseInt(day, 10)}/${year}`;
      console.log(`  RESULT: "${result}"`);

      return result;
    };

    console.log(`🟢🟢🟢 DATES THAT WILL BE DISPLAYED FOR ${event.name}:`);
    console.log(`Dates: ${formatDateString(correctDates.start_date)} - ${formatDateString(correctDates.end_date)}`);

    return (
      <div className="flex flex-col">
        <div style={{background: 'yellow', padding: '10px', margin: '10px'}}>
          DEBUG: EventCard rendering for {event.name}
        </div>
        <button 
          onClick={() => handleDeleteClick(event)}
          style={{
            background: 'red', 
            color: 'white', 
            padding: '10px', 
            margin: '10px',
            border: 'none',
            borderRadius: '4px'
          }}
        >
          DELETE {event.name}
        </button>
        <Card className="border-green-200" data-testid={`event-card-${event.id}`}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-lg text-green-800" data-testid={`event-name-${event.id}`}>{event.name}</CardTitle>
                  <Badge variant="outline" className={getEventTypeBadgeColor(event.event_type)} data-testid={`event-type-badge-${event.id}`}>
                    {getEventTypeLabel(event.event_type)}
                  </Badge>
                  {event.is_active ? (
                    <Badge variant="outline" className="text-green-700 border-green-300" data-testid={`event-status-${event.id}`}>
                      Active
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-gray-400 border-gray-300" data-testid={`event-status-${event.id}`}>
                      Inactive
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-green-600" />
                  <span className="font-medium">Dates:</span>
                </div>
                <div className="ml-6 text-sm bg-gray-50 px-2 py-1 rounded" data-testid={`event-dates-${event.id}`}>
                  {(() => {
                    const startFormatted = formatDateString(correctDates.start_date);
                    const endFormatted = formatDateString(correctDates.end_date);
                    console.log(`RENDER - Event dates: ${startFormatted} - ${endFormatted}`);
                    return `${startFormatted} - ${endFormatted}`;
                  })()}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-green-50 rounded" data-testid={`event-locations-count-${event.id}`}>
                  <div className="text-2xl font-bold text-green-800">
                    {locations?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600">Locations</div>
                </div>
                <div className="text-center p-3 bg-amber-50 rounded" data-testid={`event-bookings-count-${event.id}`}>
                  <div className="text-2xl font-bold text-amber-800">
                    {bookings?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600">Bookings</div>
                </div>
              </div>

              <Button
                onClick={() => onEventSelect(event.id)}
                className="w-full bg-green-600 hover:bg-green-700 mb-2"
                data-testid={`manage-event-${event.id}`}
              >
                Manage Event Details
              </Button>
              <Button
                onClick={() => setSelectedEventForConfig(event)}
                variant="outline"
                className="w-full border-blue-500 text-blue-700 hover:bg-blue-50 mb-2"
                data-testid={`configure-event-${event.id}`}
              >
                Configure Levels & Tests
              </Button>
              <Button
                onClick={() => handleEditClick(event)}
                variant="outline"
                className="w-full border-amber-500 text-amber-700 hover:bg-amber-50"
                disabled={isEditing}
                data-testid={`edit-event-${event.id}`}
              >
                <Edit className="h-4 w-4 mr-2" /> {isEditing ? 'Editing...' : 'Edit Event'}
              </Button>
              <Button
                onClick={() => handleDeleteClick(event)}
                variant="outline"
                className="w-full border-red-500 text-red-700 hover:bg-red-50"
                data-testid={`delete-event-${event.id}`}
              >
                <Trash className="h-4 w-4 mr-2" /> Delete Event
              </Button>
            </div>
          </CardContent>
        </Card>

        {isEditing && (
          <Card className="border-amber-200 mt-2 mb-6 shadow-md" data-testid={`edit-event-form-${event.id}`}>
            <CardHeader className="py-3">
              <CardTitle className="text-sm text-amber-800">Edit {event.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-name">Event Name</Label>
                  <Input
                    id="edit-name"
                    value={editEvent.name}
                    onChange={(e) => setEditEvent({ ...editEvent, name: e.target.value })}
                    data-testid={`edit-event-name-${event.id}`}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-start-date">Start Date</Label>
                    <Input
                      id="edit-start-date"
                      type="date"
                      value={editEvent.start_date}
                      onChange={(e) => setEditEvent({ ...editEvent, start_date: e.target.value })}
                      data-testid={`edit-start-date-${event.id}`}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-end-date">End Date</Label>
                    <Input
                      id="edit-end-date"
                      type="date"
                      value={editEvent.end_date}
                      onChange={(e) => setEditEvent({ ...editEvent, end_date: e.target.value })}
                      data-testid={`edit-end-date-${event.id}`}
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-2">
                  <Button
                    variant="outline"
                    onClick={handleCancelEdit}
                    data-testid={`cancel-edit-event-${event.id}`}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleUpdateEvent}
                    className="bg-gold text-primary hover:bg-gold-hover"
                    disabled={updateEvent.isPending}
                    data-testid={`update-event-${event.id}`}
                  >
                    {updateEvent.isPending ? 'Updating...' : 'Update Event'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  if (selectedEventForConfig) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => setSelectedEventForConfig(null)}
            className="text-green-700 hover:bg-green-50"
          >
            ← Back to Events
          </Button>
          <h3 className="text-xl font-semibold text-green-800">
            Configure {selectedEventForConfig.name}
          </h3>
        </div>

        <Tabs defaultValue="levels" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="levels">Event Levels</TabsTrigger>
            <TabsTrigger value="tests">Dressage Tests</TabsTrigger>
          </TabsList>

          <TabsContent value="levels">
            <EventLevelsManagement
              eventId={selectedEventForConfig.id}
              eventName={selectedEventForConfig.name}
            />
          </TabsContent>

          <TabsContent value="tests">
            <EventDressageTestsManagement
              eventId={selectedEventForConfig.id}
              eventName={selectedEventForConfig.name}
            />
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  if (isLoading) {
    console.log('Rendering loading state');
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading events...</div>
        </CardContent>
      </Card>
    );
  }

  console.log('Main component rendering, events:', events);
  console.log('Events length:', events?.length);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold text-green-800" data-testid="events-title">Events</h3>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-green-600 hover:bg-green-700"
          data-testid="create-event-button"
        >
          <Plus className="h-4 w-4 mr-2" /> Create Event
        </Button>
      </div>

      {showCreateForm && (
        <Card className="border-green-200" data-testid="create-event-form">
          <CardHeader>
            <CardTitle className="text-green-800">Create New Event</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Event Name</Label>
              <Input
                id="name"
                value={newEvent.name}
                onChange={(e) => setNewEvent({ ...newEvent, name: e.target.value })}
                placeholder="Enter event name"
                data-testid="event-name-input"
              />
            </div>

            <div>
              <Label htmlFor="event-type">Event Type</Label>
              <Select
                value={newEvent.event_type}
                onValueChange={(value: 'dressage' | 'horse_trials' | 'hunter_jumper') => 
                  setNewEvent({ ...newEvent, event_type: value })
                }
                data-testid="event-type-select"
              >
                <SelectTrigger data-testid="event-type-trigger">
                  <SelectValue placeholder="Select event type" />
                </SelectTrigger>
                <SelectContent data-testid="event-type-content">
                  <SelectItem value="horse_trials" data-testid="event-type-horse-trials">Horse Trials</SelectItem>
                  <SelectItem value="dressage" data-testid="event-type-dressage">Dressage</SelectItem>
                  <SelectItem value="hunter_jumper" data-testid="event-type-hunter-jumper">Hunter/Jumper</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="start_date">Competition Start</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={newEvent.start_date}
                  onChange={(e) => setNewEvent({ ...newEvent, start_date: e.target.value })}
                  data-testid="start-date-input"
                />
              </div>
              <div>
                <Label htmlFor="end_date">Competition End</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={newEvent.end_date}
                  onChange={(e) => setNewEvent({ ...newEvent, end_date: e.target.value })}
                  data-testid="end-date-input"
                />
              </div>
            </div>

            <div className="flex space-x-2">
              <Button
                onClick={handleCreateEvent}
                disabled={!newEvent.name || createEvent.isPending}
                className="bg-green-600 hover:bg-green-700"
                data-testid="create-event-submit-button"
              >
                Create Event
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowCreateForm(false);
                  setSelectedLevels([]);
                  setSelectedDiscipline('all');
                }}
                data-testid="cancel-create-event-button"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {console.log('About to map events:', events)}
        {events?.map((event) => {
          console.log('Mapping event:', event.name);
          return <EventCard key={event.id} event={event} deleteEvent={deleteEvent} />;
        })}
      </div>

      {!events || events.length === 0 && (
        <Card className="bg-gray-50 border-gray-200" data-testid="empty-events-state">
          <CardContent className="text-center py-12">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2" data-testid="empty-events-title">No events yet</h3>
            <p className="text-gray-600 mb-4" data-testid="empty-events-message">Create your first event to get started.</p>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent data-testid="delete-event-dialog">
          <AlertDialogHeader>
            <AlertDialogTitle data-testid="delete-event-dialog-title">Delete Event</AlertDialogTitle>
            <AlertDialogDescription data-testid="delete-event-dialog-description">
              <div className="space-y-4">
                <p className="text-red-600 font-medium">
                  Are you sure you want to delete "{eventToDelete?.name}"?
                </p>
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <p className="text-sm text-red-800 font-medium mb-2">This action will permanently delete:</p>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• The event itself</li>
                    <li>• All locations and arenas</li>
                    <li>• All activities and time slots</li>
                    <li>• All bookings and reservations</li>
                    <li>• All event levels and dressage tests</li>
                  </ul>
                  <p className="text-sm text-red-800 font-medium mt-3">
                    This action cannot be undone!
                  </p>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDelete} data-testid="cancel-delete-event-button">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
              disabled={deleteEvent.isPending}
              data-testid="confirm-delete-event-button"
            >
              {deleteEvent.isPending ? 'Deleting...' : 'Delete Event'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
