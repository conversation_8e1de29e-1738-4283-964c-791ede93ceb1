import React from 'react';
import { render, screen, waitFor, fireEven } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { EventDetail } from './EventDetail';
import { useUpdateEvent } from '@/hooks/useEvents';
import { useUserRoles, useUserRole } from '@/hooks/useUserRoles';
import { useSession } from '@supabase/auth-helpers-react';
import { useBookings } from '@/hooks/useBookings';
import { useLocations } from '@/hooks/useLocations';
import { useEventDressageTests } from '@/hooks/useDressageTests';
import { useEventLevels } from '@/hooks/useLevels';
import { useLevels, useCreateEventLevel, useDeleteEventLevel } from '@/hooks/useLevels';
import { useDressageTests, useCreateEventDressageTest, useDeleteEventDressageTest } from '@/hooks/useDressageTests';
import { useEventPricing, useCreateEventPricing, useUpdateEventPricing, useDeleteEventPricing } from '@/hooks/useEventPricing';
import { usePricingSettings } from '@/hooks/usePricingSettings';

// Mock the hooks
vi.mock('@/hooks/useEvents');
vi.mock('@/hooks/useUserRoles', () => ({
  useUserRoles: vi.fn(),
  useUserRole: vi.fn(),
}));
vi.mock('@/hooks/useBookings');
vi.mock('@/hooks/useLocations');
vi.mock('@/hooks/useDressageTests');
vi.mock('@/hooks/useLevels');
vi.mock('@/hooks/useEventPricing');
vi.mock('@/hooks/usePricingSettings');
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));
vi.mock('@supabase/auth-helpers-react');
vi.mock('@/components/Header', () => ({
  Header: ({ onBackToEvents, showBackButton, userRole }: { onBackToEvents?: () => void; showBackButton?: boolean; userRole?: string }) => {
    return (
      <div data-testid="header">
        <button onClick={onBackToEvents} data-testid="back-button">
          Back to Events
        </button>
        <span data-testid="user-role">{String(userRole)}</span>
      </div>
    );
  },
}));

// Mock the admin components
vi.mock('@/components/admin/LocationManagement', () => ({
  LocationManagement: ({ eventId, eventName, selectedDate }: { eventId: string; eventName: string; selectedDate?: string }) => (
    <div data-testid="location-management">
      <h3>Location Management</h3>
      <p>Event ID: {eventId}</p>
      <p>Event Name: {eventName}</p>
      <p>Selected Date: {selectedDate}</p>
    </div>
  ),
}));

vi.mock('@/components/admin/AdminBookings', () => ({
  AdminBookings: ({ event }: any) => (
    <div data-testid="admin-bookings">
      <h3>Admin Bookings</h3>
      <p>Event: {event?.name}</p>
    </div>
  ),
}));

vi.mock('@/components/admin/EventLevelsManagement', () => ({
  EventLevelsManagement: ({ eventId }: any) => (
    <div data-testid="event-levels-management">
      <h3>Event Levels Management</h3>
      <p>Event ID: {eventId}</p>
    </div>
  ),
}));

vi.mock('@/components/admin/EventDressageTestsManagement', () => ({
  EventDressageTestsManagement: ({ eventId }: any) => (
    <div data-testid="event-dressage-tests-management">
      <h3>Event Dressage Tests Management</h3>
      <p>Event ID: {eventId}</p>
    </div>
  ),
}));

vi.mock('@/components/admin/EventPricingManagement', () => ({
  EventPricingManagement: ({ eventId }: any) => (
    <div data-testid="event-pricing-management">
      <h3>Event Pricing Management</h3>
      <p>Event ID: {eventId}</p>
    </div>
  ),
}));

const mockEvent = {
  id: 'event-123',
  name: 'Test Event',
  start_date: '2024-06-15',
  end_date: '2024-06-16',
  event_type: 'horse_trials',
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockUseUpdateEvent = vi.mocked(useUpdateEvent);
const mockUseUserRoles = vi.mocked(useUserRoles);
const mockUseUserRole = vi.mocked(useUserRole);
const mockUseSession = vi.mocked(useSession);
const mockUseBookings = vi.mocked(useBookings);
const mockUseLocations = vi.mocked(useLocations);
const mockUseEventDressageTests = vi.mocked(useEventDressageTests);
const mockUseEventLevels = vi.mocked(useEventLevels);
const mockUseLevels = vi.mocked(useLevels);
const mockUseCreateEventLevel = vi.mocked(useCreateEventLevel);
const mockUseDeleteEventLevel = vi.mocked(useDeleteEventLevel);
const mockUseDressageTests = vi.mocked(useDressageTests);
const mockUseCreateEventDressageTest = vi.mocked(useCreateEventDressageTest);
const mockUseDeleteEventDressageTest = vi.mocked(useDeleteEventDressageTest);
const mockUseEventPricing = vi.mocked(useEventPricing);
const mockUseCreateEventPricing = vi.mocked(useCreateEventPricing);
const mockUseUpdateEventPricing = vi.mocked(useUpdateEventPricing);
const mockUseDeleteEventPricing = vi.mocked(useDeleteEventPricing);
const mockUsePricingSettings = vi.mocked(usePricingSettings);

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderEventDetail = (event = mockEvent, eventId = 'event-123') => {
  const queryClient = createTestQueryClient();
  
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>
        <EventDetail event={event} eventId={eventId} />
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('EventDetail', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mocks
    mockUseUpdateEvent.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      error: null,
    });
    
    mockUseUserRoles.mockReturnValue([{ role: 'organizer', isLoading: false }]);
    mockUseUserRole.mockReturnValue({ userRole: 'organizer', isLoading: false });
    mockUseSession.mockReturnValue({
      user: { id: 'test-user-id' },
      session: null,
    });
    
    // Mock the hooks that child components depend on
    mockUseBookings.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });
    
    mockUseLocations.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });
    
    mockUseEventDressageTests.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });
    
    mockUseEventLevels.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });
    
    mockUseLevels.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });
    
    mockUseCreateEventLevel.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      error: null,
    });
    
    mockUseDeleteEventLevel.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      error: null,
    });
    
    mockUseDressageTests.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });
    
    mockUseCreateEventDressageTest.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      error: null,
    });
    
    mockUseDeleteEventDressageTest.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      error: null,
    });
    
    mockUseEventPricing.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });
    
    mockUseCreateEventPricing.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      error: null,
    });
    
    mockUseUpdateEventPricing.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      error: null,
    });
    
    mockUseDeleteEventPricing.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      error: null,
    });
    
    mockUsePricingSettings.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });
  });

  describe('Tab Structure and Navigation', () => {
    it('renders all four tabs correctly', async () => {
      renderEventDetail();
      
      expect(screen.getByText('Event / Locations & Activities')).toBeInTheDocument();
      expect(screen.getByText('Bookings')).toBeInTheDocument();
      expect(screen.getByText('Levels & Tests')).toBeInTheDocument();
      expect(screen.getByText('Pricing')).toBeInTheDocument();
    });

    it('shows the overview tab content by default', async () => {
      renderEventDetail();
      
      expect(screen.getByTestId('location-management')).toBeInTheDocument();
    });

    it('switches to bookings tab when clicked', async () => {
      const user = userEvent.setup();
      renderEventDetail();
      
      await user.click(screen.getByText('Bookings'));
      
      expect(screen.getByTestId('admin-bookings')).toBeInTheDocument();
    });

    it('switches to levels & tests tab when clicked', async () => {
      const user = userEvent.setup();
      renderEventDetail();
      
      await user.click(screen.getByText('Levels & Tests'));
      
      expect(screen.getByTestId('event-levels-management')).toBeInTheDocument();
      expect(screen.getByTestId('event-dressage-tests-management')).toBeInTheDocument();
    });

    it('switches to pricing tab when clicked', async () => {
      const user = userEvent.setup();
      renderEventDetail();
      
      await user.click(screen.getByText('Pricing'));
      
      expect(screen.getByTestId('event-pricing-management')).toBeInTheDocument();
    });
  });

  describe('Event Information Display', () => {
    it('displays event name and status', async () => {
      renderEventDetail();
      
      expect(screen.getByTestId('event-name-header')).toHaveTextContent('Test Event');
      expect(screen.getByTestId('event-status-badge')).toHaveTextContent('Active');
    });

    it('displays event dates correctly', async () => {
      renderEventDetail();
      
      expect(screen.getByTestId('event-dates-header')).toHaveTextContent('6/15/2024 - 6/16/2024');
    });

    it('shows edit button when not in editing mode', async () => {
      renderEventDetail();
      
      expect(screen.getByText('Edit Event')).toBeInTheDocument();
    });
  });

  describe('Tab Content Verification', () => {
    it('overview tab contains LocationManagement component', async () => {
      renderEventDetail();
      
      expect(screen.getByTestId('location-management')).toBeInTheDocument();
    });

    it('bookings tab contains AdminBookings component', async () => {
      const user = userEvent.setup();
      renderEventDetail();
      
      await user.click(screen.getByText('Bookings'));
      
      expect(screen.getByTestId('admin-bookings')).toBeInTheDocument();
    });

    it('levels & tests tab contains both management components', async () => {
      const user = userEvent.setup();
      renderEventDetail();
      
      await user.click(screen.getByText('Levels & Tests'));
      
      expect(screen.getByTestId('event-levels-management')).toBeInTheDocument();
      expect(screen.getByTestId('event-dressage-tests-management')).toBeInTheDocument();
    });

    it('pricing tab contains EventPricingManagement component', async () => {
      const user = userEvent.setup();
      renderEventDetail();
      
      await user.click(screen.getByText('Pricing'));
      
      expect(screen.getByTestId('event-pricing-management')).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('renders header with back button', async () => {
      renderEventDetail();
      
      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('back-button')).toBeInTheDocument();
    });

    it('displays user role in header', async () => {
      renderEventDetail();
      
      expect(screen.getByTestId('user-role')).toHaveTextContent('organizer');
    });
  });

  describe('UI Element Regression Testing', () => {
    it('ensures all critical UI elements are present in the default state', async () => {
      renderEventDetail();
      
      // Header elements
      expect(screen.getByTestId('event-name-header')).toHaveTextContent('Test Event');
      expect(screen.getByTestId('event-status-badge')).toHaveTextContent('Active');
      expect(screen.getByText('Edit Event')).toBeInTheDocument();
      
      // Tab elements
      expect(screen.getByText('Event / Locations & Activities')).toBeInTheDocument();
      expect(screen.getByText('Bookings')).toBeInTheDocument();
      expect(screen.getByText('Levels & Tests')).toBeInTheDocument();
      expect(screen.getByText('Pricing')).toBeInTheDocument();
      
      // Content elements
      expect(screen.getByTestId('location-management')).toBeInTheDocument();
    });

    it('ensures tab content components render with expected UI elements', async () => {
      const user = userEvent.setup();
      renderEventDetail();
      
      // Test bookings tab
      await user.click(screen.getByText('Bookings'));
      expect(screen.getByTestId('admin-bookings')).toBeInTheDocument();
      
      // Test levels tab
      await user.click(screen.getByText('Levels & Tests'));
      expect(screen.getByTestId('event-levels-management')).toBeInTheDocument();
      expect(screen.getByTestId('event-dressage-tests-management')).toBeInTheDocument();
      
      // Test pricing tab
      await user.click(screen.getByText('Pricing'));
      expect(screen.getByTestId('event-pricing-management')).toBeInTheDocument();
    });

    it('ensures data display elements show correct information', async () => {
      renderEventDetail();
      
      expect(screen.getByTestId('event-name-header')).toHaveTextContent('Test Event');
      expect(screen.getByTestId('event-dates-header')).toHaveTextContent('6/15/2024 - 6/16/2024');
      expect(screen.getByTestId('event-type-header')).toHaveTextContent('Horse Trials');
    });

    it('ensures action buttons are present and functional', async () => {
      renderEventDetail();
      
      const editButton = screen.getByText('Edit Event');
      expect(editButton).toBeInTheDocument();
      expect(editButton).toBeEnabled();
    });
  });
}); 