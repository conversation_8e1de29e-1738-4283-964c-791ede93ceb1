import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Plus, Trash2, Target } from 'lucide-react';
import { useLevels, useEventLevels, useCreateEventLevel, useDeleteEventLevel } from '@/hooks/useLevels';

interface EventLevelsManagementProps {
  eventId: string;
  eventName: string;
  selectedDate?: string;
}

export const EventLevelsManagement: React.FC<EventLevelsManagementProps> = ({
  eventId,
  eventName,
  selectedDate
}) => {
  const [selectedLevelId, setSelectedLevelId] = useState('');
  const [selectedDiscipline, setSelectedDiscipline] = useState('all');

  const { data: allLevels, isLoading, error } = useLevels(selectedDiscipline === 'all' ? undefined : selectedDiscipline);
  const { data: eventLevels } = useEventLevels(eventId);
  const createEventLevel = useCreateEventLevel();
  const deleteEventLevel = useDeleteEventLevel();

  const availableLevels = allLevels?.filter(level => 
    level.id && level.id.trim() !== '' &&
    !eventLevels?.some(eventLevel => eventLevel.level_id === level.id)
  );

  const handleAddLevel = () => {
    if (selectedLevelId && selectedLevelId.trim() !== '') {
      createEventLevel.mutate({
        event_id: eventId,
        level_id: selectedLevelId,
        is_enabled: true
      }, {
        onSuccess: () => {
          setSelectedLevelId('');
        }
      });
    }
  };

  const handleRemoveLevel = (eventLevelId: string) => {
    deleteEventLevel.mutate(eventLevelId);
  };

  return (
    <div data-testid="event-levels-management">
      <Card className="border-green-200">
        <CardHeader>
          <CardTitle className="text-lg text-green-800 flex items-center">
            <Target className="w-5 h-5 mr-2" />
            Event Levels - {eventName}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="discipline-filter">Filter by Discipline</Label>
              <Select value={selectedDiscipline} onValueChange={setSelectedDiscipline} data-testid="discipline-filter">
                <SelectTrigger data-testid="discipline-filter">
                  <SelectValue placeholder="All disciplines" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All disciplines</SelectItem>
                  <SelectItem value="dressage">Dressage</SelectItem>
                  <SelectItem value="eventing">Eventing</SelectItem>
                  <SelectItem value="show_jumping">Show Jumping</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="level-select">Add Level</Label>
              <Select value={selectedLevelId} onValueChange={setSelectedLevelId}>
                <SelectTrigger data-testid="level-select">
                  <SelectValue placeholder="Select a level" />
                </SelectTrigger>
                <SelectContent>
                  {availableLevels?.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      {level.name} ({level.discipline})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button 
                onClick={handleAddLevel}
                disabled={!selectedLevelId || selectedLevelId.trim() === '' || createEventLevel.isPending}
                className="bg-green-600 hover:bg-green-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Level
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-green-800">Current Event Levels</h4>
            {eventLevels && eventLevels.length > 0 ? (
              <div className="grid grid-cols-1 gap-2">
                {eventLevels.map((eventLevel) => (
                  <div key={eventLevel.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline" className="text-green-700" data-testid="level-name">
                        {eventLevel.levels?.name}
                      </Badge>
                      {eventLevel.levels?.discipline && (
                        <span className="text-sm text-gray-600">
                          {eventLevel.levels.discipline}
                        </span>
                      )}
                      {eventLevel.levels?.description && (
                        <span className="text-xs text-gray-500">
                          {eventLevel.levels.description}
                        </span>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveLevel(eventLevel.id)}
                      className="text-red-600 border-red-300 hover:bg-red-50"
                      data-testid="remove-level"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 text-gray-500">
                <Target className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p>No levels added to this event yet.</p>
              </div>
            )}
          </div>

          {isLoading && (
            <div data-testid="loading">Loading...</div>
          )}
          {error && (
            <div data-testid="error">Error: {error.message}</div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
