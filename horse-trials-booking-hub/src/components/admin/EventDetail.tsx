import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Edit, ArrowLeft } from 'lucide-react';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { LocationManagement } from '@/components/admin/LocationManagement';
import { EventLevelsManagement } from '@/components/admin/EventLevelsManagement';
import { EventDressageTestsManagement } from '@/components/admin/EventDressageTestsManagement';
import { AdminBookings } from '@/components/admin/AdminBookings';
import { formatDateForDisplay } from '@/utils/dateUtils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { EventPricingManagement } from '@/components/admin/EventPricingManagement';
import { useUserRole } from '@/hooks/useUserRoles';
import { useSession } from '@supabase/auth-helpers-react';
import { useUpdateEvent } from '@/hooks/useEvents';
import type { Event } from '@/hooks/useEvents';

const formatEventType = (type: string) => {
  if (!type) return '';
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

interface EventDetailProps {
  event: Event;
  eventId: string;
}

export const EventDetail: React.FC<EventDetailProps> = ({ event, eventId }) => {
  const navigate = useNavigate();
  const updateEvent = useUpdateEvent();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [editEvent, setEditEvent] = useState({
    name: '',
    start_date: '',
    end_date: '',
    event_type: 'horse_trials' as 'horse_trials' | 'dressage' | 'hunter_jumper',
    is_active: true
  });
  const session = useSession();
  const user = session?.user;
  const { userRole } = useUserRole(!!user);

  React.useEffect(() => {
    if (event) {
      setEditEvent({
        name: event.name,
        start_date: event.start_date,
        end_date: event.end_date,
        event_type: event.event_type,
        is_active: event.is_active
      });
      
      if (event.start_date && !selectedDate) {
        setSelectedDate(event.start_date);
      }
    }
  }, [event, selectedDate]);

  const getAvailableDates = () => {
    if (!event) return [];
    
    const dates = [];
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);
    
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      dates.push(d.toISOString().split('T')[0]);
    }
    
    return dates;
  };

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    if (event) {
      setEditEvent({
        name: event.name,
        start_date: event.start_date,
        end_date: event.end_date,
        event_type: event.event_type,
        is_active: event.is_active
      });
    }
  };

  const handleUpdateEvent = () => {
    if (!editEvent.name) {
      toast({
        title: "Error",
        description: "Event name is required",
        variant: "destructive",
      });
      return;
    }

    // Log the payload being sent to updateEvent
    console.log('[EventDetail] handleUpdateEvent payload:', {
      id: eventId,
      ...editEvent
    });

    updateEvent.mutate({
      id: eventId,
      ...editEvent
    }, {
      onSuccess: () => {
        setIsEditing(false);
      }
    });
  };

  const handleBackToDashboard = () => {
    navigate('/admin/events');
  };

  const availableDates = getAvailableDates();

  // Guard: If event is undefined, show loading
  if (!event) {
    return <div>Loading event details...</div>;
  }

  // Guard: If editEvent is undefined, show loading (should not happen, but extra safety)
  if (!editEvent) {
    return <div>Loading event form...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-amber-50">
      <Header
        onBackToEvents={handleBackToDashboard}
        showBackButton={true}
        userRole={userRole}
      />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          {/* Event Header */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <div className="flex items-center mb-2">
                <Button
                  onClick={handleBackToDashboard}
                  className="mr-2 p-0 h-8 w-8"
                  variant="ghost"
                  size="sm"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Button>
                <h1 className="text-2xl font-bold text-green-800" data-testid="event-name-header">{event.name}</h1>
                <Badge
                  variant="outline"
                  className={`ml-3 border ${event.is_active
                    ? 'text-green-600 border-green-300'
                    : 'text-gray-400 border-gray-300'
                    }`}
                  data-testid="event-status-badge"
                >
                  {event.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div className="flex items-center text-sm text-gray-600 ml-10">
                <Calendar className="h-4 w-4 mr-2 text-green-600" />
                <span data-testid="event-dates-header">
                  {formatDateForDisplay(event.start_date)} - {formatDateForDisplay(event.end_date)}
                </span>
                <span className="mx-2 text-gray-400">|</span>
                <span className="font-semibold" data-testid="event-type-header">{formatEventType(event.event_type)}</span>
              </div>
            </div>
            <div className="mt-4 md:mt-0 ml-10 md:ml-0">
              {!isEditing && (
                <Button
                  onClick={handleEditClick}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  <Edit className="h-4 w-4 mr-2" /> Edit Event
                </Button>
              )}
            </div>
          </div>

          {/* Management Tabs */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Event / Locations & Activities</TabsTrigger>
              <TabsTrigger value="bookings">Bookings</TabsTrigger>
              <TabsTrigger value="levels">Levels & Tests</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-4">
              {isEditing ? (
                <Card className="border-amber-200">
                  <CardHeader>
                    <CardTitle className="text-amber-800">Edit Event Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="name">Event Name</Label>
                      <Input
                        id="name"
                        value={editEvent.name}
                        onChange={(e) => setEditEvent({ ...editEvent, name: e.target.value })}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="start_date">Start Date</Label>
                        <Input
                          id="start_date"
                          type="date"
                          value={editEvent.start_date}
                          onChange={(e) => setEditEvent({ ...editEvent, start_date: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="end_date">End Date</Label>
                        <Input
                          id="end_date"
                          type="date"
                          value={editEvent.end_date}
                          onChange={(e) => setEditEvent({ ...editEvent, end_date: e.target.value })}
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="event_type">Event Type</Label>
                      <Select
                        value={editEvent.event_type}
                        onValueChange={(value) => setEditEvent({ ...editEvent, event_type: value as 'horse_trials' | 'dressage' | 'hunter_jumper' })}
                      >
                        <SelectTrigger id="event_type">
                          <SelectValue placeholder="Select event type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="horse_trials">Horse Trials</SelectItem>
                          <SelectItem value="dressage">Dressage</SelectItem>
                          <SelectItem value="hunter_jumper">Hunter Jumper</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Label htmlFor="is_active">Active</Label>
                      <input
                        id="is_active"
                        type="checkbox"
                        checked={editEvent.is_active}
                        onChange={(e) => setEditEvent({ ...editEvent, is_active: e.target.checked })}
                      />
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" onClick={handleCancelEdit}>
                        Cancel
                      </Button>
                      <Button
                        onClick={handleUpdateEvent}
                        className="bg-gold text-primary hover:bg-gold-hover"
                        disabled={updateEvent.isPending}
                      >
                        {updateEvent.isPending ? 'Updating...' : 'Update Event'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-8">
                  <Card>
                    <CardContent className="p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="font-medium text-gray-700">Event Information</h3>
                          <div className="bg-gray-50 p-3 rounded space-y-2">
                            <div>
                              <span className="font-medium">Name:</span> <span data-testid="event-name-card">{event.name}</span>
                            </div>
                            <div>
                              <span className="font-medium">Type:</span> <span data-testid="event-type-card">{event.event_type}</span>
                            </div>
                            <div>
                              <span className="font-medium">Dates:</span> <span data-testid="event-dates-card">{formatDateForDisplay(event.start_date)} - {formatDateForDisplay(event.end_date)}</span>
                            </div>
                            {/* Organizer details: show if event.organizer exists, else fallback to organizer_id */}
                            {('organizer' in event && event.organizer && typeof event.organizer === 'object' && event.organizer !== null && 'full_name' in event.organizer && 'email' in event.organizer) ? (
                              (() => {
                                const organizer = event.organizer as { full_name: string; email: string; company_name?: string; business_name?: string };
                                return (
                                  <div>
                                    <span className="font-medium">Organizer:</span> {organizer.full_name} ({organizer.email})
                                    {organizer.company_name && (
                                      <div className="ml-4 text-sm text-gray-600">
                                        Company: {organizer.company_name}
                                      </div>
                                    )}
                                    {organizer.business_name && (
                                      <div className="ml-4 text-sm text-gray-600">
                                        Business: {organizer.business_name}
                                      </div>
                                    )}
                                  </div>
                                );
                              })()
                            ) : event.organizer_id && (
                              <div>
                                <span className="font-medium">Organizer ID:</span> {event.organizer_id}
                              </div>
                            )}
                          </div>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-700">Status</h3>
                          <div className="bg-gray-50 p-3 rounded flex items-center">
                            <Badge
                              variant="outline"
                              className={`border ${event.is_active ? 'text-green-600 border-green-300 bg-green-50' : 'text-gray-500 border-gray-300 bg-gray-100'}`}
                            >
                              {event.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Locations & Activities Section */}
                  <div>
                    <div className="mb-6">
                      <h2 className="text-2xl font-bold text-green-800 mb-2">Locations & Activities</h2>
                      <p className="text-gray-600">Manage locations and schedule activities for each event date</p>
                    </div>

                    {/* Date Selector */}
                    <Card className="mb-6 border-green-200">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-green-800 flex items-center">
                          <Calendar className="h-5 w-5 mr-2" />
                          Working on Event Date
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center space-x-4">
                          <div className="flex-1">
                            <Label htmlFor="date-selector">Select Event Date to Work On</Label>
                            <Select value={selectedDate} onValueChange={(value) => {
                              console.log('[EventDetail] Date selector changed from:', selectedDate, 'to:', value);
                              setSelectedDate(value);
                            }}>
                              <SelectTrigger>
                                <SelectValue placeholder="Choose an event date" />
                              </SelectTrigger>
                              <SelectContent>
                                {availableDates.map((date) => (
                                  <SelectItem key={date} value={date}>
                                    {formatDateForDisplay(date)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          {selectedDate && (
                            <div className="text-sm text-gray-600">
                              Managing: <span className="font-medium">{formatDateForDisplay(selectedDate)}</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    <LocationManagement
                      eventId={eventId}
                      eventName={event.name}
                      selectedDate={selectedDate}
                      onBack={handleBackToDashboard}
                    />
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="bookings" className="mt-4">
              <AdminBookings event={event} />
            </TabsContent>

            <TabsContent value="levels" className="mt-4">
              <EventLevelsManagement eventId={eventId} eventName={event.name} />
              <EventDressageTestsManagement eventId={eventId} eventName={event.name} />
            </TabsContent>

            <TabsContent value="pricing" className="mt-4">
              <EventPricingManagement eventId={eventId} />
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}; 