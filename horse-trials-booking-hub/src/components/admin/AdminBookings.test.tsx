import { render, screen, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { AdminBookings } from './AdminBookings';
import { useBookings, useDeleteBooking, useUpdateBooking } from '../../hooks/useBookings';
import { useAuth } from '../../hooks/useAuth';
import { useLocations } from '../../hooks/useLocations';
import { useEventDressageTests } from '../../hooks/useDressageTests';
import { useEventLevels } from '../../hooks/useLevels';
import userEvent from '@testing-library/user-event';

// Mock the hooks
vi.mock('../../hooks/useBookings');
vi.mock('../../hooks/useAuth');
vi.mock('../../hooks/useLocations');
vi.mock('../../hooks/useDressageTests');
vi.mock('../../hooks/useLevels');

const mockUseBookings = vi.mocked(useBookings);
const mockUseAuth = vi.mocked(useAuth);
const mockUseLocations = vi.mocked(useLocations);
const mockUseEventDressageTests = vi.mocked(useEventDressageTests);
const mockUseEventLevels = vi.mocked(useEventLevels);
const mockUseDeleteBooking = vi.mocked(useDeleteBooking);
const mockUseUpdateBooking = vi.mocked(useUpdateBooking);

// Mock data
const mockEvent = {
  id: '1',
  name: 'Test Event',
  date: '2024-01-15',
  location_id: '1',
  organizer_id: '1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockBookings = [
  {
    booking_id: '1',
    user_id: 'user1',
    rider_name: 'John Doe',
    horse_name: 'Thunder',
    booking_code: 'ABC123',
    activity_description: 'Dressage Test',
    payment_status: 'paid',
    booking_created_at: '2024-01-01T00:00:00Z',
    booking_event_dressage_test_fk: null,
    payer_name: 'John Doe',
    payer_email: '<EMAIL>',
    activity_date: '2024-01-15',
    total_price: 100,
    stripe_payment_id: 'pi_123',
    time_slot_id: 'slot1',
    time_slot_start_time: '2024-01-15T10:00:00Z',
    time_slot_end_time: '2024-01-15T11:00:00Z',
    time_slot_level: 'Intro',
    time_slot_is_booked: true,
    time_slot_activity_fk: 'activity1',
    time_slot_location_fk: 'location1',
    location_id: 'location1',
    location_name: 'Arena 1',
    location_event_id: '1',
    location_activity_type: 'dressage',
    activity_id: 'activity1',
    activity_specific_type: 'dressage',
    activity_level: 'Intro',
    activity_specific_description: 'Intro Test A',
    activity_slot_duration_minutes: 60,
    event_dressage_test_link_id: 'test1',
    dressage_test_id: 'test1',
    dressage_test_label: 'Intro Test A',
    dressage_test_description: 'Introductory dressage test',
    dressage_level_id: 'level1',
    dressage_level_name: 'Intro',
    dressage_level_discipline: 'Dressage'
  },
  {
    booking_id: '2',
    user_id: 'user2',
    rider_name: 'Jane Smith',
    horse_name: 'Storm',
    booking_code: 'DEF456',
    activity_description: 'Show Jumping',
    payment_status: 'paid',
    booking_created_at: '2024-01-01T00:00:00Z',
    booking_event_dressage_test_fk: null,
    payer_name: 'Jane Smith',
    payer_email: '<EMAIL>',
    activity_date: '2024-01-15',
    total_price: 150,
    stripe_payment_id: 'pi_456',
    time_slot_id: 'slot2',
    time_slot_start_time: '2024-01-15T14:00:00Z',
    time_slot_end_time: '2024-01-15T15:00:00Z',
    time_slot_level: 'Prelim',
    time_slot_is_booked: true,
    time_slot_activity_fk: 'activity2',
    time_slot_location_fk: 'location2',
    location_id: 'location2',
    location_name: 'Arena 2',
    location_event_id: '1',
    location_activity_type: 'show_jumping',
    activity_id: 'activity2',
    activity_specific_type: 'show_jumping',
    activity_level: 'Prelim',
    activity_specific_description: 'Prelim Show Jumping',
    activity_slot_duration_minutes: 60,
    event_dressage_test_link_id: null,
    dressage_test_id: null,
    dressage_test_label: null,
    dressage_test_description: null,
    dressage_level_id: null,
    dressage_level_name: null,
    dressage_level_discipline: null
  }
];

const mockLocations = [
  { id: '1', name: 'Arena 1', event_id: '1' },
  { id: '2', name: 'Arena 2', event_id: '1' }
];

const mockDressageTests = [
  { id: '1', name: 'Intro Test A', level: 'Intro' },
  { id: '2', name: 'Prelim Test 1', level: 'Prelim' }
];

const mockLevels = [
  { id: '1', name: 'Intro', event_id: '1' },
  { id: '2', name: 'Prelim', event_id: '1' }
];

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <div>{children}</div>
);

describe('AdminBookings', () => {
  beforeEach(() => {
    console.log('=== BEFORE EACH DEBUG ===');
    console.log('Setting up mocks...');
    
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      loading: false,
      signIn: vi.fn(),
      signOut: vi.fn(),
      signUp: vi.fn()
    });

    mockUseBookings.mockReturnValue({
      data: mockBookings,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    });

    console.log('Mock useBookings return value:', {
      data: mockBookings,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    });

    mockUseLocations.mockReturnValue({
      data: mockLocations,
      isLoading: false,
      error: null
    });

    mockUseEventDressageTests.mockReturnValue({
      data: mockDressageTests,
      isLoading: false,
      error: null
    });

    mockUseEventLevels.mockReturnValue({
      data: mockLevels,
      isLoading: false,
      error: null
    });

    mockUseDeleteBooking.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
      error: null
    });

    mockUseUpdateBooking.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
      error: null
    });

    console.log('All mocks set up');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the component with event title', () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      expect(screen.getByText('Booking Management - Test Event')).toBeInTheDocument();
    });

    it('calls useBookings with correct event ID', () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Verify that useBookings was called with the correct event ID
      expect(mockUseBookings).toHaveBeenCalledWith('1');
    });

    it('receives mock data correctly', () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Verify that the component shows the correct number of bookings
      expect(screen.getByText('Showing 2 bookings')).toBeInTheDocument();
    });

    it('shows any bookings at all', () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Debug: Log all text content to see what's actually rendered
      console.log('=== TEST DEBUG ===');
      console.log('All text content:', document.body.textContent);
      
      // The component shows "Bookings (2)" in the header, which means it has 2 bookings
      expect(screen.getByText('Bookings (2)')).toBeInTheDocument();
      
      // The component shows "Showing 2 bookings" in the filter summary
      expect(screen.getByText('Showing 2 bookings')).toBeInTheDocument();
      
      // The component should show the rider names (use getAllByText since they appear multiple times)
      expect(screen.getAllByText('John Doe').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Jane Smith').length).toBeGreaterThan(0);
      
      // The component should show the horse names (they are displayed as "Horse: Thunder")
      expect(screen.getByText('Horse: Thunder')).toBeInTheDocument();
      expect(screen.getByText('Horse: Storm')).toBeInTheDocument();
    });

    it('debug: shows raw booking count', () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Check if the component shows the raw booking count (before filtering)
      // This will help us understand if the issue is with data loading or filtering
      expect(screen.getByText('Bookings (2)')).toBeInTheDocument();
    });

    it('displays booking information', () => {
      // Add debug logging to see what's happening
      console.log('=== TEST DEBUG ===');
      console.log('Mock event:', mockEvent);
      console.log('Mock bookings:', mockBookings);
      console.log('Mock useBookings return value:', {
        data: mockBookings,
        isLoading: false,
        error: null,
        refetch: vi.fn()
      });

      render(<AdminBookings event={mockEvent} />, { wrapper });

      // There may be multiple 'John Doe' elements (rider and payer); this is intentional
      expect(screen.getAllByText('John Doe').length).toBeGreaterThan(0);
      expect(screen.getByText('Horse: Thunder')).toBeInTheDocument();
      // There may be multiple 'Jane Smith' elements (rider and payer); this is intentional
      expect(screen.getAllByText('Jane Smith').length).toBeGreaterThan(0);
      expect(screen.getByText('Horse: Storm')).toBeInTheDocument();
    });

    it('shows edit and delete buttons for each booking', () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Check that Edit and Delete buttons are present
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      expect(editButtons.length).toBeGreaterThan(0);
      
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      expect(deleteButtons.length).toBeGreaterThan(0);
    });
  });

  describe('User Interactions', () => {
    it('opens edit dialog when edit button is clicked', async () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Debug: Log what buttons are available before clicking
      console.log('=== EDIT DIALOG DEBUG ===');
      console.log('All buttons before click:', screen.getAllByRole('button').map(b => b.textContent));
      
      // Find and click the first Edit button
      const editButtons = screen.getAllByText('Edit');
      console.log('Found edit buttons:', editButtons.length);
      expect(editButtons.length).toBeGreaterThan(0);
      
      await userEvent.click(editButtons[0]);

      // Debug: Log what's rendered after clicking
      console.log('All text content after click:', document.body.textContent);
      
      // Wait for dialog to open and check for dialog content
      // The dialog is rendered in a portal, so we need to check document.body
      await waitFor(() => {
        expect(document.body.textContent).toContain('Edit Booking');
        expect(document.body.textContent).toContain('Cancel');
        expect(document.body.textContent).toContain('Update Booking');
      });

      // Debug: Check if Update Booking button exists in the portal
      const updateButtons = document.querySelectorAll('button');
      const updateButtonTexts = Array.from(updateButtons).map(b => b.textContent);
      console.log('All button texts in document:', updateButtonTexts);
      console.log('Update Booking buttons found:', updateButtonTexts.filter(t => t?.includes('Update Booking')).length);
      
      // The dialog should be open with the correct content
      expect(document.body.textContent).toContain('Edit Booking');
      expect(document.body.textContent).toContain('Cancel');
      expect(document.body.textContent).toContain('Update Booking');
    });
  });

  describe('Loading States', () => {
    it('shows loading state', () => {
      mockUseBookings.mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
        refetch: vi.fn()
      });

      render(<AdminBookings event={mockEvent} />, { wrapper });

      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('shows no bookings message when no bookings exist', () => {
      mockUseBookings.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
        refetch: vi.fn()
      });

      render(<AdminBookings event={mockEvent} />, { wrapper });

      expect(screen.getByText(/no bookings found for this event/i)).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('shows error state', () => {
      mockUseBookings.mockReturnValue({
        data: [],
        isLoading: false,
        error: { message: 'Failed to load bookings' },
        refetch: vi.fn()
      });

      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Check for the actual error message
      expect(screen.getByText('Failed to load bookings')).toBeInTheDocument();
    });
  });

  describe('UI Element Regression', () => {
    it('ensures all critical UI elements are present in the default state', async () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Header and event name
      expect(screen.getByText('Booking Management - Test Event')).toBeInTheDocument();
      expect(screen.getByText('Bookings (2)')).toBeInTheDocument();

      // Filters
      expect(screen.getByPlaceholderText(/Search by participant/i)).toBeInTheDocument();
      expect(screen.getByText('All Locations')).toBeInTheDocument();
      expect(screen.getByText('All Levels')).toBeInTheDocument();
      expect(screen.getByText('All Activities')).toBeInTheDocument();
      expect(screen.getByText('All Tests')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Reset Filters/i })).toBeInTheDocument();

      // Group by selector (combobox and default value)
      const comboboxes = screen.getAllByRole('combobox');
      // Find the group by combobox by its text content (default is 'Group by Location')
      const groupByCombobox = comboboxes.find(cb => cb.textContent && cb.textContent.toLowerCase().includes('group by'));
      expect(groupByCombobox).toBeTruthy();
      // Check that the default value is present
      expect(screen.getByText('Group by Location')).toBeInTheDocument();

      // Booking group headers (at least one)
      expect(screen.getAllByText(/- 1 Booking|Bookings/).length).toBeGreaterThan(0);

      // Booking info fields
      expect(screen.getAllByText('John Doe').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Jane Smith').length).toBeGreaterThan(0);
      expect(screen.getByText('Horse: Thunder')).toBeInTheDocument();
      expect(screen.getByText('Horse: Storm')).toBeInTheDocument();
      expect(screen.getAllByText('paid').length).toBeGreaterThan(0);
      expect(screen.getAllByText(/Dressage Test|Show Jumping/).length).toBeGreaterThan(0);

      // Action buttons
      expect(screen.getAllByRole('button', { name: /edit/i }).length).toBeGreaterThan(0);
      expect(screen.getAllByRole('button', { name: /delete/i }).length).toBeGreaterThan(0);
    });

    it('ensures Edit dialog appears with expected elements when Edit is clicked', async () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });
      const editButtons = screen.getAllByText('Edit');
      expect(editButtons.length).toBeGreaterThan(0);
      await userEvent.click(editButtons[0]);
      await waitFor(() => {
        expect(document.body.textContent).toContain('Edit Booking');
        expect(document.body.textContent).toContain('Cancel');
        expect(document.body.textContent).toContain('Update Booking');
      });
    });

    it('ensures Delete dialog appears with expected elements when Delete is clicked', async () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      expect(deleteButtons.length).toBeGreaterThan(0);
      await userEvent.click(deleteButtons[0]);
      await waitFor(() => {
        expect(document.body.textContent).toMatch(/Are you sure you want to delete|Delete Booking|Cancel/);
      });
    });
  });
}); 