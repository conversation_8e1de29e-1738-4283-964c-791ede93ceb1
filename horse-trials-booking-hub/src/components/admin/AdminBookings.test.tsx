import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { AdminBookings } from './AdminBookings';
import { useBookings, useDeleteBooking, useUpdateBooking } from '../../hooks/useBookings';
import { useEventDressageTests } from '../../hooks/useDressageTests';

// Mock the hooks
vi.mock('../../hooks/useBookings');
vi.mock('../../hooks/useDressageTests');
vi.mock('../../hooks/useDeleteBooking');
vi.mock('../../hooks/useUpdateBooking');

const mockUseBookings = vi.mocked(useBookings);
const mockUseEventDressageTests = vi.mocked(useEventDressageTests);
const mockDeleteMutation = vi.fn().mockResolvedValue({ success: true });
const mockUpdateMutation = vi.fn().mockResolvedValue({ success: true });

const mockToast = vi.fn();

// Mock toast
vi.mock('../../../hooks/use-toast', () => ({
  useToast: () => ({
    toast: mockToast,
  }),
}));

describe('AdminBookings', () => {
  let queryClient: QueryClient;

  const mockEvent = {
    id: 'event-1',
    name: 'Test Event',
  };

  const mockBookings = [
    {
      booking_id: 'booking-1',
      booking_code: 'code-1',
      participant_name: 'John Doe',
      horse_name: 'Thunder',
      payer_name: 'John Doe',
      payer_email: '<EMAIL>',
      payment_status: 'paid',
      event_dressage_test_link_id: 'dressage-1',
      dressage_test_label: 'Intro Test A',
      dressage_level_name: 'Intro',
      location_name: 'Arena 1',
      time_slot_start_time: '2024-01-15T10:00:00Z',
      activity_specific_type: 'dressage',
      time_slot_level: 'Beginner',
    },
    {
      booking_id: 'booking-2',
      booking_code: 'code-2',
      participant_name: 'Jane Smith',
      horse_name: 'Storm',
      payer_name: 'Jane Smith',
      payer_email: '<EMAIL>',
      payment_status: 'unpaid',
      event_dressage_test_link_id: null,
      dressage_test_label: null,
      dressage_level_name: null,
      location_name: 'Arena 2',
      time_slot_start_time: '2024-01-15T11:00:00Z',
      activity_specific_type: 'show_jumping',
      time_slot_level: 'Intermediate',
    },
    {
      booking_id: 'booking-3',
      booking_code: 'code-3',
      participant_name: 'Alice Brown',
      horse_name: 'Lightning',
      payer_name: 'Alice Brown',
      payer_email: '<EMAIL>',
      payment_status: 'pending',
      event_dressage_test_link_id: 'dressage-2',
      dressage_test_label: 'Intro Test B',
      dressage_level_name: 'Intro',
      location_name: 'Arena 1',
      time_slot_start_time: '2024-01-15T09:00:00Z',
      activity_specific_type: 'dressage',
      time_slot_level: 'Advanced',
    },
  ];

  const mockDressageTests = [
    {
      id: 'dressage-1',
      dressage_test_library: {
        label: 'Intro Test A',
        description: 'Introductory dressage test',
      },
    },
    {
      id: 'dressage-2',
      dressage_test_library: {
        label: 'Intro Test B',
        description: 'Another introductory test',
      },
    },
  ];

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    // Mock the hooks
    mockUseBookings.mockReturnValue({
      data: mockBookings,
      isLoading: false,
      error: null,
    } as any);

    mockUseEventDressageTests.mockReturnValue({
      data: mockDressageTests,
      isLoading: false,
      error: null,
    } as any);

    // Mock mutation hooks
    vi.mocked(useDeleteBooking).mockReturnValue({
      mutateAsync: mockDeleteMutation,
      isPending: false,
    } as any);

    vi.mocked(useUpdateBooking).mockReturnValue({
      mutateAsync: mockUpdateMutation,
      isPending: false,
    } as any);

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  it('renders booking list correctly', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    expect(screen.getByText('Booking Management - Test Event')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Thunder')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Storm')).toBeInTheDocument();
  });

  it('shows dressage test information when available', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    expect(screen.getByText('Intro Test A')).toBeInTheDocument();
    expect(screen.getByText('Intro')).toBeInTheDocument();
  });

  it('handles bookings without dressage tests', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    // Should not show dressage test info for booking-2
    expect(screen.queryByText('Intro Test B')).not.toBeInTheDocument();
  });

  it('handles loading state', () => {
    mockUseBookings.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    } as any);

    render(<AdminBookings event={mockEvent} />, { wrapper });

    expect(screen.getByText('Loading bookings...')).toBeInTheDocument();
  });

  it('handles error state', () => {
    mockUseBookings.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to load bookings'),
    } as any);

    render(<AdminBookings event={mockEvent} />, { wrapper });

    expect(screen.getByText(/error loading bookings/i)).toBeInTheDocument();
  });

  it('sorts bookings by participant name', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    const sortSelect = screen.getByRole('combobox', { name: /sort by/i });
    fireEvent.change(sortSelect, { target: { value: 'name' } });

    const names = screen.getAllByText(/^(John|Jane|Alice)/).map(el => el.textContent);
    expect(names).toEqual(['Alice Brown', 'Jane Smith', 'John Doe']);
  });

  it('sorts bookings by date', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    const sortSelect = screen.getByRole('combobox', { name: /sort by/i });
    fireEvent.change(sortSelect, { target: { value: 'date' } });

    const times = mockBookings.map(b => new Date(b.time_slot_start_time).getTime());
    const sortedTimes = [...times].sort((a, b) => a - b);
    expect(times).toEqual(sortedTimes);
  });

  it('changes sort direction', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    const sortSelect = screen.getByRole('combobox', { name: /sort by/i });
    fireEvent.change(sortSelect, { target: { value: 'name' } });

    const directionButton = screen.getByRole('button', { name: /change sort direction/i });
    fireEvent.click(directionButton);

    const names = screen.getAllByText(/^(John|Jane|Alice)/).map(el => el.textContent);
    expect(names).toEqual(['John Doe', 'Jane Smith', 'Alice Brown']);
  });

  it('applies multiple filters simultaneously', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    // Apply location filter
    const locationSelect = screen.getByRole('combobox', { name: /filter by location/i });
    fireEvent.change(locationSelect, { target: { value: 'Arena 1' } });

    // Apply activity type filter
    const activitySelect = screen.getByRole('combobox', { name: /filter by activity/i });
    fireEvent.change(activitySelect, { target: { value: 'dressage' } });

    // Apply payment status filter
    const paymentSelect = screen.getByRole('combobox', { name: /filter by payment/i });
    fireEvent.change(paymentSelect, { target: { value: 'paid' } });

    // Should only show John Doe's booking
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    expect(screen.queryByText('Alice Brown')).not.toBeInTheDocument();
  });

  it('resets all filters when reset button is clicked', async () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    // Apply some filters first
    const locationSelect = screen.getByRole('combobox', { name: /filter by location/i });
    fireEvent.change(locationSelect, { target: { value: 'Arena 1' } });

    const searchInput = screen.getByPlaceholderText(/search by participant/i);
    fireEvent.change(searchInput, { target: { value: 'John' } });

    // Click reset button
    const resetButton = screen.getByRole('button', { name: /reset filters/i });
    fireEvent.click(resetButton);

    // All bookings should be visible again
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Alice Brown')).toBeInTheDocument();

    // Search input should be cleared
    expect(searchInput).toHaveValue('');

    // Filters should be reset to 'all'
    expect(locationSelect).toHaveValue('all');
  });

  it('groups bookings by time correctly', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    const groupSelect = screen.getByRole('combobox', { name: /group by/i });
    fireEvent.change(groupSelect, { target: { value: 'time' } });

    // Check group headers
    expect(screen.getByText(/2024-01-15 - 3 Bookings/i)).toBeInTheDocument();
  });

  it('groups bookings by payment status correctly', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    const groupSelect = screen.getByRole('combobox', { name: /group by/i });
    fireEvent.change(groupSelect, { target: { value: 'paid' } });

    // Check group headers
    expect(screen.getByText(/paid - 1 booking/i)).toBeInTheDocument();
    expect(screen.getByText(/unpaid - 1 booking/i)).toBeInTheDocument();
    expect(screen.getByText(/pending - 1 booking/i)).toBeInTheDocument();
  });

  it('groups bookings by name initial correctly', () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    const groupSelect = screen.getByRole('combobox', { name: /group by/i });
    fireEvent.change(groupSelect, { target: { value: 'name' } });

    // Check group headers
    expect(screen.getByText(/A - 1 booking/i)).toBeInTheDocument(); // Alice
    expect(screen.getByText(/J - 2 bookings/i)).toBeInTheDocument(); // John and Jane
  });

  describe('accessibility', () => {
    it('has accessible combobox controls', () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Check for proper combobox labels
      expect(screen.getByRole('combobox', { name: /filter by location/i })).toHaveAttribute('aria-label', 'Filter by location');
      expect(screen.getByRole('combobox', { name: /filter by level/i })).toHaveAttribute('aria-label', 'Filter by level');
      expect(screen.getByRole('combobox', { name: /filter by activity/i })).toHaveAttribute('aria-label', 'Filter by activity type');
      expect(screen.getByRole('combobox', { name: /filter by payment/i })).toHaveAttribute('aria-label', 'Filter by payment status');
      expect(screen.getByRole('combobox', { name: /sort by/i })).toHaveAttribute('aria-label', 'Sort by');
      expect(screen.getByRole('combobox', { name: /group by/i })).toHaveAttribute('aria-label', 'Group by');
    });

    it('has accessible buttons with proper labels', () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Check for proper button labels
      expect(screen.getByRole('button', { name: /reset filters/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /change sort direction/i })).toBeInTheDocument();
      
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      expect(editButtons.length).toBeGreaterThan(0);

      const deleteButtons = screen.getAllByRole('button', { name: /delete booking/i });
      expect(deleteButtons.length).toBeGreaterThan(0);
    });

    it('maintains focus management in dialogs', () => {
      render(<AdminBookings event={mockEvent} />, { wrapper });

      // Open edit dialog
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      fireEvent.click(editButtons[0]);

      // First element should be focused
      expect(screen.getByLabelText(/participant name/i)).toHaveFocus();

      // Close dialog with escape
      fireEvent.keyDown(document.activeElement!, { key: 'Escape' });

      // Dialog should be closed
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  it('shows loading state during delete mutation', async () => {
    // Mock loading state with isPending
    vi.mocked(useDeleteBooking).mockReturnValue({
      mutateAsync: mockDeleteMutation,
      isPending: true,
    } as any);

    render(<AdminBookings event={mockEvent} />, { wrapper });

    // Click delete button to open dialog
    const deleteButtons = screen.getAllByRole('button', { name: /delete booking/i });
    fireEvent.click(deleteButtons[0]);

    // Find and click the delete button in the alert dialog
    const confirmDeleteButton = screen.getByRole('button', { name: /^delete$/i });
    
    // Should be disabled when isPending is true
    expect(confirmDeleteButton).toHaveAttribute('disabled');
  });

  it('shows loading state during update mutation', async () => {
    // Mock loading state with isPending
    vi.mocked(useUpdateBooking).mockReturnValue({
      mutateAsync: mockUpdateMutation,
      isPending: true,
    } as any);

    render(<AdminBookings event={mockEvent} />, { wrapper });

    // Click edit button to open dialog
    const editButtons = screen.getAllByRole('button', { name: /edit/i });
    fireEvent.click(editButtons[0]);

    // Find the save button
    const saveButton = screen.getByRole('button', { name: /save/i });
    
    // Should be disabled when isPending is true
    expect(saveButton).toHaveAttribute('disabled');
  });

  it('handles delete mutation error', async () => {
    const mockError = new Error('Failed to delete booking');
    mockDeleteMutation.mockRejectedValueOnce(mockError);

    render(<AdminBookings event={mockEvent} />, { wrapper });

    // Click delete button to open dialog
    const deleteButtons = screen.getAllByRole('button', { name: /delete booking/i });
    fireEvent.click(deleteButtons[0]);

    // Find and click the delete button in the alert dialog
    const confirmDeleteButton = screen.getByRole('button', { name: /^delete$/i });
    fireEvent.click(confirmDeleteButton);

    // Should show error toast
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith(expect.objectContaining({
        title: 'Error',
        description: expect.stringContaining('delete booking'),
        variant: 'destructive',
      }));
    });
  });

  it('handles update mutation error', async () => {
    const mockError = new Error('Failed to update booking');
    mockUpdateMutation.mockRejectedValueOnce(mockError);

    render(<AdminBookings event={mockEvent} />, { wrapper });

    // Click edit button to open dialog
    const editButtons = screen.getAllByRole('button', { name: /edit/i });
    fireEvent.click(editButtons[0]);

    // Find and click the save button
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);

    // Should show error toast
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith(expect.objectContaining({
        title: 'Error',
        description: expect.stringContaining('update booking'),
        variant: 'destructive',
      }));
    });
  });

  it('validates required fields before update', async () => {
    render(<AdminBookings event={mockEvent} />, { wrapper });

    // Click edit button to open dialog
    const editButtons = screen.getAllByRole('button', { name: /edit/i });
    fireEvent.click(editButtons[0]);

    // Clear required field
    const participantNameInput = screen.getByLabelText(/participant name/i);
    fireEvent.change(participantNameInput, { target: { value: '' } });

    // Try to save
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);

    // Should show validation toast
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith(expect.objectContaining({
        title: 'Validation Error',
        description: expect.stringContaining('required'),
        variant: 'destructive',
      }));
    });
  });
}); 