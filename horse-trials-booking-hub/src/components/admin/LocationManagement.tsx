import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Target, Plus, Edit, Trash, AlertCircle, ChevronDown, ChevronUp, Clock, ListTodo, Calendar, Loader2 } from 'lucide-react';
import { useLocations, useCreateLocation, useUpdateLocation, useDeleteLocation, Location } from '@/hooks/useLocations';
import { ActivityManagement } from './ActivityManagement'; // Stays as Arena props for now
import { LocationScheduleView } from './LocationScheduleView'; // Changed from ArenaScheduleView
import { TimeSlotManagement } from './TimeSlotManagement'; // Stays as Arena props for now
// import { useTimeSlotsForDate, ScheduleViewTimeSlot } from '@/hooks/useTimeSlotsForDate'; // No longer needed here
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { DEFAULT_SLOT_DURATION_MINUTES } from "@/constants/app-defaults";
import { supabase } from '@/integrations/supabase/client'; 
import { type Event, useEvent } from '@/hooks/useEvents'; // Import useEvent from useEvents
import { useEventLevels } from '@/hooks/useLevels'; // Import useEventLevels from useLevels
import { formatDateForDisplay } from '@/utils/dateUtils';

interface LocationManagementProps {
  eventId: string;
  eventName: string;
  selectedDate?: string;
  onBack: () => void;
}

const locationTypeOptions = {
  horse_trials: [
    { value: 'dressage', label: 'Dressage' },
    { value: 'show_jumping', label: 'Show Jumping' },
    { value: 'cross_country', label: 'Cross Country' }
  ],
  dressage: [
    { value: 'dressage', label: 'Dressage' }
  ],
  hunter_jumper: [
    { value: 'show_jumping', label: 'Show Jumping' }
  ]
};

export const LocationManagement: React.FC<LocationManagementProps> = ({ eventId, eventName, selectedDate, onBack }) => {
  const { toast } = useToast();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [expandedLocations, setExpandedLocations] = useState<Set<string>>(new Set());

  const [currentEventDetails, setCurrentEventDetails] = useState<Event | null>(null);
  const [eventDetailsLoading, setEventDetailsLoading] = useState(true);
  const [eventDetailsError, setEventDetailsError] = useState<Error | null>(null); // Not directly used in UI yet, but good for debugging

  const [newLocation, setNewLocation] = useState({
    name: '',
    description: '',
    event_id: eventId,
    activity_type: '', // This will store the activity type/phase
  });
  const [editLocation, setEditLocation] = useState<Partial<Location> & { id: string }>({
    id: '',
    name: '',
    description: '',
    event_id: eventId,
    activity_type: '', // This will store the activity type/phase
  });
  const [editingLocationId, setEditingLocationId] = useState<string | null>(null);

  const { data: locations, isLoading, error } = useLocations(eventId);
  const { data: event } = useEvent(eventId); // Get event details to check event_type
  const { data: eventLevels, isLoading: levelsLoading } = useEventLevels(eventId); // Get event levels
  const createLocationMutation = useCreateLocation();
  const updateLocationMutation = useUpdateLocation();
  const deleteLocationMutation = useDeleteLocation();

  // Get the appropriate activity type options based on event type
  const getActivityTypeOptions = () => {
    if (!event?.event_type) return [];
    return locationTypeOptions[event.event_type] || [];
  };

  // Fetch event details for reference
  useEffect(() => {
    if (!eventId) {
      setEventDetailsLoading(false);
      setEventDetailsError(new Error("Event ID is missing."));
      return;
    }

    const fetchEvent = async () => {
      setEventDetailsLoading(true);
      setEventDetailsError(null);
      try {
        const { data, error: fetchError } = await supabase
          .from('events')
          .select('*')
          .eq('id', eventId)
          .single();

        if (fetchError) throw fetchError;
        setCurrentEventDetails(data);
      } catch (err: any) {
        setEventDetailsError(err);
        toast({ title: "Error fetching event details", description: err.message, variant: "destructive" });
      } finally {
        setEventDetailsLoading(false);
      }
    };

    fetchEvent();
  }, [eventId, toast]);

  const handleAddLocation = () => {
    if (!newLocation.name.trim()) {
      toast({ title: "Error", description: "Location name cannot be empty.", variant: "destructive" });
      return;
    }
    const locationData = {
      name: newLocation.name,
      event_id: eventId,
      description: newLocation.description || undefined,
      activity_type: newLocation.activity_type || null, // Keep as activity_type
    };

    createLocationMutation.mutate({
      ...locationData
    }, {
      onSuccess: () => {
        setShowCreateForm(false);
        setNewLocation({
          name: '',
          description: '',
          event_id: eventId,
          activity_type: '', // Keep as activity_type
        });
      }
    });
  };

  const handleUpdateLocation = () => {
    if (!editingLocationId || !editLocation.name?.trim()) {
      toast({ title: "Error", description: "Location name cannot be empty.", variant: "destructive" });
      return;
    }
    const locationData = {
      name: editLocation.name,
      event_id: eventId,
      description: editLocation.description || undefined,
      activity_type: editLocation.activity_type || null, // Keep as activity_type
    };
    updateLocationMutation.mutate({
      ...locationData,
      id: editingLocationId, // Ensure id is passed
    }, {
      onSuccess: () => {
        setEditingLocationId(null);
        setSelectedLocation(null);
      }
    });
  };

  const toggleLocationExpansion = (locationId: string) => {
    const newExpanded = new Set(expandedLocations);
    if (newExpanded.has(locationId)) {
      newExpanded.delete(locationId);
    } else {
      newExpanded.add(locationId);
    }
    setExpandedLocations(newExpanded);
  };

  const startEditLocation = (location: Location) => {
    setEditLocation({
      id: location.id,
      name: location.name,
      description: location.description || '',
      event_id: location.event_id,
      activity_type: location.activity_type || '', // Keep as activity_type
    });
    setSelectedLocation(location);
    setEditingLocationId(location.id);
  };

  const confirmDeleteLocation = (location: Location) => {
    setSelectedLocation(location);
    setDeleteDialogOpen(true);
  };

  const handleDeleteLocation = (locationId: string) => {
    if (selectedLocation) { // Ensure selectedLocation is not null
      deleteLocationMutation.mutate({ locationId: selectedLocation.id, eventId }, {
        onSuccess: () => {
          setDeleteDialogOpen(false);
          setSelectedLocation(null);
        }
      });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading locations...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-500">
        <CardHeader><CardTitle className="text-red-700">Error</CardTitle></CardHeader>
        <CardContent className="text-red-600">
          <p>Failed to load locations: {error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button
            variant="ghost"
            onClick={onBack}
            className="text-green-700 hover:bg-green-50 mb-2"
          >
            ← Back to Event Details
          </Button>
          <h3 className="text-xl font-semibold text-green-800">
            Location Management - {eventName}
            {selectedDate && (
              <span className="text-lg font-normal text-gray-600 ml-2">
                ({formatDateForDisplay(selectedDate)})
              </span>
            )}
          </h3>
        </div>
        <Button
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="bg-green-600 hover:bg-green-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Location
        </Button>
      </div>

      {showCreateForm && (
        <Card className="border-green-200">
          <CardHeader>
            <CardTitle className="text-green-800">Create New Location</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="location-name">Location Name*</Label>
                <Input
                  id="location-name"
                  value={newLocation.name}
                  onChange={(e) => setNewLocation({ ...newLocation, name: e.target.value })}
                  placeholder="e.g., Main Competition Ring"
                />
              </div>
              <div>
                <Label htmlFor="location-type">Activity</Label>
                <Select
                  value={newLocation.activity_type}
                  onValueChange={(value) => setNewLocation({ ...newLocation, activity_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select activity" />
                  </SelectTrigger>
                  <SelectContent>
                    {getActivityTypeOptions().map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description (optional)</Label>
              <Input
                id="description"
                value={newLocation.description}
                onChange={(e) => setNewLocation({ ...newLocation, description: e.target.value })}
                placeholder="Location description"
              />
            </div>

            <div className="flex space-x-2">
              <Button
                onClick={handleAddLocation}
                disabled={!newLocation.name || createLocationMutation.isPending}
                className="bg-green-600 hover:bg-green-700"
              >
                {createLocationMutation.isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Create Location
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowCreateForm(false)}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="space-y-4">
        {locations?.map((location) => {
          const isEditing = editingLocationId === location.id;
          if (location.id === undefined) {
            console.error('[LocationManagement] CRITICAL: Location object found with undefined id:', location);
          }
          return (
            <Card key={location.id} className={`border-green-200 ${isEditing ? 'ring-2 ring-amber-300' : ''}`}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  {!isEditing ? (
                    <>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <CardTitle className="text-lg text-green-800">{location.name}</CardTitle>
                          {location.activity_type && 
                            (() => {
                              const typeOption = getActivityTypeOptions().find(option => option.value === location.activity_type);
                              return <Badge variant="outline" className="bg-cream text-primary border-primary">{typeOption ? typeOption.label : location.activity_type}</Badge>;
                            })()}
                        </div>
                        {location.description && (
                           <p className="text-sm text-gray-600 mt-1">{location.description}</p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleLocationExpansion(location.id)}
                          className="text-green-600 border-green-300 hover:bg-green-50"
                        >
                          {expandedLocations.has(location.id) ? (
                            <>
                              <ChevronUp className="w-3 h-3 mr-1" />
                              Hide Details
                            </>
                          ) : (
                            <>
                              <ChevronDown className="w-3 h-3 mr-1" />
                              Manage Activities/Schedule
                            </>
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => startEditLocation(location)}
                          className="text-amber-600 border-amber-300 hover:bg-amber-50"
                        >
                          <Edit className="w-3 h-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => confirmDeleteLocation(location)}
                          className="text-red-600 border-red-300 hover:bg-red-50"
                          disabled={deleteLocationMutation.isPending && deleteLocationMutation.variables?.locationId === location.id}
                        >
                          {deleteLocationMutation.isPending && deleteLocationMutation.variables?.locationId === location.id ? <Loader2 className="mr-1 h-3 w-3 animate-spin" /> : <Trash className="w-3 h-3 mr-1" />}
                          Delete
                        </Button>
                      </div>
                    </>
                  ) : (
                    // Edit mode - inline form
                    <div className="w-full space-y-4">
                       <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor={`location-name-${location.id}`}>Location Name*</Label>
                          <Input
                            id={`location-name-${location.id}`}
                            value={editLocation.name}
                            onChange={(e) => setEditLocation({ ...editLocation, name: e.target.value })}
                          />
                        </div>
                        <div>
                            <Label htmlFor={`location-type-${location.id}`}>Activity</Label>
                            <Select
                              value={editLocation.activity_type}
                              onValueChange={(value) => setEditLocation({ ...editLocation, activity_type: value })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select activity" />
                              </SelectTrigger>
                              <SelectContent>
                                {getActivityTypeOptions().map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                        </div>
                      </div>
                      <div>
                        <Label htmlFor={`description-${location.id}`}>Description (optional)</Label>
                        <Input
                          id={`description-${location.id}`}
                          value={editLocation.description}
                          onChange={(e) => setEditLocation({ ...editLocation, description: e.target.value })}
                        />
                      </div>
                      <div className="flex space-x-2 pt-2">
                        <Button
                          onClick={handleUpdateLocation}
                          disabled={!editLocation.name || updateLocationMutation.isPending}
                          className="bg-gold text-primary hover:bg-gold-hover"
                        >
                          {updateLocationMutation.isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                          Update Location
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setEditingLocationId(null);
                            setSelectedLocation(null);
                          }}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardHeader>

              {expandedLocations.has(location.id) && !isEditing && (
                <CardContent className="pt-0">
                  <Tabs defaultValue="activities">
                    <TabsList className="w-full grid grid-cols-3">
                      <TabsTrigger value="activities" className="flex-1">
                        <ListTodo className="w-4 h-4 mr-2" />
                        Activities
                      </TabsTrigger>
                      <TabsTrigger value="schedule" className="flex-1">
                        <Calendar className="w-4 h-4 mr-2" />
                        Schedule View
                      </TabsTrigger>
                      <TabsTrigger value="timeslots" className="flex-1">
                        <Clock className="w-4 h-4 mr-2" />
                        Time Slots
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="activities" className="mt-4">
                      <ActivityManagement
                        locationId={location.id}
                        locationName={location.name}
                        eventId={eventId}
                        initialSelectedDate={selectedDate}
                        locationActivityType={location.activity_type}
                      />
                    </TabsContent>
                    <TabsContent value="schedule" className="mt-4">
                      <LocationScheduleView
                        locationId={location.id}
                        locationName={location.name}
                        selectedDate={selectedDate}
                      />
                    </TabsContent>
                    <TabsContent value="timeslots" className="mt-4">
                      <TimeSlotManagement
                        locationId={location.id}
                        locationName={location.name}
                        selectedDate={selectedDate}
                        eventId={eventId}
                      />
                    </TabsContent>
                  </Tabs>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      {(!locations || locations.length === 0) && (
        <Card className="bg-gray-50 border-gray-200">
          <CardContent className="text-center py-12">
            <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No locations yet</h3>
            <p className="text-gray-600 mb-4">Create your first location for this event.</p>
          </CardContent>
        </Card>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Location</AlertDialogTitle>
            <AlertDialogDescription>
              <div className="flex items-start space-x-2">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
                <div>
                  <p className="font-medium">Are you sure you want to delete this location?</p>
                  <p className="text-sm text-gray-500 mt-1">
                    This will permanently remove {selectedLocation?.name} and all associated activities and time slots. This action cannot be undone.
                  </p>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleDeleteLocation(selectedLocation!.id)}
              className="bg-red-600 hover:bg-red-700 text-white"
              disabled={deleteLocationMutation.isPending}
            >
              {deleteLocationMutation.isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};