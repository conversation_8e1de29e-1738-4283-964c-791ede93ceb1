
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Users, User, Shield } from 'lucide-react';
import { useUsers, useUpdateUserRole } from '@/hooks/useUserRoles';

export const UserManagement: React.FC = () => {
  const { data: users, isLoading } = useUsers();
  const updateUserRole = useUpdateUserRole();

  const handleRoleChange = (userId: string, newRole: 'admin' | 'organizer' | 'user') => {
    updateUserRole.mutate({ userId, role: newRole });
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'organizer':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading users...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-green-800">
          <Users className="w-5 h-5 mr-2" />
          User Management
        </CardTitle>
      </CardHeader>
      <CardContent className="px-4 sm:px-6">
        <div className="space-y-4">
          {users?.map((user) => {
            const currentRole = user.user_roles?.[0]?.role || 'user';
            
            return (
              <div key={user.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border border-green-200 rounded-lg space-y-3 sm:space-y-0">
                <div className="flex items-center space-x-3">
                  <User className="w-8 h-8 text-green-600 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <div className="font-medium text-green-800 truncate">
                      {user.full_name || 'No name'}
                    </div>
                    <div className="text-sm text-gray-600 truncate">{user.email}</div>
                    {user.phone && (
                      <div className="text-sm text-gray-500 truncate">{user.phone}</div>
                    )}
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                  <Badge className={`${getRoleBadgeColor(currentRole)} text-xs`}>
                    {currentRole}
                  </Badge>
                  
                  <Select
                    value={currentRole}
                    onValueChange={(value: 'admin' | 'organizer' | 'user') => 
                      handleRoleChange(user.id, value)
                    }
                  >
                    <SelectTrigger className="w-full sm:w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">User</SelectItem>
                      <SelectItem value="organizer">Organizer</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            );
          })}
          
          {!users || users.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No users found
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
