import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Users, User, Trash2 } from 'lucide-react';
import { useAllUsers, useUpdateUserRole, useDeleteUser, UserFromHook } from '@/hooks/useUserRoles';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export const UserManagement: React.FC = () => {
  const { data: users, isLoading } = useAllUsers();
  const updateUserRole = useUpdateUserRole();
  const deleteUser = useDeleteUser();
  const [userToDelete, setUserToDelete] = useState<UserFromHook | null>(null);

  const handleRoleChange = (userId: string, newRole: 'super_admin' | 'organizer' | 'user') => {
    updateUserRole.mutate({ userId, role: newRole });
  };

  const handleDeleteClick = (user: UserFromHook) => {
    setUserToDelete(user);
  };

  const handleConfirmDelete = () => {
    if (userToDelete) {
      deleteUser.mutate(userToDelete.id, {
        onSuccess: () => {
          setUserToDelete(null);
        }
      });
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'Super Administrator';
      case 'organizer':
        return 'Event Organizer';
      case 'user':
        return 'Rider';
      default:
        return role;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'bg-red-100 text-red-800';
      case 'organizer':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading users...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-green-800">
            <Users className="w-5 h-5 mr-2" />
            User Management
          </CardTitle>
        </CardHeader>
        <CardContent className="px-4 sm:px-6">
          <div className="space-y-4">
            {users?.map((user) => {
              const currentRole = user.user_roles?.[0]?.role || 'user';
              
              return (
                <div key={user.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border border-green-200 rounded-lg space-y-3 sm:space-y-0">
                  <div className="flex items-center space-x-3">
                    <User className="w-8 h-8 text-green-600 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <div className="font-medium text-green-800 truncate">
                        {user.full_name || 'No name'}
                      </div>
                      <div className="text-sm text-gray-600 truncate">{user.email}</div>
                      {user.phone && (
                        <div className="text-sm text-gray-500 truncate">{user.phone}</div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                    <Badge className={`${getRoleBadgeColor(currentRole)} text-xs`}>
                      {getRoleDisplayName(currentRole)}
                    </Badge>
                    
                    <Select
                      value={currentRole}
                      onValueChange={(value: 'super_admin' | 'organizer' | 'user') => 
                        handleRoleChange(user.id, value)
                      }
                    >
                      <SelectTrigger className="w-full sm:w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">User</SelectItem>
                        <SelectItem value="organizer">Organizer</SelectItem>
                        <SelectItem value="super_admin">Super Administrator</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-red-600 hover:bg-red-50"
                      onClick={() => handleDeleteClick(user)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
            
            {!users || users.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No users found
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {userToDelete && (
        <AlertDialog open={!!userToDelete} onOpenChange={() => setUserToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the user <span className="font-bold">{userToDelete.full_name || userToDelete.email}</span> and all of their associated data from the system.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setUserToDelete(null)}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirmDelete}
                className="bg-red-600 hover:bg-red-700"
                disabled={deleteUser.isPending}
              >
                {deleteUser.isPending ? 'Deleting...' : 'Yes, delete user'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  );
};
