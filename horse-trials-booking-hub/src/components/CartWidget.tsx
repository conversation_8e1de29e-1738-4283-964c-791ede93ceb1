import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ShoppingCart, Clock, X, AlertTriangle, Plus } from 'lucide-react';
import { useSlotReservations, getUserSessionId, useReleaseReservation } from '@/hooks/useSlotReservations';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { useIsMobile } from '@/hooks/use-mobile';

interface ReservedSlot {
  id: string;
  time_slot_id: string;
  activity_type?: string;
  time?: string;
  location_name?: string;
  level?: string;
  expires_at: string;
  timeRemaining?: number;
}

interface CartWidgetProps {
  onProceedToBooking: (reservedSlots: ReservedSlot[]) => void;
  isOpen?: boolean;
  onClose?: () => void;
  eventId?: string;
}

export const CartWidget: React.FC<CartWidgetProps> = ({ 
  onProceedToBooking, 
  isOpen = false, 
  onClose,
  eventId
}) => {
  const userSessionId = getUserSessionId();
  const { data: reservations = [], isLoading } = useSlotReservations(userSessionId, eventId);
  const [timeRemaining, setTimeRemaining] = useState<Record<string, number>>({});
  const releaseReservation = useReleaseReservation();
  const isMobile = useIsMobile();

  // Calculate time remaining for each reservation
  useEffect(() => {
    const calculateTimeRemaining = () => {
      const now = Date.now();
      const newTimeRemaining: Record<string, number> = {};
      
      reservations.forEach(reservation => {
        const expiresAt = new Date(reservation.expires_at).getTime();
        newTimeRemaining[reservation.id] = Math.max(0, expiresAt - now);
      });
      
      setTimeRemaining(newTimeRemaining);
    };

    calculateTimeRemaining();
    const interval = setInterval(calculateTimeRemaining, 1000);
    
    return () => clearInterval(interval);
  }, [reservations]);

  const formatTimeRemaining = (milliseconds: number) => {
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleRemoveFromCart = async (reservationId: string) => {
    try {
      await releaseReservation.mutateAsync({ reservationId });
    } catch (error) {
      console.error('Error removing reservation:', error);
    }
  };

  const handleProceedToBooking = () => {
    // Fetch additional details for each reservation if needed
    const reservationsWithDetails = reservations.map(reservation => {
      const enhancedReservation = {
        ...reservation,
        timeRemaining: timeRemaining[reservation.id] || 0,
        // Ensure these fields are present
        activity_type: reservation.activity_type || 'booking',
        time: reservation.time || 'Unknown time',
        location_name: reservation.location_name || 'Location'
      };
      console.log('Enhanced reservation:', enhancedReservation);
      return enhancedReservation;
    });
    
    console.log('Proceeding to booking with enhanced data:', reservationsWithDetails);
    onProceedToBooking(reservationsWithDetails);
    if (onClose) onClose();
  };

  const handleClose = () => {
    if (onClose) onClose();
  };

  // Don't auto-close for empty cart - let user see the empty state
  const CartContent = () => (
    <div className="space-y-4">
      {reservations.length === 0 ? (
        // Empty cart state
        <Card className="border-gray-200 bg-gray-50">
          <CardContent className="pt-6 text-center">
            <div className="flex flex-col items-center space-y-3">
              <ShoppingCart className="w-12 h-12 text-gray-400" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">
                  Your cart is empty
                </p>
                <p className="text-xs text-gray-500">
                  Select time slots from the schedule to add them to your cart
                </p>
              </div>
              <Button 
                variant="outline" 
                onClick={handleClose}
                data-testid="browse-time-slots"
                className="mt-4"
              >
                <Plus className="w-4 h-4 mr-2" />
                Browse Time Slots
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Warning Message */}
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="pt-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-orange-800">
                    Your spots are not reserved until checkout is complete
                  </p>
                  <p className="text-xs text-orange-600">
                    Time slots are temporarily held. Complete your booking to secure your spots.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Reserved Slots */}
          <div className="space-y-3">
            {reservations.map((reservation) => (
              <Card key={reservation.id} className="border-green-200">
                <CardContent className="pt-4">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="font-medium">
                        {reservation.location_name || 'Time Slot Reserved'}
                      </div>
                      <div className="text-sm text-gray-600">
                        {reservation.activity_type ? 
                          `${reservation.activity_type.replace('_', ' ')}` : 
                          'Booking'
                        }
                        {reservation.level && ` - ${reservation.level}`}
                      </div>
                      <div className="text-sm text-gray-600">
                        {reservation.time || `Slot ID: ${reservation.time_slot_id}`}
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <Clock className="w-4 h-4 text-orange-500" />
                        <span className="text-orange-600 font-medium" data-testid={`expires-in-${reservation.id}`}>
                          Expires in: {formatTimeRemaining(timeRemaining[reservation.id] || 0)}
                        </span>
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveFromCart(reservation.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      data-testid={`remove-from-cart-${reservation.id}`}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <Button 
              onClick={handleProceedToBooking}
              className="flex-1 bg-green-600 hover:bg-green-700"
              disabled={reservations.length === 0}
              data-testid="proceed-to-booking"
            >
              Proceed to Booking ({reservations.length} slot{reservations.length !== 1 ? 's' : ''})
            </Button>
            <Button 
              variant="outline" 
              onClick={handleClose}
              className="mt-4"
            >
              <Plus className="w-4 h-4 mr-2" />
              Browse Time Slots
            </Button>
          </div>
        </>
      )}
    </div>
  );

  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={handleClose}>
        <SheetContent side="bottom" className="h-[80vh]">
          <SheetHeader>
            <SheetTitle className="flex items-center space-x-2">
              <ShoppingCart className="w-5 h-5" />
              <span>Your Cart</span>
              {reservations.length > 0 && (
                <Badge variant="secondary" className="bg-primary text-cream">
                  {reservations.length}
                </Badge>
              )}
            </SheetTitle>
          </SheetHeader>
          <div className="mt-6 overflow-y-auto h-full pb-20">
            <CartContent />
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <ShoppingCart className="w-5 h-5" />
            <span>Your Cart</span>
            {reservations.length > 0 && (
              <Badge variant="secondary" className="bg-primary text-cream">
                {reservations.length}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>
        <CartContent />
      </DialogContent>
    </Dialog>
  );
};
