import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CalendarDays, Users, Settings, ArrowRight } from 'lucide-react';

interface AdminDashboardProps {
  onEventManage: () => void;
  onCreateEvent: () => void;
  onSettingsManage: () => void;
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({ 
  onEventManage, 
  onCreateEvent,
  onSettingsManage
}) => {
  return (
    <div className="space-y-6 px-4 sm:px-0">
      <div className="text-center sm:text-left">
        <h2 className="text-2xl sm:text-3xl font-bold text-green-800">Admin Dashboard</h2>
        <p className="text-sm sm:text-base text-gray-600 mt-2">Manage events, locations, users, and bookings</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
        <Card className="border-green-200 hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="bg-green-50 w-12 h-12 rounded-full flex items-center justify-center mb-3">
              <CalendarDays className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-lg text-green-800" data-testid="card-title-events">Events</CardTitle>
            <CardDescription className="text-sm">Manage competition and training events</CardDescription>
          </CardHeader>
          <CardContent className="pb-3">
            <p className="text-sm text-gray-600">Create, edit, and configure events, locations, levels, and dressage tests.</p>
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row gap-2 sm:justify-between pt-3">
            <Button 
              variant="outline" 
              className="border-green-500 text-green-700 hover:bg-green-50 w-full sm:w-auto order-2 sm:order-1"
              onClick={onCreateEvent}
            >
              Create New
            </Button>
            <Button 
              className="bg-green-600 hover:bg-green-700 w-full sm:w-auto order-1 sm:order-2"
              onClick={onEventManage}
            >
              <span className="sm:hidden">Manage</span>
              <span className="hidden sm:inline">Manage Events</span>
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        <Card className="border-blue-200">
          <CardHeader className="pb-3">
            <div className="bg-blue-50 w-12 h-12 rounded-full flex items-center justify-center mb-3">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <CardTitle className="text-lg text-blue-800" data-testid="card-title-users">Users</CardTitle>
            <CardDescription className="text-sm">Manage user accounts and permissions</CardDescription>
          </CardHeader>
          <CardContent className="pb-3">
            <p className="text-sm text-gray-600">Add, edit, and remove user accounts. Configure user roles and permissions.</p>
          </CardContent>
          <CardFooter className="flex justify-end pt-3">
            <Button 
              className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
              onClick={() => {}}
              disabled
            >
              <span className="sm:hidden">Coming Soon</span>
              <span className="hidden sm:inline">Coming Soon</span>
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        <Card className="border-purple-200 lg:col-span-2 xl:col-span-1">
          <CardHeader className="pb-3">
            <div className="bg-purple-50 w-12 h-12 rounded-full flex items-center justify-center mb-3">
              <Settings className="h-6 w-6 text-purple-600" />
            </div>
            <CardTitle className="text-lg text-purple-800" data-testid="card-title-settings">Settings</CardTitle>
            <CardDescription className="text-sm">Configure system settings</CardDescription>
          </CardHeader>
          <CardContent className="pb-3">
            <p className="text-sm text-gray-600">Manage global settings, pricing, and system preferences.</p>
          </CardContent>
          <CardFooter className="flex justify-end pt-3">
            <Button 
              className="bg-purple-600 hover:bg-purple-700 w-full sm:w-auto"
              onClick={onSettingsManage}
            >
              <span className="sm:hidden">Settings</span>
              <span className="hidden sm:inline">Manage Settings</span>
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};
