import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'; // Keep Tabs import
import { Calendar, Clock, Target, Filter, AlertCircle, Loader2, RefreshCw, CalendarDays } from 'lucide-react';
import { LocationSchedule } from './LocationSchedule'; // Changed from ArenaSchedule
import { BookingFlow } from './BookingFlow';
import { useLocations } from '@/hooks/useLocations'; // Changed from useArenas
import { useEventLevels } from '@/hooks/useLevels';
import { useActivities } from '@/hooks/useActivities';
import { supabase } from '@/integrations/supabase/client';
import { formatDateForDisplay, createDateWithoutTimezone } from '@/utils/dateUtils';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { toast } from 'sonner';
import { getUserSessionId, useSlotReservations } from '@/hooks/useSlotReservations';
import { Checkbox } from "@/components/ui/checkbox"; // Import Checkbox
import { Label } from "@/components/ui/label";     // Import Label
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Event } from '@/hooks/useEvents';

interface EventDetailsProps {
  event: Event;
  onBooking: (slots: Array<{ id: string; time_slot_id: string; activity_id: string; location_id: string; activity_type: string; location_name: string; time: string; expires_at: string; event_dressage_test_id?: string; participant_name?: string; horse_name?: string; fixed_level?: string }>) => void;
  initialSelectedDate?: string;
}

export const EventDetails: React.FC<EventDetailsProps> = ({ event, onBooking, initialSelectedDate }) => {
  // State declarations - keep these at the top level
  const [selectedDate, setSelectedDate] = useState(initialSelectedDate || '');
  const userSessionId = getUserSessionId();
  const { data: cart = [] } = useSlotReservations(userSessionId, event.id);
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [error, setError] = useState<Error | null>(null);
  const [locationsWithActivities, setLocationsWithActivities] = useState([]);
  const [showBookingFlow, setShowBookingFlow] = useState(false);
  const [selectedBookingSlots, setSelectedBookingSlots] = useState([]);
  const [showOnlyAvailableGlobal, setShowOnlyAvailableGlobal] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();
  // Add a refresh function
  const refreshData = useCallback(() => {
    // Invalidate all relevant queries
    queryClient.invalidateQueries({ queryKey: ['bookings'] });
    queryClient.invalidateQueries({ queryKey: ['time_slots'] });
    queryClient.invalidateQueries({ queryKey: ['locations'] });

    // Show toast notification
    toast({
      title: "Refreshed",
      description: "Schedule data has been refreshed",
    });
  }, [queryClient, toast]);

  // Use a different name for our internal function
  const handleBookingStart = useCallback((selectedSlots) => {
    setShowBookingFlow(true);
    setSelectedBookingSlots(selectedSlots);
  }, []);

  const handleBackFromBooking = useCallback(() => {
    setShowBookingFlow(false);
    setSelectedBookingSlots([]);

    // Refresh data when returning from booking
    refreshData();
  }, [refreshData]);

  // Filter states
  const [dressageLocationFilter, setDressageLocationFilter] = useState('all'); // Changed from dressageArenaFilter
  const [dressageLevelFilter, setDressageLevelFilter] = useState('all');
  const [dressageTimeFilter, setDressageTimeFilter] = useState('all'); // Keep this
  const [showJumpingLocationFilter, setShowJumpingLocationFilter] = useState('all');
  const [showJumpingLevelFilter, setShowJumpingLevelFilter] = useState('all');
  const [showJumpingTimeFilter, setShowJumpingTimeFilter] = useState('all'); // Keep this

  // Data fetching hooks
  const { data: locations, isLoading: locationsLoading, error: locationsError } = useLocations(event.id);
  const { data: eventLevels } = useEventLevels(event.id);

  // Define helper functions using useCallback to ensure consistent references
  const formatDateSafely = useCallback((dateString) => {
    if (!dateString) return '';

    // Check if the date string is already in ISO format
    const isISOFormat = dateString.includes('T');

    // If it's just a date (YYYY-MM-DD), append T12:00:00 to avoid timezone issues
    // Using noon (12:00) ensures the date doesn't shift due to timezone conversion
    const dateWithTime = isISOFormat ? dateString : `${dateString}T12:00:00`;
    // Create a date object
    const date = new Date(dateWithTime);
    // Format the date
    const formattedDate = date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      timeZone: 'UTC' // Use UTC to avoid timezone issues
    });
    return formattedDate;
  }, []);

  // Improved date comparison function with better timezone handling
  const getActivitiesForDate = useCallback((date) => {
    if (!locationsWithActivities || !date) return [];

    // Filter locations that have activities on the selected date
    const locationsForDate = locationsWithActivities.filter(location => {
      // If we have activities data for this location, check if any activity is on the selected date
      const locationActivities = location.activities || []; // Changed from arenaActivities

      const hasActivitiesOnDate = locationActivities.some(activity => { // Changed from arenaActivities
        // Extract just the date part from the activity start time
        const activityDate = new Date(activity.start_time);
        const activityDateString = `${activityDate.getUTCFullYear()}-${String(activityDate.getUTCMonth() + 1).padStart(2, '0')}-${String(activityDate.getUTCDate()).padStart(2, '0')}`;

        return activityDateString === date;
      });

      return hasActivitiesOnDate;
    });
    return locationsForDate;
  }, [locationsWithActivities]);

  const isTimeSlotAfterTime = useCallback((timeSlot, filterTime) => {
    if (filterTime === 'all') return true;
    const slotTime = new Date(`2000-01-01T${timeSlot.start_time.split('T')[1]}`);
    const filterTimeDate = new Date(`2000-01-01T${filterTime}:00`);

    return slotTime >= filterTimeDate;
  }, []);

  // Generate array of dates between start and end dates - with improved timezone handling
  const eventDates = useMemo(() => {
    if (!event.start_date || !event.end_date) {
      return [];
    }
    const dates = [];
    // Use our utility function to create dates without timezone issues
    const currentDate = createDateWithoutTimezone(event.start_date);
    const endDate = createDateWithoutTimezone(event.end_date);

    // Check if dates are valid
    if (isNaN(currentDate.getTime()) || isNaN(endDate.getTime())) {
      return [];
    }
    // Loop through dates and add them to the array
    while (currentDate <= endDate) {
      dates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return dates;
  }, [event.start_date, event.end_date]);

  // Fetch activities for locations
  useEffect(() => {
    const fetchLocationActivities = async () => { // Renamed function
      if (!locations || locations.length === 0) {
        setLocationsWithActivities([]); // Ensure state is cleared if no locations
        setIsLoading(false); // Set loading to false if no locations
        return;
      }

      setIsLoading(true);
      setError(null);
      try {
        // Create a copy of locations to add activities to
        const locationsWithActivitiesData = await Promise.all(locations.map(async (location) => {
          // Fetch activities for this location
          const { data: activities, error } = await supabase
            .from('activities')
            .select('*')
            .eq('location_id', location.id) // Changed from arena_id
            .order('start_time');

          if (error) {
            throw error; // Propagate error to catch block
          }
          return { ...location, activities: activities || [] };
        }));
        // Update the locations state with activities
        setLocationsWithActivities(locationsWithActivitiesData);
      } catch (error) {
        setError(error instanceof Error ? error : new Error('Unknown error fetching activities'));
      } finally {
        setIsLoading(false);
      }
    };
    fetchLocationActivities(); // Call renamed function
  }, [locations]); // Depend on locations

  // Automatically select the first event date if available and none is selected
  useEffect(() => {
    if (eventDates && eventDates.length > 0 && !selectedDate && !initialSelectedDate) {
      setSelectedDate(eventDates[0]);
    }
  }, [eventDates, selectedDate, initialSelectedDate]);

  // Memoized derived state
  const selectedLocations = useMemo(() => {
    if (!selectedDate) return [];
    const result = getActivitiesForDate(selectedDate);
    return result;
  }, [selectedDate, getActivitiesForDate]);

  const dressageLocations = useMemo(() => {
    const filtered = selectedLocations.filter(location => {
      return location.activity_type?.trim().toLowerCase() === 'dressage';// Match lowercase
    });
    return filtered;
  }, [selectedLocations]);

  const showJumpingLocations = useMemo(() => { // Changed from showJumpingArenas
    const filtered = selectedLocations.filter(location => {
      return location.activity_type?.trim().toLowerCase() === 'show_jumping';
    });
    return filtered;
  }, [selectedLocations]);

  const initialDiscipline = useMemo(() => {
    if (dressageLocations.length > 0) return "dressage";
    if (showJumpingLocations.length > 0) return "show_jumping";
    return "dressage";
  }, [dressageLocations, showJumpingLocations]);

  //const [activeActivityTab, setActiveActivityTab] = useState(initialDiscipline);
  const [activeActivityTab, setActiveActivityTab] = useState('dressage');

  const filteredDressageLocations = useMemo(() => { // Changed from filteredDressageArenas
    if (dressageLocationFilter !== 'all') {
      return dressageLocations.filter(location => location.id === dressageLocationFilter);
    }
    return dressageLocations;
  }, [dressageLocations, dressageLocationFilter]);

  const filteredShowJumpingLocations = useMemo(() => {
    if (showJumpingLocationFilter !== 'all') {
      return showJumpingLocations.filter(location => location.id === showJumpingLocationFilter);
    }
    return showJumpingLocations;
  }, [showJumpingLocations, showJumpingLocationFilter]);

  // Add these memoized values with proper null/undefined handling
  const dressageLevels = useMemo(() => {
    if (!eventLevels || eventLevels.length === 0) return [];

    // Extract unique level names from event levels where discipline is dressage
    // Handle cases where levels or discipline might be undefined
    return [...new Set(
      eventLevels
        .filter(level => level.levels?.discipline === 'dressage')
        .map(level => level.levels?.name)
        .filter(Boolean) // Remove null/undefined values
    )];
  }, [eventLevels]);

  const showJumpingLevels = useMemo(() => {
    if (!eventLevels || eventLevels.length === 0) return [];

    // First, get levels from event_levels
    const eventLevelNames = [...new Set(
      eventLevels
        .filter(level => level.levels?.discipline === 'show_jumping')
        .map(level => level.levels?.name)
        .filter(Boolean)
    )];

    // Then, if we have arenas with activities, extract levels from activities
    const activityLevels = new Set<string>(); // Specify type for Set
    if (showJumpingLocations && showJumpingLocations.length > 0) {
      showJumpingLocations.forEach(location => {
        // Check for levels on activities
        if (location.activities) { // Changed from arena.activities
          location.activities.forEach(activity => {
            if (activity.level) {
              activityLevels.add(activity.level);
            }
          });
        }
      });
    }

    // Combine both sets of levels
    return [...new Set([...eventLevelNames, ...activityLevels])];
  }, [eventLevels, showJumpingLocations]);

  useEffect(() => {
    if (dressageLocations.length > 0) {
      setActiveActivityTab("dressage");
    } else if (showJumpingLocations.length > 0) {
      setActiveActivityTab("show_jumping");
    }
  }, [dressageLocations, showJumpingLocations]);

  // Define time options for the time filters
  const timeOptions = useMemo(() => [
    { value: 'all', label: 'Any Time' },
    { value: '08:00', label: '8:00 AM or later' },
    { value: '09:00', label: '9:00 AM or later' },
    { value: '10:00', label: '10:00 AM or later' },
    { value: '11:00', label: '11:00 AM or later' },
    { value: '12:00', label: '12:00 PM or later' },
    { value: '13:00', label: '1:00 PM or later' },
    { value: '14:00', label: '2:00 PM or later' },
    { value: '15:00', label: '3:00 PM or later' },
    { value: '16:00', label: '4:00 PM or later' },
  ], []);

  // Handle errors
  if (error || locationsError) { // Changed from arenasError
    return (
      <Card className="bg-red-50 border-red-200">
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertCircle className="w-12 h-12 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Error Loading Event Details</h3>
            <p className="mb-4">{(error || locationsError)?.message || 'An unknown error occurred'}</p>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="border-red-300 text-red-600 hover:bg-red-100"
            >
              Reload Page
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show loading state
  if (isLoading || locationsLoading) {
    return (
      <Card className="bg-white border-cream-light">
        <CardContent className="p-6">
          <div className="text-center text-primary">
            <Loader2 className="w-12 h-12 mx-auto mb-4 animate-spin" />
            <h3 className="text-lg font-medium">Loading Event Details</h3>
            <p className="text-text-secondary mt-2">Please wait while we fetch the event information...</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  return (
    <div className="space-y-6">
      {showBookingFlow ? (
        <BookingFlow
          selectedSlots={selectedBookingSlots}
          event={event}
          onBack={handleBackFromBooking}
          selectedDate={selectedDate}
        />
      ) : (
        <div>
          <Card className="bg-white border-cream-light">
            <CardHeader>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold text-primary">{event.name}</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshData}
                  className="text-primary border-primary hover:bg-cream"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh Schedule
                </Button>
              </div>
              <div className="flex items-center space-x-4 text-sm text-text-secondary">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1 text-amber-500" />
                  Competition: {formatDateForDisplay(event.start_date)} - {formatDateForDisplay(event.end_date)}
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Date Selector */}
          {eventDates.length > 0 && (
            <Card className="bg-white border-cream-light">
              <CardHeader>
                <CardTitle className="text-lg text-primary flex items-center">
                  <CalendarDays className="w-5 h-5 mr-2" />
                  Select Date
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-4">
                  <div className="text-sm text-text-secondary">
                    {selectedDate && (
                      <span>
                        Showing schedule for <strong>{formatDateSafely(selectedDate)}</strong>
                      </span>
                    )}
                  </div>
                </div>
                
                {/* Date selection buttons */}
                <div className="flex flex-wrap gap-2">
                  {eventDates.map((date) => (
                    <Button
                      key={date}
                      variant={selectedDate === date ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedDate(date)}
                      className={`text-xs ${
                        selectedDate === date 
                          ? "bg-primary text-cream hover:bg-primary-light" 
                          : "bg-white text-primary border-primary hover:bg-cream-light"
                      }`}
                    >
                      {formatDateSafely(date)}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {selectedDate && selectedLocations.length > 0 && (
            <div className="grid lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card className="bg-white border-cream-light">
                  <CardHeader>
                    <CardTitle className="text-lg text-primary">
                      Locations for {formatDateSafely(selectedDate)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Tabs value={activeActivityTab} onValueChange={(val) => {
                      console.log("[Tab Change] New tab selected:", val);
                      setActiveActivityTab(val);
                    }}>                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="dressage" disabled={dressageLocations.length === 0}>
                          Dressage ({filteredDressageLocations.length})
                        </TabsTrigger>
                        <TabsTrigger value="show_jumping" disabled={showJumpingLocations.length === 0}>
                          Show Jumping ({filteredShowJumpingLocations.length})
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="dressage" className="space-y-4">
                        {/* Dressage Filters */}
                        <Card className="bg-gray-50">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-2 mb-3">
                              <Filter className="w-4 h-4 text-gray-600" />
                              <span className="text-sm font-medium text-gray-700">Filters</span>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              <div>
                                <label className="block text-xs font-medium text-gray-600 mb-1">Location</label>
                                <Select value={dressageLocationFilter} onValueChange={setDressageLocationFilter}>
                                  <SelectTrigger className="h-8 text-xs">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All Locations</SelectItem>
                                    {dressageLocations.map(location => (
                                      <SelectItem key={location.id} value={location.id}>{location.name}</SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <label className="block text-xs font-medium text-gray-600 mb-1">Level</label>
                                <Select value={dressageLevelFilter} onValueChange={setDressageLevelFilter}>
                                  <SelectTrigger className="h-8 text-xs">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All Levels</SelectItem>
                                    {dressageLevels && dressageLevels.length > 0 ? (
                                      dressageLevels.map(level => (
                                        <SelectItem key={level} value={level}>{level}</SelectItem>
                                      ))
                                    ) : (
                                      <SelectItem value="none" disabled>No levels available</SelectItem>
                                    )}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <label className="block text-xs font-medium text-gray-600 mb-1">Time</label>
                                <Select value={dressageTimeFilter} onValueChange={setDressageTimeFilter}>
                                  <SelectTrigger className="h-8 text-xs">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {timeOptions.map(option => (
                                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="flex items-center pt-5"> {/* Adjusted for alignment */}
                                <Checkbox
                                  id="dressage-show-only-available"
                                  checked={showOnlyAvailableGlobal}
                                  onCheckedChange={(checked) => setShowOnlyAvailableGlobal(checked as boolean)}
                                  className="mr-2"
                                />
                                <Label htmlFor="dressage-show-only-available" className="text-xs text-gray-600">Show only available</Label>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        {filteredDressageLocations.map((location) => (
                          <LocationActivityDisplay // Renamed component
                            key={location.id}
                            location={location}
                            date={selectedDate}
                            cart={cart}
                            eventId={event.id}
                            levelFilter={dressageLevelFilter}
                            timeFilter={dressageTimeFilter}
                            isTimeSlotAfterTime={isTimeSlotAfterTime}
                            showOnlyAvailable={showOnlyAvailableGlobal}
                          />
                        ))}

                        {filteredDressageLocations.length === 0 && (
                          <div className="text-center py-8 text-gray-500">
                            <p>No dressage arenas available for this date with the selected filters.</p>
                          </div>
                        )}
                      </TabsContent>

                      <TabsContent value="show_jumping" className="space-y-4">
                        {/* Show Jumping Filters */}
                        <Card className="bg-gray-50">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-2 mb-3">
                              <Filter className="w-4 h-4 text-gray-600" />
                              <span className="text-sm font-medium text-gray-700">Filters</span>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              <div>
                                <label className="block text-xs font-medium text-gray-600 mb-1">Location</label>
                                <Select value={showJumpingLocationFilter} onValueChange={setShowJumpingLocationFilter}>
                                  <SelectTrigger className="h-8 text-xs">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All Locations</SelectItem>
                                    {showJumpingLocations.map(location => (
                                      <SelectItem key={location.id} value={location.id}>{location.name}</SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <label className="block text-xs font-medium text-gray-600 mb-1">Level</label>
                                <Select value={showJumpingLevelFilter} onValueChange={setShowJumpingLevelFilter}>
                                  <SelectTrigger className="h-8 text-xs">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All Levels</SelectItem>
                                    {showJumpingLevels && showJumpingLevels.length > 0 ? (
                                      showJumpingLevels.map((level, index) => (
                                        <SelectItem key={`${level}-${index}`} value={String(level)}>
                                          {String(level)}
                                        </SelectItem>
                                      ))
                                    ) : (
                                      <SelectItem value="none" disabled>No levels available</SelectItem>
                                    )}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <label className="block text-xs font-medium text-gray-600 mb-1">Time</label>
                                <Select value={showJumpingTimeFilter} onValueChange={setShowJumpingTimeFilter}>
                                  <SelectTrigger className="h-8 text-xs">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {timeOptions.map(option => (
                                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="flex items-center pt-5"> {/* Adjusted for alignment */}
                                <Checkbox
                                  id="sj-show-only-available"
                                  checked={showOnlyAvailableGlobal}
                                  onCheckedChange={(checked) => setShowOnlyAvailableGlobal(checked as boolean)}
                                  className="mr-2"
                                />
                                <Label htmlFor="sj-show-only-available" className="text-xs text-gray-600">Show only available</Label>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        {filteredShowJumpingLocations.map((location) => (
                          <LocationActivityDisplay // Renamed component
                            key={location.id}
                            location={location}
                            date={selectedDate}
                            cart={cart}
                            eventId={event.id}
                            levelFilter={showJumpingLevelFilter}
                            timeFilter={showJumpingTimeFilter}
                            isTimeSlotAfterTime={isTimeSlotAfterTime}
                            showOnlyAvailable={showOnlyAvailableGlobal}
                          />
                        ))}

                        {filteredShowJumpingLocations.length === 0 && (
                          <div className="text-center py-8 text-gray-500">
                            <p>No show jumping arenas available for this date with the selected filters.</p>
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </div>

              {cart.length > 0 && (
                <div className="lg:col-span-1">
                  <Card className="sticky top-4 bg-white border-cream-light">
                    <CardHeader>
                      <CardTitle className="text-lg text-primary">Booking Cart</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {cart.map((slot) => (
                        <div key={slot.id} className="flex justify-between items-center p-3 bg-cream rounded">
                          <div className="text-sm">
                            <div className="font-medium">{slot.location_name}</div>
                            <div className="text-gray-600">{slot.time}</div>
                            <div className="text-gray-600">{slot.activity_type}</div>
                            {slot.level && <div className="text-gray-600">Level: {slot.level}</div>}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={async () => {
                              // Use the view instead of direct table access to avoid RLS issues
                              await supabase
                                .from('slot_reservations_with_event')
                                .delete()
                                .eq('id', slot.id); // or whatever uniquely identifies the reservation
                              queryClient.invalidateQueries({ queryKey: ['slot_reservations', userSessionId] });
                            }} className="text-red-600 hover:bg-red-50"
                          >
                            Remove
                          </Button>
                        </div>
                      ))}

                      <div className="pt-4 border-t">
                        <div className="flex justify-between items-center mb-3">
                          <span className="font-medium">Total Slots:</span>
                          <span>{cart.length}</span>
                        </div>
                        <Button
                          onClick={() => handleBookingStart(cart)}
                          className="w-full bg-primary hover:bg-primary-light text-cream"
                        >
                          Proceed to Booking
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          )}

          {selectedDate && selectedLocations.length === 0 && (
            <Card className="bg-gray-50 border-gray-200">
              <CardContent className="text-center py-8">
                <p className="text-gray-600">No locations available for the selected date.</p>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};
// Renamed component to display location activities
const LocationActivityDisplay = ({
  location,
  date,
  cart,
  eventId,
  levelFilter = 'all',
  timeFilter = 'all',
  isTimeSlotAfterTime,
  showOnlyAvailable // New prop
}) => { // Renamed component
  // Use the useActivities hook to fetch activities for this location
  const { data: activities, isLoading, error } = useActivities(location.id);

  // Filter activities based on level and date
  const filteredActivities = useMemo(() => {
    if (!activities) return [];

    return activities.filter(activity => {
      // First, check if this activity is for the selected date
      const activityDate = activity.start_time.split('T')[0];
      if (activityDate !== date) {
        return false;
      }

      // Then apply level filter
      if (levelFilter !== 'all') {
        // Check both activity level and arena fixed level
        const activityLevel = activity.level;
        if (!activityLevel || activityLevel !== levelFilter) {
          return false;
        }
      }
      return true;
    });
  }, [activities, levelFilter, date]);

  if (isLoading) {
    return (
      <div className="border border-cream-light rounded-lg p-4">
        <div className="flex justify-center items-center h-32">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2 text-primary">Loading activities...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="border border-red-200 rounded-lg p-4 bg-red-50">
        <div className="flex justify-center items-center h-32">
          <AlertCircle className="h-6 w-6 text-red-600" />
          <span className="ml-2 text-red-600">Error loading activities</span>
        </div>
      </div>
    );
  }

  return (
    <div className="border border-cream-light rounded-lg p-4">
      <div className="flex justify-between items-start mb-3">
        <div>
          <h4 className="font-semibold text-primary">{location.name}</h4>
          <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
            <div className="flex items-center">
              <Target className="w-4 h-4 mr-1" />
              {location.activity_type || 'General'}
            </div>
          </div>
        </div>
      </div>
      {filteredActivities.length > 0 ? (
        <div className="space-y-4">
          {filteredActivities.map((activity) => (
            <div key={activity.id} className="bg-gray-50 rounded p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Badge variant={activity.activity_type === 'dressage' ? 'default' : 'secondary'} className="bg-primary text-cream">
                    {activity.activity_type.replace('_', ' ')}
                  </Badge>
                  {(activity.level) && (
                    <Badge variant="outline" className="bg-cream text-primary border-primary">{activity.level}</Badge>
                  )}
                </div>
                <div className="text-sm text-gray-600">
                  <Clock className="w-4 h-4 inline mr-1" />
                  {activity.start_time.split('T')[1].substring(0, 5)} - {activity.end_time.split('T')[1].substring(0, 5)}
                </div>
              </div>
              {activity.description && (
                <p className="text-sm text-gray-600 mb-2">{activity.description}</p>
              )}
              <LocationSchedule
                date={date}
                locations={[{ ...location, activities: [activity] }]}
                cart={cart} // Keep cart
                activityMode={true}
                eventId={eventId}
                timeFilter={timeFilter}
                isTimeSlotAfterTime={isTimeSlotAfterTime}
                showOnlyAvailable={showOnlyAvailable} // Pass down
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-4 text-gray-500">
          <p>No activities scheduled for this location {levelFilter !== 'all' ? 'with the selected level filter' : 'yet'}.</p>
        </div>
      )}
    </div>
  );
};
