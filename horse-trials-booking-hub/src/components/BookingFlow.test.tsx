// src/components/BookingFlow.test.tsx
// Import the centralized mocks. This line needs to be at the top.
import "../test/test-utils";
import { mockToast, mockMutateAsync } from "../test/test-utils";

// then load the rest of the functions
import {
  createTestQueryClient,
  getMockBookingFlowProps,
  mockSlotReservationsDeleteMatch,
  MOCK_STRIPE_CHECKOUT_RESPONSE,
  MOCK_PRICING_SETTINGS,
} from "../test/test-utils";

// import act
import { act } from "react-dom/test-utils";

import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { BookingFlow } from "./BookingFlow";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useToast } from "@/components/ui/use-toast";
import { userEvent } from "@testing-library/user-event";
import { useCreateBooking } from "@/hooks/useBookings";
import { supabase } from "@/integrations/supabase/client";

// Mock Supabase client
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: { full_name: "Test User" },
            error: null,
          }),
        }),
      }),
    }),
    functions: {
      invoke: vi.fn().mockResolvedValue(MOCK_STRIPE_CHECKOUT_RESPONSE),
    },
  },
}));

// Mock Supabase Auth Session
vi.mock("@supabase/auth-helpers-react", () => ({
  useSession: () => ({
    user: {
      id: "test-user-id",
      email: "<EMAIL>",
    },
  }),
}));

// Mock the useBookings module
vi.mock("@/hooks/useBookings", () => ({
  useCreateBooking: () => mockUseCreateBooking(),
  useBookings: () => ({
    data: [],
    isLoading: false,
  }),
}));

// Mock window.location.href assignment
const mockAssign = vi.fn();
Object.defineProperty(window, 'location', {
  value: { 
    href: '',
    assign: mockAssign,
    origin: 'http://localhost:3000',
    pathname: '/booking'
  },
  writable: true
});

// Mock fetch for Stripe checkout
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe("BookingFlow Component", () => {
  let queryClient: QueryClient;
  let mockProps: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockProps = getMockBookingFlowProps();
    queryClient = createTestQueryClient();

    // Reset fetch mock for each test
    mockFetch.mockReset();
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ url: 'https://stripe.com/checkout' })
    });
  });

  // Test for basic rendering and interaction
  it("renders the form and allows interaction", async () => {
    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...mockProps} />
      </QueryClientProvider>,
    );

    await waitFor(() => {
      expect(screen.getByText(/Complete Your Booking/i)).toBeInTheDocument();
    });

    // 1. Check if the component rendered
    expect(container.firstChild).not.toBeNull();

    // 2. Check for the correct title
    const titleElement = screen.getByText(/Complete Your Booking/i);
    expect(titleElement).toBeInTheDocument();

    // 3. Check for participant information section
    const participantInfoElement = screen.getByText(/Participant Information/i);
    expect(participantInfoElement).toBeInTheDocument();

    // 4. Get form inputs
    const inputs = container.querySelectorAll("input");
    expect(inputs.length).toBeGreaterThan(0);

    // 5. Fill out the form
    if (inputs.length >= 4) {
      const nameInput = inputs[0];
      const emailInput = inputs[1];
      const participantNameInput = inputs[2];

      await act(async () => {
        fireEvent.change(nameInput, { target: { value: "John Doe" } });
        fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
        fireEvent.change(participantNameInput, {
          target: { value: "Rider Name" },
        });
      });

      // 6. Verify input values
      expect(nameInput.value).toBe("John Doe");
      expect(emailInput.value).toBe("<EMAIL>");
    }

    // 7. Check for dressage test selection if applicable
    const selectElement = container.querySelector("select");
    if (selectElement) {
      fireEvent.change(selectElement, { target: { value: "test1" } });
      expect(selectElement.value).toBe("test1");
    }

    // 8. Check for price information
    const priceElements = screen.getAllByText(/\$/);
    expect(priceElements.length).toBeGreaterThan(0);

    // 9. Check for the complete booking button
    const completeButton = screen.getByText(/Complete Booking/i);
    expect(completeButton).toBeInTheDocument();
  });

  // Test for successful Stripe checkout flow
  it("handles Stripe checkout flow correctly", async () => {
    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...mockProps} />
      </QueryClientProvider>,
    );

    // Fill out the form
    const inputs = container.querySelectorAll("input");
    const nameInput = inputs[0];
    const emailInput = inputs[1];
    const participantNameInput = inputs[2];

    await act(async () => {
      fireEvent.change(nameInput, { target: { value: "John Doe" } });
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(participantNameInput, {
        target: { value: "Rider Name" },
      });
    });

    // Select dressage test if needed
    const selectTrigger = screen.queryByText("Select a test");
    if (selectTrigger) {
      fireEvent.click(selectTrigger);
      const option = screen.getByRole("option", { name: /Test 1/i });
      fireEvent.click(option);
    }

    // Submit the form
    const completeButton = screen.getByText(/Complete Booking/i);
    expect(completeButton).not.toBeDisabled();
    
    await act(async () => {
      fireEvent.click(completeButton);
    });

    // Verify fetch was called with correct data
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('create-stripe-checkout'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.any(Object),
          body: expect.any(String)
        })
      );
    });

    // Verify redirect attempt
    expect(window.location.href).toBe('https://stripe.com/checkout');
  });

  // Test for Stripe checkout error handling
  it("handles Stripe checkout errors correctly", async () => {
    // Mock fetch to return an error
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({ 
        error: 'Failed to create checkout session',
        details: 'Failed to create checkout session'
      })
    });

    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...mockProps} />
      </QueryClientProvider>,
    );

    // Fill out the form
    const inputs = container.querySelectorAll("input");
    const nameInput = inputs[0];
    const emailInput = inputs[1];
    const participantNameInput = inputs[2];

    await act(async () => {
      fireEvent.change(nameInput, { target: { value: "John Doe" } });
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(participantNameInput, {
        target: { value: "Rider Name" },
      });
    });

    // Select dressage test if needed
    const selectTrigger = screen.queryByText("Select a test");
    if (selectTrigger) {
      fireEvent.click(selectTrigger);
      const option = screen.getByRole("option", { name: /Test 1/i });
      fireEvent.click(option);
    }

    // Submit the form
    const completeButton = screen.getByText(/Complete Booking/i);
    expect(completeButton).not.toBeDisabled();
    
    await act(async () => {
      fireEvent.click(completeButton);
    });

    // Verify error toast was shown
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Payment Error",
          description: expect.stringContaining("Failed to create checkout session"),
          variant: "destructive",
        })
      );
    });
  });

  // Test for form validation
  it("properly validates form fields and disables submit button when required fields are empty", async () => {
    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...mockProps} />
      </QueryClientProvider>,
    );

    const completeButton = screen.getByText(/Complete Booking/i);
    expect(completeButton).toBeDisabled();

    // Fill out only some fields
    const inputs = container.querySelectorAll("input");
    const nameInput = inputs[0];

    await act(async () => {
      fireEvent.change(nameInput, { target: { value: "John Doe" } });
    });

    // Button should still be disabled
    expect(completeButton).toBeDisabled();
  });

  // Test for show jumping activity type
  it("handles show jumping activity type correctly", async () => {
    const showJumpingProps = {
      ...mockProps,
      selectedSlots: [{
        ...mockProps.selectedSlots[0],
        activity_type: "show_jumping",
      }],
    };

    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...showJumpingProps} />
      </QueryClientProvider>,
    );

    // Verify show jumping specific elements
    expect(screen.getByText(/Show Jumping/i)).toBeInTheDocument();
  });

  // Test for email validation
  it("validates email format correctly", async () => {
    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...mockProps} />
      </QueryClientProvider>,
    );

    const inputs = container.querySelectorAll("input");
    const emailInput = inputs[1];

    // Test invalid email
    await act(async () => {
      fireEvent.change(emailInput, { target: { value: "invalid-email" } });
    });

    const completeButton = screen.getByText(/Complete Booking/i);
    expect(completeButton).toBeDisabled();

    // Test valid email
    await act(async () => {
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    });

    // Button might still be disabled due to other required fields
    expect(completeButton).toBeDisabled();
  });

  // Test for price calculation
  it("calculates prices correctly for different activity types", async () => {
    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...mockProps} />
      </QueryClientProvider>,
    );

    // Wait for pricing settings to load
    await waitFor(() => {
      const priceText = `$${MOCK_PRICING_SETTINGS.dressage_price}`;
      const priceElements = screen.getAllByText(priceText);
      expect(priceElements.length).toBeGreaterThan(0);
    });
  });

  // Test for participant name validation
  it("validates participant names correctly", async () => {
    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...mockProps} />
      </QueryClientProvider>,
    );

    const inputs = container.querySelectorAll("input");
    const participantNameInput = inputs[2];

    await act(async () => {
      fireEvent.change(participantNameInput, { target: { value: "" } });
    });

    const completeButton = screen.getByText(/Complete Booking/i);
    expect(completeButton).toBeDisabled();

    await act(async () => {
      fireEvent.change(participantNameInput, { target: { value: "John Rider" } });
    });

    // Button might still be disabled due to other required fields
    expect(completeButton).toBeDisabled();
  });

  // Test for cart clearing after successful booking
  it("clears the cart (slot_reservations) after successful booking", async () => {
    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...mockProps} />
      </QueryClientProvider>,
    );

    // Fill out the form
    const inputs = container.querySelectorAll("input");
    const nameInput = inputs[0];
    const emailInput = inputs[1];
    const participantNameInput = inputs[2];

    await act(async () => {
      fireEvent.change(nameInput, { target: { value: "Cart Clearer" } });
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(participantNameInput, {
        target: { value: "Cart Clearer" },
      });
    });

    // Select dressage test if needed
    const selectTrigger = screen.queryByText("Select a test");
    if (selectTrigger) {
      fireEvent.click(selectTrigger);
      const option = screen.getByRole("option", { name: /Test 1/i });
      fireEvent.click(option);
    }

    // Submit the form
    const completeButton = screen.getByText(/Complete Booking/i);
    expect(completeButton).not.toBeDisabled();
    
    await act(async () => {
      fireEvent.click(completeButton);
    });

    // Verify redirect to Stripe checkout
    await waitFor(() => {
      expect(window.location.href).toBe('https://stripe.com/checkout');
    });
  });
});
