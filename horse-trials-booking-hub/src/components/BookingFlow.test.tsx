// src/components/BookingFlow.test.tsx
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { BookingFlow } from './BookingFlow';
import { useSession } from '@supabase/auth-helpers-react';
import { useCreateBooking } from '@/hooks/useBookings';
import { useToast } from '@/hooks/use-toast';
import { useEventDressageTests } from '@/hooks/useDressageTests';
import { usePricingSettings } from '@/hooks/usePricingSettings';
import { useEventActivityPricing } from '@/hooks/useEventPricing';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import type { TimeSlot } from './BookingFlow';
import type { Event } from '@/hooks/useEvents';

// Mock all hooks
vi.mock('@supabase/auth-helpers-react');
vi.mock('@/hooks/useBookings');
vi.mock('@/hooks/use-toast');
vi.mock('@/hooks/useDressageTests');
vi.mock('@/hooks/usePricingSettings');
vi.mock('@/hooks/useEventPricing');

// Mock the Select component to avoid pointer-events issues
vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange, 'data-testid': testId }: React.PropsWithChildren<{ value: string; onValueChange: (val: string) => void; 'data-testid'?: string }>) => (
    <select 
      value={value} 
      onChange={(e) => onValueChange(e.target.value)}
      data-testid={testId}
    >
      {children}
    </select>
  ),
  SelectContent: ({ children }: React.PropsWithChildren) => <div>{children}</div>,
  SelectItem: ({ children, value }: React.PropsWithChildren<{ value: string }>) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }: React.PropsWithChildren) => <div>{children}</div>,
  SelectValue: ({ placeholder }: { placeholder: string }) => <span>{placeholder}</span>,
}));

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    origin: 'http://localhost:3000',
    pathname: '/test',
    href: 'http://localhost:3000/test',
  },
  writable: true,
});

// Place this at the top-level (outside any function)
const toastSpy = vi.fn();
const mockUseToast = vi.fn(() => ({ toast: toastSpy }));

describe('BookingFlow Component', () => {
  const mockOnBack = vi.fn();
  const mockEvent = {
    id: 'event123',
    name: 'Test Event',
    date: '2024-05-01'
  };
  const mockSelectedSlots = [
    {
      id: 'slot1',
      time_slot_id: 'ts1',
      activity_id: 'act1',
      location_id: 'loc1',
      activity_type: 'dressage',
      location_name: 'Main Arena',
      time: '9:00 AM',
      expires_at: '2025-06-22T21:11:31.276Z',
      event_dressage_test_id: '',
      participant_name: '',
      horse_name: ''
    }
  ];

  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      user_metadata: { full_name: 'Test User' }
    }
  };

  const mockPricingSettings = {
    dressage_price: 45,
    show_jumping_price: 55,
    currency_symbol: '$'
  };

  const mockEventDressageTests = [
    {
      id: 'test1',
      dressage_test_library: {
        label: 'Intro Test A',
        levels_library: { name: 'Intro' }
      }
    }
  ];

  const mockUseCreateBooking = vi.fn(() => ({
    mutate: vi.fn(),
    isPending: false
  }));

  const mockUseEventDressageTests = vi.fn(() => ({
    data: mockEventDressageTests,
    isLoading: false
  }));

  const mockUsePricingSettings = vi.fn(() => ({
    data: mockPricingSettings
  }));

  const mockUseEventActivityPricing = vi.fn((eventId, activityType) => {
    if (activityType === 'show_jumping') {
      return { data: { price: 55, source: 'event' } };
    }
    return { data: { price: 45, source: 'event' } };
  });

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mocks
    (useSession as jest.Mock).mockReturnValue(mockSession);
    (useCreateBooking as jest.Mock).mockReturnValue(mockUseCreateBooking());
    (useToast as jest.Mock).mockImplementation(mockUseToast);
    (useEventDressageTests as jest.Mock).mockReturnValue(mockUseEventDressageTests());
    (usePricingSettings as jest.Mock).mockReturnValue(mockUsePricingSettings());
    (useEventActivityPricing as jest.Mock).mockImplementation(mockUseEventActivityPricing);
    
    // Setup fetch mock
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ url: 'https://stripe.com/checkout' })
    });
  });

  const renderBookingFlow = (props = {}) => {
    const defaultProps = {
      selectedSlots: mockSelectedSlots,
      event: mockEvent,
      onBack: mockOnBack,
      ...props
    };
    const queryClient = new QueryClient();
    return render(
      <QueryClientProvider client={queryClient}>
        <BookingFlow {...defaultProps} />
      </QueryClientProvider>
    );
  };

  const getPayerNameInput = () => screen.getByLabelText(/payer name/i);
  const getPayerEmailInput = () => screen.getByLabelText(/payer email/i);
  const getRiderNameInput = () => screen.getByLabelText(/rider name/i);
  const getHorseNameInput = () => screen.getByLabelText(/horse name/i);

  describe('Rendering and Initial State', () => {
    it('renders the booking form with correct title and sections', () => {
      renderBookingFlow();

      expect(screen.getByText('Complete Your Booking')).toBeInTheDocument();
      expect(screen.getByText('Participant Information')).toBeInTheDocument();
      expect(screen.getByText('Booking Details')).toBeInTheDocument();
      expect(screen.getByText('Back to Schedule')).toBeInTheDocument();
    });

    it('displays selected slots information', () => {
      renderBookingFlow();

      expect(screen.getByText('Main Arena')).toBeInTheDocument();
      expect(screen.getByText('9:00 AM')).toBeInTheDocument();
      expect(screen.getByText('dressage')).toBeInTheDocument();
    });

    it('shows pricing information', async () => {
      renderBookingFlow();

      expect(screen.getByText('Booking Details')).toBeInTheDocument();
      expect(screen.getByText('Main Arena')).toBeInTheDocument();
      expect(screen.getByText('9:00 AM')).toBeInTheDocument();

      await waitFor(() => {
        const match = Array.from(screen.getAllByText(/\$/)).find(el =>
          el.textContent?.trim().replace(/\s/g, '') === '$45'
        );
        expect(match).toBeTruthy();
      });
    });

    it('pre-fills form with user profile data', () => {
      renderBookingFlow();

      expect(getPayerNameInput()).toHaveValue('Test User');
      expect(getPayerEmailInput()).toHaveValue('<EMAIL>');
    });
  });

  describe('Form Interaction and Validation', () => {
    it('allows users to fill out all form fields', async () => {
      const user = userEvent.setup();
      renderBookingFlow();

      const nameInput = getPayerNameInput();
      const emailInput = getPayerEmailInput();
      const riderNameInput = getRiderNameInput();

      await user.clear(nameInput);
      await user.type(nameInput, 'John Doe');
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      await user.clear(riderNameInput);
      await user.type(riderNameInput, 'John Doe');

      expect(nameInput).toHaveValue('John Doe');
      expect(emailInput).toHaveValue('<EMAIL>');
      expect(riderNameInput).toHaveValue('John Doe');
    });

    it('disables submit button when required fields are empty', () => {
      renderBookingFlow();

      const completeButtons = screen.getAllByText(/Complete Booking/i);
      const disabledButton = completeButtons.find(button => button.disabled);
      expect(disabledButton).toBeDisabled();
    });

    it('enables submit button when all required fields are filled', async () => {
      const user = userEvent.setup();
      renderBookingFlow();

      // Fill in required fields
      const nameInput = getPayerNameInput();
      const emailInput = getPayerEmailInput();
      const riderNameInput = getRiderNameInput();

      await user.clear(nameInput);
      await user.type(nameInput, 'John Doe');
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      await user.clear(riderNameInput);
      await user.type(riderNameInput, 'John Doe');

      // Select a dressage test
      const dressageSelect = screen.getByTestId('dressage-test-select');
      await user.selectOptions(dressageSelect, 'test1');

      await waitFor(() => {
        const completeButtons = screen.getAllByText(/Complete Booking/i);
        const enabledButton = completeButtons.find(button => !button.disabled);
        expect(enabledButton).not.toBeDisabled();
      });
    });

    it('validates email format correctly', async () => {
      const user = userEvent.setup();
      renderBookingFlow();

      const emailInput = getPayerEmailInput();
      await user.clear(emailInput);
      await user.type(emailInput, 'invalid-email');

      const completeButtons = screen.getAllByText(/Complete Booking/i);
      const disabledButton = completeButtons.find(button => button.disabled);
      expect(disabledButton).toBeDisabled();
    });

    it('requires rider name to be filled', async () => {
      const user = userEvent.setup();
      renderBookingFlow();

      const riderNameInput = getRiderNameInput();
      await user.clear(riderNameInput);

      const completeButtons = screen.getAllByText(/Complete Booking/i);
      const disabledButton = completeButtons.find(button => button.disabled);
      expect(disabledButton).toBeDisabled();
    });
  });

  describe('Dressage Test Selection', () => {
    it('shows dressage test dropdown for dressage activities', () => {
      renderBookingFlow();

      expect(screen.getByTestId('dressage-test-select')).toBeInTheDocument();
      expect(screen.getByText('Select a test')).toBeInTheDocument();
    });

    it('handles show jumping activity type without dressage test selection', () => {
      const showJumpingSlots = [
        {
          ...mockSelectedSlots[0],
          activity_type: 'show_jumping',
          fixed_level: 'Beginner'
        }
      ];
      renderBookingFlow({ selectedSlots: showJumpingSlots });

      expect(screen.queryByTestId('dressage-test-select')).not.toBeInTheDocument();
      
      // Use getAllByText since multiple elements contain the text
      const levelElements = screen.getAllByText((content, element) => {
        return element?.textContent?.includes('Level: Beginner');
      });
      expect(levelElements.length).toBeGreaterThan(0);
    });
  });

  describe('Price Calculation', () => {
    it('calculates total price correctly for multiple slots', async () => {
      const multipleSlots = [
        ...mockSelectedSlots,
        {
          ...mockSelectedSlots[0],
          id: 'slot2',
          activity_type: 'show_jumping'
        }
      ];
      renderBookingFlow({ selectedSlots: multipleSlots });

      await waitFor(() => {
        // Total should be $45 (dressage) + $55 (show jumping) = $100
        expect(screen.getByText('$100')).toBeInTheDocument();
      });
    });

    it('shows correct pricing for different activity types', async () => {
      const user = userEvent.setup();
      const multipleSlots = [
        {
          id: 'slot1',
          time_slot_id: 'ts1',
          activity_id: 'act1',
          location_id: 'loc1',
          activity_type: 'dressage',
          location_name: 'Main Arena',
          time: '9:00 AM',
          expires_at: '2025-06-22T21:11:31.276Z',
          event_dressage_test_id: '',
          participant_name: '',
          horse_name: ''
        },
        {
          id: 'slot2',
          time_slot_id: 'ts2',
          activity_id: 'act2',
          location_id: 'loc2',
          activity_type: 'show_jumping',
          location_name: 'Outdoor Ring',
          time: '10:00 AM',
          expires_at: '2025-06-22T21:11:31.276Z',
          event_dressage_test_id: '',
          participant_name: '',
          horse_name: '',
          fixed_level: 'Intro'
        }
      ];

      renderBookingFlow({ selectedSlots: multipleSlots });

      // Fill in participant names for both slots
      const riderNameInputs = screen.getAllByLabelText(/rider name/i);
      await user.clear(riderNameInputs[0]);
      await user.type(riderNameInputs[0], 'Alice');
      await user.clear(riderNameInputs[1]);
      await user.type(riderNameInputs[1], 'Bob');

      // Select a dressage test for the dressage slot
      const dressageSelect = screen.getByTestId('dressage-test-select');
      await user.selectOptions(dressageSelect, 'test1');

      // Wait for price $45 (dressage)
      await waitFor(() => {
        const match = Array.from(screen.getAllByText(/\$/)).find(el =>
          el.textContent?.trim().replace(/\s/g, '') === '$45'
        );
        expect(match).toBeTruthy();
      });
      // Wait for price $100 (total)
      await waitFor(() => {
        const match = Array.from(screen.getAllByText(/\$/)).find(el =>
          el.textContent?.trim().replace(/\s/g, '') === '$100'
        );
        expect(match).toBeTruthy();
      });
    });
  });

  describe('Stripe Checkout Flow', () => {
    it('successfully initiates Stripe checkout when form is submitted', async () => {
      const user = userEvent.setup();
      renderBookingFlow();

      // First check if the slot details section is rendered
      expect(screen.getByText('Main Arena')).toBeInTheDocument();
      expect(screen.getByText('9:00 AM')).toBeInTheDocument();

      // Fill in required fields
      const nameInput = getPayerNameInput();
      const emailInput = getPayerEmailInput();
      
      await user.clear(nameInput);
      await user.type(nameInput, 'John Doe');
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');

      // Fill in rider name for the slot
      const riderNameInput = screen.getByLabelText(/rider name/i);
      await user.clear(riderNameInput);
      await user.type(riderNameInput, 'John Doe');

      // Select a dressage test
      const dressageSelect = screen.getByTestId('dressage-test-select');
      await user.selectOptions(dressageSelect, 'test1');

      // Wait for the button to be enabled
      await waitFor(() => {
        const completeButton = screen.getByText('Complete Booking');
        expect(completeButton).not.toBeDisabled();
      });

      // Submit the form
      const completeButton = screen.getByText('Complete Booking');
      await user.click(completeButton);

      // Verify fetch was called with correct data
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('create-stripe-checkout'),
          expect.objectContaining({
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: expect.stringContaining('John Doe')
          })
        );
      });
    });

    it('handles Stripe checkout errors gracefully', async () => {
      const user = userEvent.setup();
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ error: 'Payment failed' })
      });

      renderBookingFlow();

      // Wait for slot details to be rendered
      await waitFor(() => {
        expect(screen.getByText('Rider Name*')).toBeInTheDocument();
      });

      // Fill in required fields
      const nameInput = getPayerNameInput();
      const emailInput = getPayerEmailInput();

      await user.clear(nameInput);
      await user.type(nameInput, 'John Doe');
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');

      // Fill in rider name for the slot
      const riderNameInput = screen.getByLabelText(/rider name/i);
      await user.clear(riderNameInput);
      await user.type(riderNameInput, 'John Doe');

      // Select a dressage test
      const dressageSelect = screen.getByTestId('dressage-test-select');
      await user.selectOptions(dressageSelect, 'test1');

      // Wait for the button to be enabled
      await waitFor(() => {
        const completeButton = screen.getByText('Complete Booking');
        expect(completeButton).not.toBeDisabled();
      });

      // Submit the form
      const completeButton = screen.getByText('Complete Booking');
      await user.click(completeButton);

      // Verify error toast was shown
      await waitFor(() => {
        expect(toastSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            title: "Payment Error",
            description: expect.stringContaining('Payment failed'),
            variant: "destructive"
          })
        );
      });
    });

    it('handles network errors during checkout', async () => {
      const user = userEvent.setup();
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      renderBookingFlow();

      // Wait for slot details to be rendered
      await waitFor(() => {
        expect(screen.getByText('Rider Name*')).toBeInTheDocument();
      });

      // Fill in required fields
      const nameInput = getPayerNameInput();
      const emailInput = getPayerEmailInput();

      await user.clear(nameInput);
      await user.type(nameInput, 'John Doe');
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');

      // Fill in rider name for the slot
      const riderNameInput = screen.getByLabelText(/rider name/i);
      await user.clear(riderNameInput);
      await user.type(riderNameInput, 'John Doe');

      // Select a dressage test
      const dressageSelect = screen.getByTestId('dressage-test-select');
      await user.selectOptions(dressageSelect, 'test1');

      // Wait for the button to be enabled
      await waitFor(() => {
        const completeButton = screen.getByText('Complete Booking');
        expect(completeButton).not.toBeDisabled();
      });

      // Submit the form
      const completeButton = screen.getByText('Complete Booking');
      await user.click(completeButton);

      // Verify error toast was shown
      await waitFor(() => {
        expect(toastSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            title: "Payment Error",
            description: expect.stringContaining('Network error'),
            variant: "destructive"
          })
        );
      });
    });
  });

  describe('Slot Expiration', () => {
    it('shows expiration warning when slots are about to expire', async () => {
      const expiringSlots = [
        {
          ...mockSelectedSlots[0],
          expires_at: new Date(Date.now() + 60000).toISOString() // 1 minute from now
        }
      ];
      renderBookingFlow({ selectedSlots: expiringSlots });

      await waitFor(() => {
        const expirationElement = document.querySelector('.text-red-600');
        expect(expirationElement).toBeTruthy();
        expect(expirationElement?.textContent).toContain('Your reservation will expire in:');
        expect(
          expirationElement?.textContent?.includes('minute(s)') ||
          expirationElement?.textContent?.includes('second(s)')
        ).toBeTruthy();
      }, { timeout: 5000 });
    });
  });

  describe('Back Navigation', () => {
    it('calls onBack when back button is clicked', async () => {
      const user = userEvent.setup();
      renderBookingFlow();

      const backButton = screen.getByText('Back to Schedule');
      await user.click(backButton);

      expect(mockOnBack).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty selected slots gracefully', () => {
      renderBookingFlow({ selectedSlots: [] });

      expect(screen.getByText('Loading booking information...')).toBeInTheDocument();
      expect(screen.queryByText('Main Arena')).not.toBeInTheDocument();
    });

    it('handles missing event data gracefully', () => {
      renderBookingFlow({ event: null });

      expect(screen.getByText('Loading booking information...')).toBeInTheDocument();
      expect(screen.queryByText('Main Arena')).not.toBeInTheDocument();
    });

    it('handles missing user session gracefully', () => {
      (useSession as jest.Mock).mockReturnValue({ user: null });
      renderBookingFlow();

      expect(screen.getByText('Complete Your Booking')).toBeInTheDocument();
      expect(getPayerNameInput()).toHaveValue(''); // Empty name field
    });
  });

  describe('UI Element Regression', () => {
    it('ensures all critical UI elements are present in the default state', async () => {
      renderBookingFlow();

      // Header and navigation
      expect(screen.getByText('Complete Your Booking')).toBeInTheDocument();
      expect(screen.getByText('Back to Schedule')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /back to schedule/i })).toBeInTheDocument();

      // Section headers
      expect(screen.getByText('Participant Information')).toBeInTheDocument();
      expect(screen.getByText('Booking Details')).toBeInTheDocument();

      // Participant information form fields
      expect(screen.getByLabelText(/payer name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/payer email/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/enter your full name/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/enter your email/i)).toBeInTheDocument();

      // Booking details - slot information
      expect(screen.getByText('Main Arena')).toBeInTheDocument();
      expect(screen.getByText('9:00 AM')).toBeInTheDocument();
      expect(screen.getByText('dressage')).toBeInTheDocument();

      // Slot form fields
      expect(screen.getByLabelText(/rider name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/horse name/i)).toBeInTheDocument();

      // Dressage test selection
      expect(screen.getByTestId('dressage-test-select')).toBeInTheDocument();
      expect(screen.getByText('Select a test')).toBeInTheDocument();

      // Pricing display
      await waitFor(() => {
        const match = Array.from(screen.getAllByText(/\$/)).find(el =>
          el.textContent?.trim().replace(/\s/g, '') === '$45'
        );
        expect(match).toBeTruthy();
      });

      // Booking summary
      expect(screen.getByText('Event:')).toBeInTheDocument();
      expect(screen.getByText('Test Event')).toBeInTheDocument();
      expect(screen.getByText('Number of slots:')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('Total:')).toBeInTheDocument();

      // Action buttons
      expect(screen.getByRole('button', { name: /complete booking/i })).toBeInTheDocument();

      // Footer text
      expect(screen.getByText(/you will receive a confirmation email/i)).toBeInTheDocument();
    });

    it('ensures all critical UI elements are present for show jumping activity', async () => {
      const showJumpingSlots = [
        {
          ...mockSelectedSlots[0],
          activity_type: 'show_jumping',
          fixed_level: 'Beginner'
        }
      ];
      renderBookingFlow({ selectedSlots: showJumpingSlots });

      // Header and navigation
      expect(screen.getByText('Complete Your Booking')).toBeInTheDocument();
      expect(screen.getByText('Back to Schedule')).toBeInTheDocument();

      // Section headers
      expect(screen.getByText('Participant Information')).toBeInTheDocument();
      expect(screen.getByText('Booking Details')).toBeInTheDocument();

      // Booking details - slot information
      expect(screen.getByText('Main Arena')).toBeInTheDocument();
      expect(screen.getByText('9:00 AM')).toBeInTheDocument();
      expect(screen.getByText('show jumping')).toBeInTheDocument();

      // Show jumping level display
      const levelElements = screen.getAllByText((content, element) => {
        return element?.textContent?.includes('Level: Beginner');
      });
      expect(levelElements.length).toBeGreaterThan(0);

      // No dressage test selection for show jumping
      expect(screen.queryByTestId('dressage-test-select')).not.toBeInTheDocument();

      // Pricing display
      await waitFor(() => {
        const match = Array.from(screen.getAllByText(/\$/)).find(el =>
          el.textContent?.trim().replace(/\s/g, '') === '$55'
        );
        expect(match).toBeTruthy();
      });

      // Action buttons
      expect(screen.getByRole('button', { name: /complete booking/i })).toBeInTheDocument();
    });

    it('ensures all critical UI elements are present for multiple slots', async () => {
      const multipleSlots = [
        ...mockSelectedSlots,
        {
          ...mockSelectedSlots[0],
          id: 'slot2',
          activity_type: 'show_jumping',
          fixed_level: 'Beginner'
        }
      ];
      renderBookingFlow({ selectedSlots: multipleSlots });

      // Header and navigation
      expect(screen.getByText('Complete Your Booking')).toBeInTheDocument();
      expect(screen.getByText('Back to Schedule')).toBeInTheDocument();

      // Multiple slot information
      expect(screen.getAllByText('Main Arena')).toHaveLength(2);
      expect(screen.getAllByText('9:00 AM')).toHaveLength(2);
      expect(screen.getByText('dressage')).toBeInTheDocument();
      expect(screen.getByText('show jumping')).toBeInTheDocument();

      // Multiple rider name inputs
      const riderNameInputs = screen.getAllByLabelText(/rider name/i);
      expect(riderNameInputs).toHaveLength(2);

      // Multiple horse name inputs
      const horseNameInputs = screen.getAllByLabelText(/horse name/i);
      expect(horseNameInputs).toHaveLength(2);

      // Dressage test selection (only for dressage slot)
      expect(screen.getByTestId('dressage-test-select')).toBeInTheDocument();

      // Show jumping level display
      const levelElements = screen.getAllByText((content, element) => {
        return element?.textContent?.includes('Level: Beginner');
      });
      expect(levelElements.length).toBeGreaterThan(0);

      // Total price calculation
      await waitFor(() => {
        const match = Array.from(screen.getAllByText(/\$/)).find(el =>
          el.textContent?.trim().replace(/\s/g, '') === '$100'
        );
        expect(match).toBeTruthy();
      });

      // Booking summary with correct slot count
      expect(screen.getByText('Number of slots:')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();

      // Action buttons
      expect(screen.getByRole('button', { name: /complete booking/i })).toBeInTheDocument();
    });

    it('ensures expiration warning appears when slots are expiring', async () => {
      const expiringSlots = [
        {
          ...mockSelectedSlots[0],
          expires_at: new Date(Date.now() + 60000).toISOString() // 1 minute from now
        }
      ];
      renderBookingFlow({ selectedSlots: expiringSlots });

      // Header and navigation
      expect(screen.getByText('Complete Your Booking')).toBeInTheDocument();
      expect(screen.getByText('Back to Schedule')).toBeInTheDocument();

      // Expiration warning
      await waitFor(() => {
        const expirationElement = document.querySelector('.text-red-600');
        expect(expirationElement).toBeTruthy();
        expect(expirationElement?.textContent).toContain('Your reservation will expire in:');
        expect(
          expirationElement?.textContent?.includes('minute(s)') ||
          expirationElement?.textContent?.includes('second(s)')
        ).toBeTruthy();
      }, { timeout: 5000 });

      // All other critical elements should still be present
      expect(screen.getByText('Participant Information')).toBeInTheDocument();
      expect(screen.getByText('Booking Details')).toBeInTheDocument();
      expect(screen.getByLabelText(/payer name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/payer email/i)).toBeInTheDocument();
      expect(screen.getByText('Main Arena')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /complete booking/i })).toBeInTheDocument();
    });

    it('ensures loading state displays correctly', () => {
      renderBookingFlow({ selectedSlots: [] });

      // Loading spinner
      expect(screen.getByText('Loading booking information...')).toBeInTheDocument();
      
      // Loading spinner element
      const spinner = document.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });
  });
});
