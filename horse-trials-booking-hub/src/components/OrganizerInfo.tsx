import React from 'react';
import { Badge } from '@/components/ui/badge';
import { User } from 'lucide-react';

interface OrganizerInfoProps {
  organizer?: {
    id: string;
    email: string;
    full_name?: string;
  } | null;
  showLabel?: boolean;
  className?: string;
}

export const OrganizerInfo: React.FC<OrganizerInfoProps> = ({ 
  organizer, 
  showLabel = true,
  className = ""
}) => {
  if (!organizer) {
    return (
      <div className={`flex items-center text-gray-500 ${className}`}>
        <User className="w-4 h-4 mr-2" />
        <span className="text-sm">No organizer assigned</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center ${className}`}>
      <User className="w-4 h-4 mr-2 text-blue-600" />
      {showLabel && <span className="text-sm font-medium mr-2">Organizer:</span>}
      <span className="text-sm text-gray-700">
        {organizer.full_name || organizer.email}
      </span>
      <Badge variant="outline" className="ml-2 text-xs bg-blue-50 text-blue-700 border-blue-200">
        Primary
      </Badge>
    </div>
  );
}; 