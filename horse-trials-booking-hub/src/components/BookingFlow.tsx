import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, User, Mail, Calendar, Clock, Target, Edit, CheckCircle } from 'lucide-react';
import { useCreateBooking } from '@/hooks/useBookings';
import { useToast } from '@/hooks/use-toast';
import { useEventDressageTests } from '@/hooks/useDressageTests';
import { useQueryClient } from '@tanstack/react-query';
import { usePricingSettings, getActivityPrice } from '@/hooks/usePricingSettings';
import { supabase, getEdgeFunctionUrl } from '@/integrations/supabase/client';
import { getUserSessionId } from '@/hooks/useSlotReservations';
import { useSession } from '@supabase/auth-helpers-react';
import { useEventActivityPricing } from '@/hooks/useEventPricing';
import { Event } from '@/hooks/useEvents';

interface TimeSlot {
  id: string;
  start_time: string;
  end_time: string;
  activity_type: string;
  location_name: string;
  event_dressage_test_id?: string;
  participant_name?: string;
  horse_name?: string;
  expires_at?: string;
  fixed_level?: string;
}

interface BookingFlowProps {
  selectedSlots: TimeSlot[];
  event: Event | null;
  onBack: () => void;
}

export const BookingFlow: React.FC<BookingFlowProps> = ({
  selectedSlots,
  event,
  onBack
}) => {
  const [bookingData, setBookingData] = useState({
    payer_email: '',
    payer_name: '',
    horse_name: '',
    participant_name: '',
  });
  const [profileName, setProfileName] = useState('');

  const session = useSession();
  const eventId = event?.id;

  const [updatedSlots, setUpdatedSlots] = useState(
    selectedSlots.map(slot => ({
      ...slot,
      event_dressage_test_id: slot.event_dressage_test_id || '',
      participant_name: slot.participant_name || '',
      horse_name: slot.horse_name || ''
    }))
  );
  const [isLoading, setIsLoading] = useState(true);
  const userSessionId = getUserSessionId();

  const { toast } = useToast();
  const createBooking = useCreateBooking();
  const { data: eventDressageTests, isLoading: isLoadingTests } = useEventDressageTests(eventId || '');
  const queryClient = useQueryClient();
  const { data: pricingSettings } = usePricingSettings();
  
  // Get pricing for all activity types in the selected slots
  const uniqueActivityTypes = [...new Set(updatedSlots.map(slot => slot.activity_type))];
  
  // Call hooks at the top level for each unique activity type
  const dressagePricing = useEventActivityPricing(eventId || '', 'dressage');
  const showJumpingPricing = useEventActivityPricing(eventId || '', 'show_jumping');
  const crossCountryPricing = useEventActivityPricing(eventId || '', 'cross_country');
  
  // Create a map of activity pricing for easy lookup
  const activityPricingMap = useMemo(() => {
    const map: Record<string, number> = {};
    
    if (dressagePricing?.data && dressagePricing.data.source !== 'global') {
      map['dressage'] = dressagePricing.data.price;
    }
    if (showJumpingPricing?.data && showJumpingPricing.data.source !== 'global') {
      map['show_jumping'] = showJumpingPricing.data.price;
    }
    if (crossCountryPricing?.data && crossCountryPricing.data.source !== 'global') {
      map['cross_country'] = crossCountryPricing.data.price;
    }
    
    return map;
  }, [dressagePricing?.data, showJumpingPricing?.data, crossCountryPricing?.data]);

  useEffect(() => {
    console.log('BookingFlow mounted or event/selectedSlots changed.');
    console.log('Event:', event);
    console.log('Selected Slots:', selectedSlots);
    console.log('Session:', session);
  }, [event, selectedSlots, session]);

  useEffect(() => {
    if (event && selectedSlots.length > 0) {
      setUpdatedSlots(
        selectedSlots.map(slot => ({
          ...slot,
          event_dressage_test_id: slot.event_dressage_test_id || '',
          participant_name: slot.participant_name || '',
          horse_name: slot.horse_name || ''
        }))
      );
      setIsLoading(false);
    }
  }, [event, selectedSlots]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      const now = Date.now();
      setUpdatedSlots((prevSlots) => {
        if (prevSlots.length === 0) {
          return prevSlots;
        }

        const stillActiveSlots = prevSlots.filter((slot) => {
          if (!slot.expires_at) return true;
          return new Date(slot.expires_at).getTime() > now;
        });

        if (stillActiveSlots.length === 0 && prevSlots.length > 0) {
          toast({
            title: "Slots Expired",
            description: "All your selected time slots have expired. Please return to the schedule and select again.",
            variant: "destructive",
          });
          onBack();
          return [];
        }
        return stillActiveSlots;
      });
    }, 1000);

    return () => clearInterval(intervalId);
  }, [onBack, toast]);

  useEffect(() => {
    if (!session?.user?.id) return;

    const fetchProfile = async () => {
      try {
        // For now, just use user metadata instead of querying profiles table
        const fullName = session.user.user_metadata?.full_name;
        if (fullName) {
          setProfileName(fullName);
        } else {
          // Fallback to email if no full name in metadata
          setProfileName(session.user.email || 'User');
        }
      } catch (error) {
        console.warn('Error in fetchProfile:', error);
        // Fallback to email
        setProfileName(session.user.email || 'User');
      }
    };

    fetchProfile();
  }, [session]);

  useEffect(() => {
    if (!session || !profileName) return;

    setBookingData(prev => ({
      ...prev,
      payer_name: prev.payer_name || profileName,
      payer_email: prev.payer_email || session.user.email || '',
      participant_name: prev.participant_name || profileName,
    }));
  }, [session, profileName]);

  // Ensure participant names are defaulted when profile name is loaded
  useEffect(() => {
    if (profileName && updatedSlots.length > 0) {
      setUpdatedSlots(prevSlots =>
        prevSlots.map(slot => ({
          ...slot,
          participant_name: slot.participant_name || profileName,
        }))
      );
    }
  }, [profileName, updatedSlots.length]);

  const prevPayerNameRef = useRef<string>();

  useEffect(() => {
    const currentPayerName = bookingData.payer_name || '';
    const previousPayerName = prevPayerNameRef.current || '';

    setUpdatedSlots(prevSlots =>
      prevSlots.map(slot => {
        const slotParticipantName = slot.participant_name || '';
        const shouldUpdate = slotParticipantName === '' || slotParticipantName === previousPayerName;
        return shouldUpdate ? { ...slot, participant_name: currentPayerName } : slot;
      })
    );
    prevPayerNameRef.current = currentPayerName;
  }, [bookingData.payer_name]);

  const handleInputChange = (field: keyof typeof bookingData, value: string) => {
    setBookingData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Helper function to get pricing for a slot
  const getSlotPrice = (slot: TimeSlot) => {
    // Try to get event-specific pricing first
    if (slot.activity_type && activityPricingMap[slot.activity_type]) {
      return activityPricingMap[slot.activity_type];
    }
    
    // Fallback to global pricing
    return getActivityPrice(slot.activity_type, pricingSettings);
  };

  const calculateTotal = () => {
    return updatedSlots.reduce((total, slot) => {
      const price = getSlotPrice(slot);
      return total + price;
    }, 0);
  };

  const isFormValid = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const hasBasicInfo = !!(
      bookingData.payer_name &&
      emailRegex.test(bookingData.payer_email)
    );

    const allDressageSlotsHaveTests = updatedSlots.every(slot =>
      slot.activity_type !== 'dressage' ||
      (slot.activity_type === 'dressage' && slot.event_dressage_test_id)
    );

    const allSlotsHaveParticipantNames = updatedSlots.every(slot =>
      !!(slot.participant_name)
    );

    return hasBasicInfo && allDressageSlotsHaveTests && allSlotsHaveParticipantNames;
  };

  const handleDressageTestChange = (slotId: string, testId: string) => {
    setUpdatedSlots(prev =>
      prev.map(slot =>
        slot.id === slotId ? { ...slot, event_dressage_test_id: testId } : slot
      )
    );
  };

  const handleSubmit = async () => {
    if (!isFormValid()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const stripeBookingData = {
        // Basic booking info
        userId: session?.user?.id || null,
        
        // Payer info
        payer_name: bookingData.payer_name,
        payer_email: bookingData.payer_email,
        
        // Session info for clearing reservations
        userSessionId: userSessionId,
        
        // Slots as an array (expected by the Edge Function)
        slots: updatedSlots.map(slot => ({
          locationId: slot.location_id,
          timeSlotId: slot.time_slot_id,
          activityId: slot.activity_id,
          activityName: slot.activity_type?.replace('_', ' '),
          locationName: slot.location_name,
          timeSlot: slot.time,
          price: getSlotPrice(slot),
          
          // Participant details
          participant_name: slot.participant_name,
          horse_name: slot.horse_name,
          
          // Dressage test info
          event_dressage_test_id: slot.event_dressage_test_id || '',
          activity_description: slot.activity_type === 'dressage' && slot.event_dressage_test_id
            ? `Dressage - ${eventDressageTests?.find(t => t.id === slot.event_dressage_test_id)?.dressage_test_library?.label || 'Test'}`
            : slot.activity_type === 'show_jumping' && slot.fixed_level
            ? `Show Jumping - ${slot.fixed_level}`
            : slot.activity_type?.replace('_', ' '),
        })),
      };

      // Use the new public function URL
      const functionName = import.meta.env.VITE_STRIPE_FUNCTION_NAME || 'create-stripe-checkout-public';
      const functionUrl = getEdgeFunctionUrl(functionName);

      // Call the Edge Function to create a Stripe Checkout session
      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookingData: stripeBookingData,
          successUrl: window.location.origin + '/booking-success?session_id={CHECKOUT_SESSION_ID}',
          cancelUrl: window.location.origin + window.location.pathname,
        }),
      });

      const data = await response.json();
      if (!response.ok || !data.url) {
        console.error('Full error response:', data);
        throw new Error(data.error || data.details || 'Failed to create Stripe Checkout session');
      }

      // Redirect to Stripe Checkout
      window.location.href = data.url;
    } catch (error) {
      console.error('Stripe Checkout error:', error);
      console.error('Error details:', error);
      
      let errorMessage = 'There was a problem redirecting to payment. Please try again.';
      if (error.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: "Payment Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4 text-text-primary">Loading booking information...</p>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow border border-cream-light p-6">
      <div className="flex items-center space-x-4 mb-6">
        <Button
          variant="ghost"
          onClick={onBack}
          className="text-primary hover:bg-cream-light"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Schedule
        </Button>
        <h2 className="font-playfair text-xl font-semibold tracking-tight text-primary">Complete Your Booking</h2>
      </div>

      <div className="space-y-6">
        {/* Participant Information */}
        <div className="space-y-4">
          <h3 className="font-playfair text-lg font-semibold tracking-tight text-primary flex items-center">
            <User className="w-5 h-5 mr-2" />
            Participant Information
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="payer_name" className="text-text-primary">Payer Name *</Label>
              <Input
                id="payer_name"
                value={bookingData.payer_name}
                onChange={(e) => handleInputChange('payer_name', e.target.value)}
                placeholder="Enter your full name"
                className="border-cream-light focus:ring-gold"
              />
            </div>
            <div>
              <Label htmlFor="payer_email" className="text-text-primary">Payer Email *</Label>
              <Input
                id="payer_email"
                type="email"
                value={bookingData.payer_email}
                onChange={(e) => handleInputChange('payer_email', e.target.value)}
                placeholder="Enter your email"
                className="border-cream-light focus:ring-gold"
              />
            </div>
          </div>
        </div>

        {/* Booking Details */}
        <div className="space-y-4">
          <h3 className="font-playfair text-lg font-semibold tracking-tight text-primary flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Booking Details
          </h3>
          {updatedSlots.map((slot) => (
            <div key={slot.id} className="p-4 border border-cream-light rounded-lg bg-cream">
              <div className="flex justify-between items-start mb-3">
                <div className="w-full">
                  <h4 className="font-medium text-primary">{slot.location_name}</h4>
                  <div className="flex items-center justify-center space-x-4 text-sm text-text-secondary mt-1">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {slot.time}
                    </div>
                    <Badge variant="outline" className="capitalize bg-cream text-primary border-primary">
                      {slot.activity_type && slot.activity_type.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
                <span className="font-medium">
                  {pricingSettings?.currency_symbol || '$'}
                  {getSlotPrice(slot)}
                </span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                <div>
                  <Label htmlFor={`participant_name_${slot.id}`} className="text-sm text-text-primary">Rider Name*</Label>
                  <Input
                    id={`participant_name_${slot.id}`}
                    type="text"
                    value={slot.participant_name || ''}
                    onChange={(e) => {
                      const updated = updatedSlots.map(s => s.id === slot.id ? { ...s, participant_name: e.target.value } : s);
                      setUpdatedSlots(updated);
                    }}
                    className="h-8 text-sm border-cream-light focus:ring-gold"
                  />
                </div>
                <div>
                  <Label htmlFor={`horse_name_${slot.id}`} className="text-sm text-text-primary">Horse Name (Optional)</Label>
                  <Input
                    id={`horse_name_${slot.id}`}
                    type="text"
                    value={slot.horse_name || ''}
                    onChange={(e) => {
                      const updated = updatedSlots.map(s => s.id === slot.id ? { ...s, horse_name: e.target.value } : s);
                      setUpdatedSlots(updated);
                    }}
                    className="h-8 text-sm border-cream-light focus:ring-gold"
                  />
                </div>
              </div>

              {slot.activity_type === 'dressage' && (
                <div className="mt-2">
                  <Label className="text-sm font-medium mb-1 block text-text-primary">
                    Dressage Test
                  </Label>
                  <div className="flex items-center gap-2">
                    <Select
                      value={slot.event_dressage_test_id || ''}
                      onValueChange={(value) => handleDressageTestChange(slot.id, value)}
                      data-testid="dressage-test-select"
                    >
                      <SelectTrigger className="h-8 text-sm border-cream-light focus:ring-gold">
                        <SelectValue placeholder="Select a test" />
                      </SelectTrigger>
                      <SelectContent>
                        {eventDressageTests?.map((eventTest) => (
                          <SelectItem key={eventTest.id} value={eventTest.id}>
                            {eventTest.dressage_test_library?.label}
                            {eventTest.dressage_test_library?.levels_library &&
                              ` (${eventTest.dressage_test_library.levels_library.name})`
                            }
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {slot.activity_type === 'show_jumping' && slot.fixed_level && (
                <div className="flex items-center mt-2">
                  <Target className="w-4 h-4 mr-2 text-gold" />
                  <span className="text-sm text-text-secondary">
                    Level: <span className="font-medium">{slot.fixed_level}</span>
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Booking Summary */}
        <div className="border-t border-cream-light pt-4 space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Event:</span>
              <span className="font-medium">{event.name}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Number of slots:</span>
              <span className="font-medium">{selectedSlots.length}</span>
            </div>
          </div>

          <div className="border-t border-cream-light pt-3">
            <div className="flex justify-between items-center">
              <span className="font-medium">Total:</span>
              <span className="text-xl font-bold text-primary">
                {pricingSettings?.currency_symbol || '$'}{calculateTotal()}
              </span>
            </div>
          </div>
          
          {updatedSlots.length > 0 && updatedSlots[0].expires_at && (
            <div className="text-sm text-red-600 text-center">
              ⏳ Your reservation will expire in: {Math.floor((new Date(updatedSlots[0].expires_at).getTime() - Date.now()) / 60000)} minute(s)
              and {Math.floor(((new Date(updatedSlots[0].expires_at).getTime() - Date.now()) % 60000) / 1000)} second(s)
            </div>
          )}
          
          <Button
            onClick={handleSubmit}
            disabled={!isFormValid() || createBooking.isPending}
            className="w-full btn-primary disabled:bg-gray-300"
          >
            {createBooking.isPending ? 'Processing...' : 'Complete Booking'}
          </Button>

          <p className="text-xs text-text-secondary text-center">
            You will receive a confirmation email with your booking details and a unique link to manage your booking.
          </p>
        </div>
      </div>
    </div>
  );
};
