import { render, screen, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { PublicBookings } from './PublicBookings';
import { usePublicEntries } from '../hooks/useBookings';
import { useLocations } from '../hooks/useLocations';
import userEvent from '@testing-library/user-event';

// Mock the hooks
vi.mock('../hooks/useBookings');
vi.mock('../hooks/useLocations');

// Mock the Select component to avoid pointer-events issues
vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange, 'data-testid': testId }) => {
    // Extract options from children
    const options = [];
    const processChildren = (children) => {
      if (Array.isArray(children)) {
        children.forEach(child => processChildren(child));
      } else if (children?.type?.name === 'SelectContent') {
        processChildren(children.props.children);
      } else if (children?.type?.name === 'SelectItem') {
        options.push({
          value: children.props.value,
          label: children.props.children
        });
      }
    };
    processChildren(children);

    return (
      <select 
        value={value} 
        onChange={(e) => onValueChange(e.target.value)}
        data-testid={testId}
      >
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    );
  },
  SelectContent: ({ children }) => children,
  SelectItem: ({ children, value }) => ({ type: { name: 'SelectItem' }, props: { children, value } }),
  SelectTrigger: ({ children }) => children,
  SelectValue: ({ placeholder }) => placeholder,
}));

const mockUsePublicEntries = vi.mocked(usePublicEntries);
const mockUseLocations = vi.mocked(useLocations);

// Mock data
const mockEvent = {
  id: 'event-123',
  name: 'Test Event',
  start_date: '2024-06-15',
  end_date: '2024-06-16',
  is_active: true,
  organizer_id: 'org-123',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockEntries = [
  {
    booking_id: 'booking-1',
    participant_name: 'John Doe',
    horse_name: 'Thunder',
    booking_code: 'ABC123',
    activity_description: 'Dressage Test',
    time_slot_start_time: '2024-06-15T10:00:00Z',
    time_slot_end_time: '2024-06-15T11:00:00Z',
    time_slot_level: 'Intro',
    location_name: 'Main Arena',
    location_event_id: 'event-123',
    activity_specific_type: 'dressage',
    activity_level: 'Intro',
    dressage_test_label: 'Intro Test A',
    dressage_level_name: 'Intro'
  },
  {
    booking_id: 'booking-2',
    participant_name: 'Jane Smith',
    horse_name: 'Storm',
    booking_code: 'DEF456',
    activity_description: 'Show Jumping',
    time_slot_start_time: '2024-06-15T14:00:00Z',
    time_slot_end_time: '2024-06-15T15:00:00Z',
    time_slot_level: 'Prelim',
    location_name: 'Outdoor Ring',
    location_event_id: 'event-123',
    activity_specific_type: 'show_jumping',
    activity_level: 'Prelim',
    dressage_test_label: null,
    dressage_level_name: null
  },
  {
    booking_id: 'booking-3',
    participant_name: 'Alice Johnson',
    horse_name: 'Lightning',
    booking_code: 'GHI789',
    activity_description: 'Cross Country',
    time_slot_start_time: '2024-06-16T09:00:00Z',
    time_slot_end_time: '2024-06-16T10:00:00Z',
    time_slot_level: 'Novice',
    location_name: 'Cross Country Course',
    location_event_id: 'event-123',
    activity_specific_type: 'cross_country',
    activity_level: 'Novice',
    dressage_test_label: null,
    dressage_level_name: null
  }
];

const mockLocations = [
  { id: 'loc-1', name: 'Main Arena', event_id: 'event-123' },
  { id: 'loc-2', name: 'Outdoor Ring', event_id: 'event-123' },
  { id: 'loc-3', name: 'Cross Country Course', event_id: 'event-123' }
];

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <div>{children}</div>
);

describe('PublicBookings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUsePublicEntries.mockReturnValue({
      data: mockEntries,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    });

    mockUseLocations.mockReturnValue({
      data: mockLocations,
      isLoading: false,
      error: null
    });
  });

  describe('Rendering', () => {
    it('renders the component with event title', () => {
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      expect(screen.getByText('All Entries - Test Event')).toBeInTheDocument();
    });

    it('shows loading state when data is loading', () => {
      mockUsePublicEntries.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
        refetch: vi.fn()
      });

      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      expect(screen.getByText('Loading entries data...')).toBeInTheDocument();
    });

    it('displays all entries when no filters are applied', () => {
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.getByText('Thunder')).toBeInTheDocument();
      expect(screen.getByText('Storm')).toBeInTheDocument();
      expect(screen.getByText('Lightning')).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('filters entries by rider name', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      
      await user.clear(searchInput);
      await user.type(searchInput, 'John');

      // Should show both John Doe and Alice Johnson (substring match)
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.getByText('Thunder')).toBeInTheDocument();
      expect(screen.getByText('Lightning')).toBeInTheDocument();
      // Should not show Jane Smith
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });

    it('filters entries by horse name', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      
      await user.clear(searchInput);
      await user.type(searchInput, 'Storm');

      // Should show only Jane Smith's entry (horse: Storm)
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Storm')).toBeInTheDocument();
      
      // Should not show other entries
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
      expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
      
      // Should show correct count
      expect(screen.getByText('Showing 1 entry matching "Storm"')).toBeInTheDocument();
    });

    it('filters entries by partial rider name', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      
      await user.clear(searchInput);
      await user.type(searchInput, 'Jane');

      // Should show only Jane Smith's entry
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Storm')).toBeInTheDocument();
      
      // Should not show other entries
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
      expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
    });

    it('filters entries by partial horse name', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      
      await user.clear(searchInput);
      await user.type(searchInput, 'Light');

      // Should show only Alice Johnson's entry (horse: Lightning)
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.getByText('Lightning')).toBeInTheDocument();
      
      // Should not show other entries
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });

    it('is case insensitive', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      
      await user.clear(searchInput);
      await user.type(searchInput, 'jane');

      // Should show Jane Smith's entry even with lowercase search
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Storm')).toBeInTheDocument();
    });

    it('shows all entries when search is cleared', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      
      // First search for something
      await user.clear(searchInput);
      await user.type(searchInput, 'John');
      
      // Verify only John is shown
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      
      // Clear the search
      await user.clear(searchInput);
      
      // Verify all entries are shown again
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
    });

    it('shows no results message when no matches found', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      
      await user.clear(searchInput);
      await user.type(searchInput, 'Nonexistent');

      // Should show no results message
      expect(screen.getByText('No entries found matching your search criteria.')).toBeInTheDocument();
      
      // Should not show any entries
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
    });

    it('filters entries by unique rider name', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      
      await user.clear(searchInput);
      await user.type(searchInput, 'Alice');

      // Should show only Alice Johnson's entry
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.getByText('Lightning')).toBeInTheDocument();
      // Should not show other entries
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      expect(screen.queryByText('Thunder')).not.toBeInTheDocument();
      expect(screen.queryByText('Storm')).not.toBeInTheDocument();
    });

    it('combines search with filters', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      // Search for John
      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      await user.clear(searchInput);
      await user.type(searchInput, 'John');

      // Filter by Main Arena
      const locationSelect = screen.getByTestId('location-filter');
      await user.selectOptions(locationSelect, 'Main Arena');

      // Should show only John Doe from Main Arena
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Thunder')).toBeInTheDocument();
      expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });
  });

  describe('Filter Functionality', () => {
    it('filters entries by location', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const locationSelect = screen.getByTestId('location-filter');
      await user.selectOptions(locationSelect, 'Main Arena');

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Thunder')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      expect(screen.queryByText('Lightning')).not.toBeInTheDocument();
    });

    it('filters entries by level', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const levelSelect = screen.getByTestId('level-filter');
      await user.selectOptions(levelSelect, 'Intro');

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Thunder')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      expect(screen.queryByText('Lightning')).not.toBeInTheDocument();
    });

    it('filters entries by activity type', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      const activitySelect = screen.getByTestId('activity-filter');
      await user.selectOptions(activitySelect, 'dressage');

      // Should show only John Doe (dressage)
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Thunder')).toBeInTheDocument();
      // Should not show Jane Smith (show_jumping) or Alice Johnson (cross_country)
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
    });
  });

  describe('Reset Filters', () => {
    it('resets all filters when reset button is clicked', async () => {
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      // Apply some filters
      const searchInput = screen.getByPlaceholderText('Search by rider or horse name...');
      await user.clear(searchInput);
      await user.type(searchInput, 'John');

      // Verify filter is applied - should show only John entries
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument(); // Contains "John"
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();

      // Click reset button
      const resetButton = screen.getByText('Reset Filters');
      await user.click(resetButton);

      // Verify all entries are shown again
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      
      // Verify search is cleared
      expect(searchInput).toHaveValue('');
    });
  });

  describe('Navigation', () => {
    it('calls onBack when back button is clicked', async () => {
      const mockOnBack = vi.fn();
      const user = userEvent.setup();
      render(<PublicBookings event={mockEvent} onBack={mockOnBack} />, { wrapper });

      const backButton = screen.getByText('Back to Event');
      await user.click(backButton);

      expect(mockOnBack).toHaveBeenCalledTimes(1);
    });
  });

  describe('Empty State', () => {
    it('shows appropriate message when no entries exist', () => {
      mockUsePublicEntries.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
        refetch: vi.fn()
      });

      render(<PublicBookings event={mockEvent} onBack={vi.fn()} />, { wrapper });

      expect(screen.getByText('No entries found for this event.')).toBeInTheDocument();
    });
  });
}); 