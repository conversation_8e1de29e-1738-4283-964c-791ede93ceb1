import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { AuthWrapper } from './AuthWrapper';
import { useSession } from '@supabase/auth-helpers-react';
import { useUserRoles, useIsSuperAdmin } from '@/hooks/useUserRoles';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

// Mock all hooks
vi.mock('@supabase/auth-helpers-react');
vi.mock('@/hooks/useUserRoles');
vi.mock('@/hooks/use-toast');
vi.mock('@/integrations/supabase/client');

// Mock the Tabs component to avoid pointer-events issues
vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, defaultValue }: React.PropsWithChildren<{ defaultValue: string }>) => (
    <div data-testid="tabs" data-default-value={defaultValue}>
      {children}
    </div>
  ),
  TabsList: ({ children }: React.PropsWithChildren) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children, value }: React.PropsWithChildren<{ value: string }>) => (
    <button data-testid={`tab-${value}`} data-value={value}>
      {children}
    </button>
  ),
  TabsContent: ({ children, value }: React.PropsWithChildren<{ value: string }>) => (
    <div data-testid={`tab-content-${value}`} data-value={value}>
      {children}
    </div>
  ),
}));

const mockUseSession = vi.mocked(useSession);
const mockUseUserRoles = vi.mocked(useUserRoles);
const mockUseIsSuperAdmin = vi.mocked(useIsSuperAdmin);
const mockUseToast = vi.mocked(useToast);
const mockSupabase = vi.mocked(supabase);

describe('AuthWrapper', () => {
  const mockToast = vi.fn();
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    user_metadata: { full_name: 'Test User' }
  };

  const mockUserRoles = [
    { role: 'organizer', isLoading: false },
    { role: 'admin', isLoading: false }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mocks
    mockUseSession.mockReturnValue({
      user: mockUser,
      access_token: 'mock-token',
      refresh_token: 'mock-refresh-token',
      expires_in: 3600,
      expires_at: Date.now() + 3600000,
      token_type: 'bearer'
    });
    mockUseUserRoles.mockReturnValue({
      data: mockUserRoles,
      isLoading: false,
      error: null
    });
    mockUseIsSuperAdmin.mockReturnValue({
      data: false,
      isLoading: false,
      error: null
    });
    mockUseToast.mockReturnValue({ toast: mockToast });
    
    // Setup Supabase auth mock
    mockSupabase.auth = {
      signInWithPassword: vi.fn(),
      signUp: vi.fn()
    } as any;
  });

  const renderAuthWrapper = (props = {}) => {
    const defaultProps = {
      children: <div data-testid="protected-content">Protected Content</div>,
      ...props
    };
    return render(<AuthWrapper {...defaultProps} />);
  };

  describe('No Authentication Required', () => {
    it('renders children when no authentication is required', () => {
      renderAuthWrapper({ requireAuth: false });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('renders children when user is authenticated but no auth required', () => {
      renderAuthWrapper({ requireAuth: false });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });
  });

  describe('Authentication Required - Loading States', () => {
    it('shows loading spinner when session is loading (null)', () => {
      mockUseSession.mockReturnValue(null); // Session is null while loading
      
      renderAuthWrapper({ requireAuth: true });

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('shows loading spinner when roles are loading', () => {
      mockUseUserRoles.mockReturnValue({
        data: null,
        isLoading: true,
        error: null
      });
      
      renderAuthWrapper({ requireAuth: true, requiredRoles: ['admin'] });

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  describe('Authentication Required - Not Authenticated', () => {
    beforeEach(() => {
      // Mock session with no user (authenticated but no user)
      mockUseSession.mockReturnValue({
        user: null,
        access_token: null,
        refresh_token: null,
        expires_in: 0,
        expires_at: 0,
        token_type: 'bearer'
      });
    });

    it('shows authentication form when user is not authenticated', () => {
      renderAuthWrapper({ requireAuth: true });

      expect(screen.getByText('Authentication Required')).toBeInTheDocument();
      expect(screen.getByTestId('tabs')).toBeInTheDocument();
      expect(screen.getByTestId('tab-signin')).toBeInTheDocument();
      expect(screen.getByTestId('tab-signup')).toBeInTheDocument();
    });

    it('shows sign in form by default', () => {
      renderAuthWrapper({ requireAuth: true });

      expect(screen.getByTestId('tab-content-signin')).toBeInTheDocument();
      expect(screen.getByLabelText('Email')).toBeInTheDocument();
      expect(screen.getByLabelText('Password')).toBeInTheDocument();
      expect(screen.getByTestId('signin-submit')).toBeInTheDocument();
    });

    it('allows switching to sign up form', async () => {
      const user = userEvent.setup();
      renderAuthWrapper({ requireAuth: true });

      const signUpTab = screen.getByTestId('tab-signup');
      await user.click(signUpTab);

      expect(screen.getByTestId('tab-content-signup')).toBeInTheDocument();
      expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Email')).toBeInTheDocument();
      expect(screen.getByLabelText('Password')).toBeInTheDocument();
      expect(screen.getByTestId('signup-submit')).toBeInTheDocument();
    });
  });

  describe('Sign In Functionality', () => {
    beforeEach(() => {
      // Mock session with no user
      mockUseSession.mockReturnValue({
        user: null,
        access_token: null,
        refresh_token: null,
        expires_in: 0,
        expires_at: 0,
        token_type: 'bearer'
      });
    });

    it('handles successful sign in', async () => {
      const user = userEvent.setup();
      const mockSignIn = vi.fn().mockResolvedValue({ error: null });
      mockSupabase.auth.signInWithPassword = mockSignIn;

      renderAuthWrapper({ requireAuth: true });

      const signInContent = screen.getByTestId('tab-content-signin');
      const emailInput = within(signInContent).getByLabelText('Email');
      const passwordInput = within(signInContent).getByLabelText('Password');
      const signInButton = within(signInContent).getByTestId('signin-submit');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(signInButton);

      expect(mockSignIn).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Success',
        description: 'Signed in successfully'
      });
    });

    it('handles sign in error', async () => {
      const user = userEvent.setup();
      const mockSignIn = vi.fn().mockResolvedValue({ 
        error: { message: 'Invalid credentials' } 
      });
      mockSupabase.auth.signInWithPassword = mockSignIn;

      renderAuthWrapper({ requireAuth: true });

      const signInContent = screen.getByTestId('tab-content-signin');
      const emailInput = within(signInContent).getByLabelText('Email');
      const passwordInput = within(signInContent).getByLabelText('Password');
      const signInButton = within(signInContent).getByTestId('signin-submit');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(signInButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error',
        description: 'Invalid credentials',
        variant: 'destructive'
      });
    });

    it('shows loading state during sign in', async () => {
      const user = userEvent.setup();
      const mockSignIn = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ error: null }), 100))
      );
      mockSupabase.auth.signInWithPassword = mockSignIn;

      renderAuthWrapper({ requireAuth: true });

      const signInContent = screen.getByTestId('tab-content-signin');
      const emailInput = within(signInContent).getByLabelText('Email');
      const passwordInput = within(signInContent).getByLabelText('Password');
      const signInButton = within(signInContent).getByTestId('signin-submit');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(signInButton);

      expect(within(signInContent).getByTestId('signin-submit')).toBeDisabled();
    });
  });

  describe('Sign Up Functionality', () => {
    beforeEach(() => {
      // Mock session with no user
      mockUseSession.mockReturnValue({
        user: null,
        access_token: null,
        refresh_token: null,
        expires_in: 0,
        expires_at: 0,
        token_type: 'bearer'
      });
    });

    it('handles successful sign up', async () => {
      const user = userEvent.setup();
      const mockSignUp = vi.fn().mockResolvedValue({ error: null });
      mockSupabase.auth.signUp = mockSignUp;

      renderAuthWrapper({ requireAuth: true });

      // Switch to sign up tab
      const signUpTab = screen.getByTestId('tab-signup');
      await user.click(signUpTab);

      const signUpContent = screen.getByTestId('tab-content-signup');
      const fullNameInput = within(signUpContent).getByLabelText('Full Name');
      const emailInput = within(signUpContent).getByPlaceholderText('Email');
      const passwordInput = within(signUpContent).getByPlaceholderText('Password');
      const signUpButton = within(signUpContent).getByTestId('signup-submit');

      await user.type(fullNameInput, 'John Doe');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(signUpButton);

      expect(mockSignUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          data: {
            full_name: 'John Doe'
          }
        }
      });
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Success',
        description: 'Account created successfully! Please check your email to verify your account.'
      });
    });

    it('handles sign up error', async () => {
      const user = userEvent.setup();
      const mockSignUp = vi.fn().mockResolvedValue({ 
        error: { message: 'Email already exists' } 
      });
      mockSupabase.auth.signUp = mockSignUp;

      renderAuthWrapper({ requireAuth: true });

      // Switch to sign up tab
      const signUpTab = screen.getByTestId('tab-signup');
      await user.click(signUpTab);

      const signUpContent = screen.getByTestId('tab-content-signup');
      const emailInput = within(signUpContent).getByPlaceholderText('Email');
      const passwordInput = within(signUpContent).getByPlaceholderText('Password');
      const signUpButton = within(signUpContent).getByTestId('signup-submit');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(signUpButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error',
        description: 'Email already exists',
        variant: 'destructive'
      });
    });
  });

  describe('Role-Based Access Control', () => {
    beforeEach(() => {
      // Always mock an authenticated user for these tests
      mockUseSession.mockReturnValue({
        user: mockUser,
        access_token: 'mock-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
        expires_at: Date.now() + 3600000,
        token_type: 'bearer'
      });
    });

    it('allows access when user has required role', () => {
      mockUseUserRoles.mockReturnValue({
        data: [{ role: 'admin', isLoading: false }],
        isLoading: false,
        error: null
      });

      renderAuthWrapper({ requireAuth: true, requiredRoles: ['admin'] });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('allows access when user has multiple required roles', () => {
      mockUseUserRoles.mockReturnValue({
        data: [
          { role: 'organizer', isLoading: false },
          { role: 'admin', isLoading: false }
        ],
        isLoading: false,
        error: null
      });

      renderAuthWrapper({ requireAuth: true, requiredRoles: ['admin', 'organizer'] });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('allows access when user is super admin', () => {
      mockUseIsSuperAdmin.mockReturnValue({
        data: true,
        isLoading: false,
        error: null
      });
      mockUseUserRoles.mockReturnValue({
        data: [{ role: 'user', isLoading: false }],
        isLoading: false,
        error: null
      });

      renderAuthWrapper({ requireAuth: true, requiredRoles: ['admin'] });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('denies access when user lacks required role', () => {
      mockUseUserRoles.mockReturnValue({
        data: [{ role: 'user', isLoading: false }],
        isLoading: false,
        error: null
      });

      renderAuthWrapper({ requireAuth: true, requiredRoles: ['admin'] });

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.getByText(/You don't have permission to access this page/)).toBeInTheDocument();
      expect(screen.getByText(/Required roles: admin/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Go Back' })).toBeInTheDocument();
    });

    it('denies access when user has no roles', () => {
      mockUseUserRoles.mockReturnValue({
        data: [],
        isLoading: false,
        error: null
      });

      renderAuthWrapper({ requireAuth: true, requiredRoles: ['admin'] });

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });

    it('allows access when no specific roles are required', () => {
      renderAuthWrapper({ requireAuth: true, requiredRoles: [] });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('allows access when requiredRoles is not specified', () => {
      renderAuthWrapper({ requireAuth: true });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('denies access to admin dashboard for user with only "user" role', () => {
      mockUseUserRoles.mockReturnValue({
        data: [{ role: 'user', isLoading: false }],
        isLoading: false,
        error: null
      });
      renderAuthWrapper({ requireAuth: true, requiredRoles: ['admin'] });
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });

    it('denies access to organizer dashboard for user with only "user" role', () => {
      mockUseUserRoles.mockReturnValue({
        data: [{ role: 'user', isLoading: false }],
        isLoading: false,
        error: null
      });
      renderAuthWrapper({ requireAuth: true, requiredRoles: ['organizer'] });
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });

    it('shows authentication form for non-authenticated user accessing admin dashboard', () => {
      mockUseSession.mockReturnValue({
        user: null,
        access_token: null,
        refresh_token: null,
        expires_in: 0,
        expires_at: 0,
        token_type: 'bearer'
      });
      renderAuthWrapper({ requireAuth: true, requiredRoles: ['admin'] });
      expect(screen.getByText('Authentication Required')).toBeInTheDocument();
    });

    it('shows authentication form for non-authenticated user accessing organizer dashboard', () => {
      mockUseSession.mockReturnValue({
        user: null,
        access_token: null,
        refresh_token: null,
        expires_in: 0,
        expires_at: 0,
        token_type: 'bearer'
      });
      renderAuthWrapper({ requireAuth: true, requiredRoles: ['organizer'] });
      expect(screen.getByText('Authentication Required')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      // Mock session with no user
      mockUseSession.mockReturnValue({
        user: null,
        access_token: null,
        refresh_token: null,
        expires_in: 0,
        expires_at: 0,
        token_type: 'bearer'
      });
    });

    it('requires email and password for sign in', async () => {
      const user = userEvent.setup();
      renderAuthWrapper({ requireAuth: true });

      // Wait for the auth form to be visible
      await waitFor(() => {
        expect(screen.getByText('Authentication Required')).toBeInTheDocument();
      });

      const signInContent = screen.getByTestId('tab-content-signin');
      const signInButton = within(signInContent).getByTestId('signin-submit');
      await user.click(signInButton);

      // Form should not submit without required fields
      expect(mockSupabase.auth.signInWithPassword).not.toHaveBeenCalled();
    });

    it('requires email and password for sign up', async () => {
      const user = userEvent.setup();
      renderAuthWrapper({ requireAuth: true });

      // Wait for the auth form to be visible
      await waitFor(() => {
        expect(screen.getByText('Authentication Required')).toBeInTheDocument();
      });

      // Switch to sign up tab
      const signUpTab = screen.getByTestId('tab-signup');
      await user.click(signUpTab);

      const signUpContent = screen.getByTestId('tab-content-signup');
      const signUpButton = within(signUpContent).getByTestId('signup-submit');
      await user.click(signUpButton);

      // Form should not submit without required fields
      expect(mockSupabase.auth.signUp).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      // Mock session with no user
      mockUseSession.mockReturnValue({
        user: null,
        access_token: null,
        refresh_token: null,
        expires_in: 0,
        expires_at: 0,
        token_type: 'bearer'
      });
    });

    it('handles unexpected errors during sign in', async () => {
      const user = userEvent.setup();
      const mockSignIn = vi.fn().mockRejectedValue(new Error('Network error'));
      mockSupabase.auth.signInWithPassword = mockSignIn;

      renderAuthWrapper({ requireAuth: true });

      // Wait for the auth form to be visible
      await waitFor(() => {
        expect(screen.getByText('Authentication Required')).toBeInTheDocument();
      });

      const signInContent = screen.getByTestId('tab-content-signin');
      const emailInput = within(signInContent).getByLabelText('Email');
      const passwordInput = within(signInContent).getByLabelText('Password');
      const signInButton = within(signInContent).getByTestId('signin-submit');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(signInButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error',
        description: 'Network error',
        variant: 'destructive'
      });
    });

    it('handles unexpected errors during sign up', async () => {
      const user = userEvent.setup();
      const mockSignUp = vi.fn().mockRejectedValue(new Error('Network error'));
      mockSupabase.auth.signUp = mockSignUp;

      renderAuthWrapper({ requireAuth: true });

      // Wait for the auth form to be visible
      await waitFor(() => {
        expect(screen.getByText('Authentication Required')).toBeInTheDocument();
      });

      // Switch to sign up tab
      const signUpTab = screen.getByTestId('tab-signup');
      await user.click(signUpTab);

      const signUpContent = screen.getByTestId('tab-content-signup');
      const emailInput = within(signUpContent).getByPlaceholderText('Email');
      const passwordInput = within(signUpContent).getByPlaceholderText('Password');
      const signUpButton = within(signUpContent).getByTestId('signup-submit');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(signUpButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error',
        description: 'Network error',
        variant: 'destructive'
      });
    });
  });
}); 