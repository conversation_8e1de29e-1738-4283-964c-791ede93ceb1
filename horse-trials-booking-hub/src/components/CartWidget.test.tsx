import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CartWidget } from './CartWidget';
import { useSlotReservations, getUserSessionId, useReleaseReservation } from '@/hooks/useSlotReservations';
import { useIsMobile } from '@/hooks/use-mobile';

// Mock the hooks
vi.mock('@/hooks/useSlotReservations');
vi.mock('@/hooks/use-mobile');

const mockUseSlotReservations = vi.mocked(useSlotReservations);
const mockUseReleaseReservation = vi.mocked(useReleaseReservation);
const mockUseIsMobile = vi.mocked(useIsMobile);
const mockGetUserSessionId = vi.mocked(getUserSessionId);

// Mock the UI components
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: React.PropsWithChildren<{ open: boolean }>) => 
    open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: React.PropsWithChildren) => <div data-testid="dialog-content">{children}</div>,
  DialogHeader: ({ children }: React.PropsWithChildren) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: React.PropsWithChildren) => <div data-testid="dialog-title">{children}</div>,
}));

vi.mock('@/components/ui/sheet', () => ({
  Sheet: ({ children, open }: React.PropsWithChildren<{ open: boolean }>) => 
    open ? <div data-testid="sheet">{children}</div> : null,
  SheetContent: ({ children }: React.PropsWithChildren) => <div data-testid="sheet-content">{children}</div>,
  SheetHeader: ({ children }: React.PropsWithChildren) => <div data-testid="sheet-header">{children}</div>,
  SheetTitle: ({ children }: React.PropsWithChildren) => <div data-testid="sheet-title">{children}</div>,
}));

describe('CartWidget Component', () => {
  const mockOnProceedToBooking = vi.fn();
  const mockOnClose = vi.fn();
  const mockReleaseReservation = vi.fn();

  const mockReservations = [
    {
      id: 'reservation1',
      time_slot_id: 'slot1',
      activity_type: 'dressage',
      time: '9:00 AM',
      location_name: 'Main Arena',
      level: 'Beginner',
      expires_at: new Date(Date.now() + 300000).toISOString(), // 5 minutes from now
    },
    {
      id: 'reservation2',
      time_slot_id: 'slot2',
      activity_type: 'show_jumping',
      time: '10:00 AM',
      location_name: 'Jumper Ring',
      level: 'Intermediate',
      expires_at: new Date(Date.now() + 600000).toISOString(), // 10 minutes from now
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockGetUserSessionId.mockReturnValue('test-session-id');
    mockUseIsMobile.mockReturnValue(false);
    mockUseReleaseReservation.mockReturnValue({
      mutateAsync: mockReleaseReservation,
      isPending: false,
      error: null,
    });
  });

  const renderCartWidget = (props = {}) => {
    return render(
      <CartWidget
        onProceedToBooking={mockOnProceedToBooking}
        onClose={mockOnClose}
        eventId="test-event"
        {...props}
      />
    );
  };

  describe('Empty Cart State', () => {
    beforeEach(() => {
      mockUseSlotReservations.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });
    });

    it('displays empty cart message when no reservations', () => {
      renderCartWidget({ isOpen: true });

      expect(screen.getByText('Your cart is empty')).toBeInTheDocument();
      expect(screen.getByText('Select time slots from the schedule to add them to your cart')).toBeInTheDocument();
      expect(screen.getByText('Browse Time Slots')).toBeInTheDocument();
    });

    it('calls onClose when browse button is clicked', async () => {
      const user = userEvent.setup();
      // Simulate not authenticated and not showing sign-up form
      const { container } = renderCartWidget({ isOpen: true });

      // Try getByTestId, fallback to querySelector
      let browseButton;
      try {
        browseButton = screen.getByTestId('browse-time-slots');
      } catch {
        browseButton = container.querySelector('[data-testid="browse-time-slots"]');
      }
      expect(browseButton).toBeTruthy();
      await user.click(browseButton!);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('Reservations Display', () => {
    beforeEach(() => {
      mockUseSlotReservations.mockReturnValue({
        data: mockReservations,
        isLoading: false,
        error: null,
      });
    });

    it('displays warning message about reservations', () => {
      renderCartWidget({ isOpen: true });

      expect(screen.getByText('Your spots are not reserved until checkout is complete')).toBeInTheDocument();
      expect(screen.getByText('Time slots are temporarily held. Complete your booking to secure your spots.')).toBeInTheDocument();
    });

    it('displays all reservations with correct information', () => {
      const { container } = renderCartWidget({ isOpen: true });

      expect(screen.getByText('Main Arena')).toBeInTheDocument();
      expect(screen.getByText('Jumper Ring')).toBeInTheDocument();
      expect(screen.getByText('9:00 AM')).toBeInTheDocument();
      expect(screen.getByText('10:00 AM')).toBeInTheDocument();

      // Use function matcher for lines containing Beginner/Intermediate
      expect(screen.getByText((content) => content.includes('Beginner'))).toBeInTheDocument();
      expect(screen.getByText((content) => content.includes('Intermediate'))).toBeInTheDocument();

      // Use regex for activity type lines
      expect(screen.getByText((content) => /dressage/.test(content))).toBeInTheDocument();
      expect(screen.getByText((content) => /show jumping/.test(content))).toBeInTheDocument();
    });

    it('displays time remaining for each reservation', async () => {
      renderCartWidget({ isOpen: true });

      // Wait for the time remaining to be calculated
      await waitFor(() => {
        expect(screen.getByTestId('expires-in-reservation1')).toBeInTheDocument();
        expect(screen.getByTestId('expires-in-reservation2')).toBeInTheDocument();
      });
    });

    it('shows proceed to booking button with correct count', () => {
      renderCartWidget({ isOpen: true });

      expect(screen.getByTestId('proceed-to-booking')).toBeInTheDocument();
    });

    it('shows singular "slot" when only one reservation', () => {
      mockUseSlotReservations.mockReturnValue({
        data: [mockReservations[0]],
        isLoading: false,
        error: null,
      });

      renderCartWidget({ isOpen: true });

      expect(screen.getByTestId('proceed-to-booking')).toBeInTheDocument();
    });
  });

  describe('Remove from Cart', () => {
    beforeEach(() => {
      mockUseSlotReservations.mockReturnValue({
        data: mockReservations,
        isLoading: false,
        error: null,
      });
    });

    it('calls releaseReservation when remove button is clicked', async () => {
      const user = userEvent.setup();
      renderCartWidget({ isOpen: true });

      const removeButton = screen.getByTestId('remove-from-cart-reservation1');
      await user.click(removeButton);

      expect(mockReleaseReservation).toHaveBeenCalledWith({ reservationId: 'reservation1' });
    });

    it('handles error when removing reservation fails', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockReleaseReservation.mockRejectedValueOnce(new Error('Network error'));

      const user = userEvent.setup();
      renderCartWidget({ isOpen: true });

      const removeButton = screen.getByTestId('remove-from-cart-reservation1');
      await user.click(removeButton);

      expect(consoleSpy).toHaveBeenCalledWith('Error removing reservation:', expect.any(Error));
      consoleSpy.mockRestore();
    });
  });

  describe('Proceed to Booking', () => {
    beforeEach(() => {
      mockUseSlotReservations.mockReturnValue({
        data: mockReservations,
        isLoading: false,
        error: null,
      });
    });

    it('calls onProceedToBooking with enhanced reservation data', async () => {
      const user = userEvent.setup();
      renderCartWidget({ isOpen: true });

      const proceedButton = screen.getByTestId('proceed-to-booking');
      await user.click(proceedButton);

      expect(mockOnProceedToBooking).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'reservation1',
            activity_type: 'dressage',
            time: '9:00 AM',
            location_name: 'Main Arena',
            timeRemaining: expect.any(Number),
          }),
          expect.objectContaining({
            id: 'reservation2',
            activity_type: 'show_jumping',
            time: '10:00 AM',
            location_name: 'Jumper Ring',
            timeRemaining: expect.any(Number),
          }),
        ])
      );
    });

    it('calls onClose after proceeding to booking', async () => {
      const user = userEvent.setup();
      renderCartWidget({ isOpen: true });

      const proceedButton = screen.getByTestId('proceed-to-booking');
      await user.click(proceedButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('Loading State', () => {
    it('shows loading state when reservations are loading', () => {
      mockUseSlotReservations.mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
      });

      renderCartWidget({ isOpen: true });

      // The component should still render the empty cart state even when loading
      expect(screen.getByText('Your cart is empty')).toBeInTheDocument();
    });
  });

  describe('Error State', () => {
    it('handles error state gracefully', () => {
      mockUseSlotReservations.mockReturnValue({
        data: [],
        isLoading: false,
        error: new Error('Failed to load reservations'),
      });

      renderCartWidget({ isOpen: true });

      // Should still show empty cart state even with error
      expect(screen.getByText('Your cart is empty')).toBeInTheDocument();
    });
  });

  describe('Mobile vs Desktop Rendering', () => {
    it('renders as dialog on desktop', () => {
      mockUseIsMobile.mockReturnValue(false);
      mockUseSlotReservations.mockReturnValue({
        data: mockReservations,
        isLoading: false,
        error: null,
      });

      renderCartWidget({ isOpen: true });

      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.queryByTestId('sheet')).not.toBeInTheDocument();
    });

    it('renders as sheet on mobile', () => {
      mockUseIsMobile.mockReturnValue(true);
      mockUseSlotReservations.mockReturnValue({
        data: mockReservations,
        isLoading: false,
        error: null,
      });

      renderCartWidget({ isOpen: true });

      expect(screen.getByTestId('sheet')).toBeInTheDocument();
      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });
  });

  describe('Time Remaining Calculation', () => {
    it('formats time remaining correctly', () => {
      mockUseSlotReservations.mockReturnValue({
        data: [{
          id: 'reservation1',
          time_slot_id: 'slot1',
          activity_type: 'dressage',
          time: '9:00 AM',
          location_name: 'Main Arena',
          expires_at: new Date(Date.now() + 125000).toISOString(), // 2 minutes 5 seconds
        }],
        isLoading: false,
        error: null,
      });

      renderCartWidget({ isOpen: true });

      // Should show time in MM:SS format
      const expiresIn = screen.getByTestId('expires-in-reservation1');
      expect(expiresIn.textContent).toMatch(/Expires in: \d+:\d{2}/);
    });

    it('shows 0:00 when reservation has expired', () => {
      mockUseSlotReservations.mockReturnValue({
        data: [{
          id: 'reservation1',
          time_slot_id: 'slot1',
          activity_type: 'dressage',
          time: '9:00 AM',
          location_name: 'Main Arena',
          expires_at: new Date(Date.now() - 1000).toISOString(), // 1 second ago
        }],
        isLoading: false,
        error: null,
      });

      renderCartWidget({ isOpen: true });

      const expiresIn = screen.getByTestId('expires-in-reservation1');
      expect(expiresIn.textContent).toContain('Expires in: 0:00');
    });
  });

  describe('Edge Cases', () => {
    it('handles reservations with missing optional fields', () => {
      mockUseSlotReservations.mockReturnValue({
        data: [{
          id: 'reservation1',
          time_slot_id: 'slot1',
          expires_at: new Date(Date.now() + 300000).toISOString(),
        }],
        isLoading: false,
        error: null,
      });

      renderCartWidget({ isOpen: true });

      expect(screen.getByText('Time Slot Reserved')).toBeInTheDocument();
      expect(screen.getByText('Booking')).toBeInTheDocument();
      expect(screen.getByText(/Slot ID: slot1/)).toBeInTheDocument();
    });

    it('handles activity type with underscores', () => {
      mockUseSlotReservations.mockReturnValue({
        data: [{
          id: 'reservation1',
          time_slot_id: 'slot1',
          activity_type: 'show_jumping',
          time: '9:00 AM',
          location_name: 'Main Arena',
          expires_at: new Date(Date.now() + 300000).toISOString(),
        }],
        isLoading: false,
        error: null,
      });

      renderCartWidget({ isOpen: true });

      expect(screen.getByText('show jumping')).toBeInTheDocument();
    });
  });
}); 