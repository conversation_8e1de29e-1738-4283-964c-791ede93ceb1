import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, MapPin, Clock, UserPlus } from 'lucide-react';
import { useEvents, Event } from '@/hooks/useEvents';
import { useSession } from '@supabase/auth-helpers-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { formatDateForDisplay } from '@/utils/dateUtils';
import { getErrorMessage } from '@/types/errors';

interface EventListProps {
  onEventSelect: (event: Event) => void;
}

export const EventList: React.FC<EventListProps> = ({ onEventSelect }) => {
  const session = useSession();
  const user = session?.user;
  const { toast } = useToast();
  const { data: events, isLoading, error, refetch } = useEvents();
  const [showSignUp, setShowSignUp] = React.useState(false);
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [fullName, setFullName] = React.useState('');
  const [isSigningUp, setIsSigningUp] = React.useState(false);

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSigningUp(true);
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });
      
      if (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: "Account created successfully! Please check your email to verify your account.",
        });
        setShowSignUp(false);
        setEmail('');
        setPassword('');
        setFullName('');
      }
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: getErrorMessage(error),
        variant: "destructive",
      });
    } finally {
      setIsSigningUp(false);
    }
  };

  if (isLoading) {
    return (
      <div className="grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="card-base animate-pulse">
            <div className="h-6 bg-cream-light rounded mb-2"></div>
            <div className="h-4 bg-cream-light rounded w-1/2 mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-cream-light rounded"></div>
              <div className="h-4 bg-cream-light rounded"></div>
              <div className="h-4 bg-cream-light rounded w-3/4"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading events: {error.message}</p>
        <p className="text-sm text-text-secondary mt-2">Please try refreshing the page or contact support.</p>
      </div>
    );
  }

  if (!events || events.length === 0) {
    return (
      <div className="text-center">
        <h3 className="font-playfair text-xl text-primary mb-2 font-semibold tracking-tight">
          No Events Available
        </h3>
        <p className="text-text-secondary mb-6">
          Check back later for upcoming events, or use the test data button above to populate sample events.
        </p>

        {!user && (
          <div className="max-w-md mx-auto">
            {!showSignUp ? (
              <Button
                onClick={() => setShowSignUp(true)}
                className="btn-primary"
              >
                <UserPlus className="w-4 h-4 mr-2" />
                Sign Up for Updates
              </Button>
            ) : (
              <Card className="card-base">
                <CardHeader>
                  <CardTitle className="text-lg font-playfair">Create Account</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSignUp} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-1 text-text-primary">Full Name</label>
                      <input
                        type="text"
                        value={fullName}
                        onChange={(e) => setFullName(e.target.value)}
                        className="w-full px-3 py-2 border border-cream-light rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-gold"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 text-text-primary">Email</label>
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="w-full px-3 py-2 border border-cream-light rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-gold"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 text-text-primary">Password</label>
                      <input
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                        className="w-full px-3 py-2 border border-cream-light rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-gold"
                        placeholder="Password"
                      />
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        type="submit"
                        disabled={isSigningUp}
                        className="flex-1 btn-primary"
                      >
                        {isSigningUp ? 'Creating...' : 'Sign Up'}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setShowSignUp(false)}
                        className="flex-1 border-cream-light text-text-primary hover:bg-cream-light"
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
      {events.map((event) => (
        <div
          key={event.id}
          tabIndex={0}
          className="card-base card-hover flex flex-col items-start cursor-pointer"
          onClick={() => onEventSelect(event)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              onEventSelect(event);
            }
          }}
          aria-label={`${event.name}, ${formatDateForDisplay(event.start_date)}`}
        >
          <span className="text-sm font-semibold text-primary mb-2">
            {formatDateForDisplay(event.start_date)}
          </span>
          <h3 className="font-playfair text-xl text-primary mb-2 font-semibold tracking-tight">
            {event.name}
          </h3>
          <p className="text-text-secondary mb-4">
            {event.is_active 
              ? 'Perfect for riders looking to gain experience and feedback in a supportive, educational environment.'
              : 'This event is currently inactive. Check back for updates.'
            }
          </p>
          <button 
            className="mt-auto px-4 py-2 rounded-full bg-primary text-cream font-semibold hover:bg-primary-light focus:bg-primary-dark transition-colors shadow focus:outline-none focus:ring-2 focus:ring-gold"
            aria-label={`View Details for ${event.name}`}
          >
            View Details
          </button>
        </div>
      ))}
    </div>
  );
};
