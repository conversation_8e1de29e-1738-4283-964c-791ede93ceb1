<!DOCTYPE html>
<html>
<head>
    <title>Test Stripe Functions</title>
</head>
<body>
    <h1>Test Stripe Functions</h1>
    
    <button onclick="testCreateCheckout()">Test Create Stripe Checkout</button>
    <button onclick="testStripeWebhook()">Test Stripe Webhook</button>
    
    <div id="results"></div>

    <script>
        const SUPABASE_URL = 'https://tuocgtwpjvruwtfwzmiu.supabase.co';
        
        async function testCreateCheckout() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing create-stripe-checkout...';
            
            try {
                const response = await fetch(`${SUPABASE_URL}/functions/v1/create-stripe-checkout`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        bookingData: {
                            activityName: 'Test Activity',
                            locationName: 'Test Location',
                            timeSlot: 'Test Time',
                            price: 10.00,
                            payer_email: '<EMAIL>'
                        },
                        successUrl: 'https://example.com/success',
                        cancelUrl: 'https://example.com/cancel'
                    })
                });
                
                const data = await response.json();
                results.innerHTML = `<h3>Create Checkout Result:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                results.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testStripeWebhook() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing stripe-webhook...';
            
            try {
                const response = await fetch(`${SUPABASE_URL}/functions/v1/stripe-webhook`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        type: 'test',
                        data: { object: { id: 'test' } }
                    })
                });
                
                const data = await response.json();
                results.innerHTML = `<h3>Webhook Result:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                results.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html> 