-- Debug all time slots for 2025-06-25
SELECT 
  'Total time slots for 2025-06-25' as info,
  COUNT(*) as count
FROM time_slots 
WHERE DATE(start_time) = '2025-06-25';

-- Show time slots by location (including those without locations)
SELECT 
  COALESCE(l.name, 'NO LOCATION') as location_name,
  COALESCE(l.activity_type, 'UNKNOWN') as activity_type,
  ts.location_id,
  COUNT(ts.id) as time_slot_count,
  MIN(ts.start_time) as first_slot,
  MAX(ts.end_time) as last_slot
FROM time_slots ts
LEFT JOIN locations l ON ts.location_id = l.id
WHERE DATE(ts.start_time) = '2025-06-25'
GROUP BY l.id, l.name, l.activity_type, ts.location_id
ORDER BY l.name;

-- Show time slots that don't have a location
SELECT 
  'Time slots without location' as info,
  COUNT(*) as count
FROM time_slots ts
LEFT JOIN locations l ON ts.location_id = l.id
WHERE DATE(ts.start_time) = '2025-06-25' 
  AND l.id IS NULL;

-- Show sample time slots
SELECT 
  ts.id,
  ts.location_id,
  l.name as location_name,
  ts.start_time,
  ts.end_time,
  ts.level,
  ts.is_booked
FROM time_slots ts
LEFT JOIN locations l ON ts.location_id = l.id
WHERE DATE(ts.start_time) = '2025-06-25'
ORDER BY ts.start_time
LIMIT 10; 