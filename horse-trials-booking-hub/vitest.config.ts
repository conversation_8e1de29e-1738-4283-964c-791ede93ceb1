import { defineConfig, mergeConfig } from 'vitest/config';
import viteConfig from './vite.config';

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: ['./src/test/setup.ts'],
      include: [
        'src/**/*.test.ts',
        'src/**/*.test.tsx',
        'src/**/*.spec.ts',
        'src/**/*.spec.tsx',
      ],
      exclude: [
        'node_modules/**',
        'dist/**',
        'cypress/**',
      ],
      // Enable console logs during tests
      onConsoleLog: (log) => {
        console.log(log); // This will force logs to be displayed
        return undefined; // Allow the log to be printed normally as well
      },
      coverage: {
        exclude: [
          'src/components/ui/**', // Exclude shadcn/ui components
          'src/integrations/supabase/types.ts', // Exclude generated types
          'supabase/functions/**', // Exclude Supabase functions
          'src/test/**', // Exclude test utilities
          'src/hooks/useTestData.tsx', // Exclude test data hook
          'postcss.config.js',
          'tailwind.config.ts',
          'vite.config.ts',
          'vitest.config.ts',
        ],
      },
    },
  })
);
