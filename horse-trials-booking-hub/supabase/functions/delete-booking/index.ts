// supabase/functions/delete-booking/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Define a type for the request body for clarity
interface DeleteBookingRequestBody {
  bookingId: string;
}

// Define a type for the response for clarity
interface FunctionResponse {
  success?: boolean;
  bookingId?: string;
  warning?: string | null;
  error?: { message: string };
}

// Helper to create an admin client
const getSupabaseAdminClient = (): SupabaseClient => {
  const supabaseUrl = Deno.env.get('SUPABASE_URL');
  const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

  if (!supabaseUrl || !serviceRoleKey) {
    throw new Error('Missing Supabase URL or Service Role Key in environment variables.');
  }

  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      // Important: autoRefreshToken and persistSession are not needed for service role
      autoRefreshToken: false,
      persistSession: false,
      // detectSessionInUrl is for client-side, not relevant here
    }
  });
}

serve(async (req: Request) => {
  // This is needed if you're planning to invoke your function from a browser.
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*', // Or your specific frontend URL
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  }

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { bookingId } = await req.json() as DeleteBookingRequestBody;

    if (!bookingId) {
      return new Response(
        JSON.stringify({ error: { message: 'Booking ID is required.' } } as FunctionResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    const supabaseAdmin = getSupabaseAdminClient();

    // 1. Get the time_slot_id associated with the booking
    const { data: bookingData, error: fetchError } = await supabaseAdmin
      .from('bookings')
      .select('time_slot_id, event_id') // Also select event_id if needed for cache invalidation context
      .eq('id', bookingId)
      .single();

    // PGRST116: "The result contains 0 rows" - this is okay if the booking was already deleted.
    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error fetching booking details:', fetchError);
      throw new Error(`Error fetching booking details: ${fetchError.message}`);
    }

    // 2. Delete the booking
    // It's generally safe to attempt deletion even if the fetch above found nothing,
    // as the delete operation will simply do nothing if the ID doesn't exist.
    const { error: deleteError } = await supabaseAdmin
      .from('bookings')
      .delete()
      .match({ id: bookingId }); // Use match for safety, though eq('id', bookingId) is also common

    if (deleteError) {
      console.error('Error deleting booking:', deleteError);
      throw new Error(`Error deleting booking: ${deleteError.message}`);
    }

    let warningMessage: string | null = null;

    // 3. If the booking had a time_slot_id, update the time_slot
    if (bookingData?.time_slot_id) {
      const { error: updateSlotError } = await supabaseAdmin
        .from('time_slots')
        .update({ is_booked: false, booked_by_user_id: null, booking_id: null })
        .eq('id', bookingData.time_slot_id);

      if (updateSlotError) {
        console.error('Failed to update time slot status:', updateSlotError);
        // This is a non-critical error in the sense that the booking is deleted.
        // We'll return a warning to the client.
        warningMessage = "Booking deleted, but failed to update the time slot status. Please check manually.";
      }
    } else if (!bookingData && !fetchError) { // Booking was not found by the initial select
        console.warn(`Booking with ID ${bookingId} not found when attempting to update its time slot. It might have been already deleted or never existed.`);
        // No specific warning to client needed here if delete was successful.
    }


    return new Response(
      JSON.stringify({ success: true, bookingId, warning: warningMessage } as FunctionResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error: any) {
    console.error('Critical error in delete-booking function:', error);
    return new Response(
      JSON.stringify({ error: { message: error.message || 'An unexpected error occurred.' } } as FunctionResponse),
      {
        status: 500, // Internal Server Error
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
})
