import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.0.0?target=deno'

// Initialize Stripe client
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') as string, {
  httpClient: Stripe.createFetchHttpClient(),
  apiVersion: '2024-06-20',
});

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { 
        auth: { autoRefreshToken: false, persistSession: false }
      }
    );
    
    const requestData = await req.json();

    if (requestData.action === 'confirm_booking') {
      return await handleBookingConfirmation(requestData.sessionId, supabaseAdmin);
    }
    
    const { bookingData, successUrl, cancelUrl } = requestData;
    if (!bookingData || !successUrl || !cancelUrl) {
      throw new Error('Missing required parameters for checkout creation.');
    }

    const { data: pendingBooking, error: pendingError } = await supabaseAdmin
      .from('pending_bookings')
      .insert({ payload: bookingData })
      .select('id')
      .single();

    if (pendingError) {
      console.error('Error creating pending booking:', pendingError);
      throw new Error('Failed to temporarily save booking information.');
    }
    
    const pendingBookingId = pendingBooking.id;
    console.log(`Saved pending booking with ID: ${pendingBookingId}`);

    const slots = bookingData.slots || [];
    const lineItems = slots.map(slot => ({
      price_data: {
        currency: 'usd',
        product_data: { name: `Booking - ${slot.activityName}`, description: `${slot.locationName} - ${slot.timeSlot}` },
        unit_amount: Math.round(slot.price * 100)
      },
      quantity: 1
    }));
    
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      customer_email: bookingData.payer_email || undefined,
      metadata: {
        pendingBookingId: pendingBookingId
      }
    });

    return new Response(JSON.stringify({ sessionId: session.id, url: session.url }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    console.error("Error in main function handler:", { message: error.message, stack: error.stack });
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400
    });
  }
});

async function handleBookingConfirmation(sessionId: string, supabaseAdmin: any) {
  console.log('Handling booking confirmation for session:', sessionId);
  
  try {
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    if (session.payment_status !== 'paid') {
      throw new Error(`Payment not completed. Status: ${session.payment_status}`);
    }

    const metadata = session.metadata || {};
    const pendingBookingId = metadata.pendingBookingId;
    if (!pendingBookingId) {
      throw new Error('No pendingBookingId found in Stripe session metadata.');
    }
    console.log(`Retrieved pendingBookingId: ${pendingBookingId}`);

    const { data: pendingBooking, error: fetchError } = await supabaseAdmin
      .from('pending_bookings')
      .select('payload')
      .eq('id', pendingBookingId)
      .single();

    if (fetchError || !pendingBooking) {
      console.error('Error fetching pending booking:', fetchError);
      throw new Error(`Could not retrieve pending booking with ID: ${pendingBookingId}`);
    }

    const bookingPayload = pendingBooking.payload;
    const slots = bookingPayload.slots || [];

    const { data: existingBooking } = await supabaseAdmin.from('bookings').select('id').eq('stripe_payment_id', session.payment_intent).single();
    if (existingBooking) {
      console.log('Booking already exists:', existingBooking.id);
      return new Response(JSON.stringify({ success: true, booking: existingBooking, message: 'Booking already confirmed' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    const bookingDataToInsert = {
      payer_name: bookingPayload.payer_name,
      payer_email: bookingPayload.payer_email,
      user_id: bookingPayload.userId || null,
      payment_status: 'paid',
      stripe_payment_id: session.payment_intent,
      total_price: session.amount_total ? session.amount_total / 100 : 0,
    };
    
    const { data: booking, error: bookingError } = await supabaseAdmin.from("bookings").insert(bookingDataToInsert).select().single();
    if (bookingError) {
      console.error("Error inserting booking:", bookingError);
      throw new Error(`Error inserting booking: ${bookingError.message}`);
    }
    console.log("Booking created successfully:", booking.id);

    // Process slots sequentially to avoid overwhelming the database
    for (const slot of slots) {
      if (!slot.timeSlotId) continue;
      
      const slotData = {
        booking_code: booking.booking_code,
        time_slot_id: slot.timeSlotId,
        participant_name: slot.participant_name || bookingPayload.payer_name,
        horse_name: slot.horse_name || '',
        event_dressage_test_id: slot.event_dressage_test_id || null,
        activity_description: slot.activity_description || slot.activityName,
        activity_id: slot.activityId || null
      };

      console.log("Attempting to insert booking slot with data:", JSON.stringify(slotData, null, 2));

      const { data: insertedSlot, error: slotError } = await supabaseAdmin.from("booking_slots").insert(slotData).select().single();
      if (slotError) {
        console.error("Error inserting booking slot:", slotError);
        console.error("Slot data that failed:", JSON.stringify(slotData, null, 2));
      } else {
        console.log("Successfully inserted booking slot:", insertedSlot);
        await supabaseAdmin.from("time_slots").update({ is_booked: true }).eq("id", slot.timeSlotId);
        console.log("Marked time slot as booked:", slot.timeSlotId);
      }
    }

    // Clean up reservations
    if (bookingPayload.userSessionId) {
      const { error: reservationError } = await supabaseAdmin.from("slot_reservations").delete().eq("user_session_id", bookingPayload.userSessionId);
      if (reservationError) {
        console.error("Error clearing slot reservations:", reservationError);
      }
    }
    
    // Clean up pending booking
    const { error: deleteError } = await supabaseAdmin.from('pending_bookings').delete().eq('id', pendingBookingId);
    if (deleteError) {
      console.error('Failed to delete pending booking:', deleteError);
    } else {
      console.log(`Successfully cleaned up pending booking ID: ${pendingBookingId}`);
    }

    return new Response(JSON.stringify({ success: true, booking: booking, message: 'Booking confirmed successfully' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });
  } catch (error) {
    console.error("Error in handleBookingConfirmation:", { message: error.message, stack: error.stack });
    return new Response(JSON.stringify({ success: false, error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400
    });
  }
}