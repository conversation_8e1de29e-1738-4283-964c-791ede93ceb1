-- First, drop the existing constraint on activity_type in locations
ALTER TABLE locations DROP CONSTRAINT IF EXISTS arenas_activity_type_check;

-- Drop the fixed_level column as it's not needed
ALTER TABLE locations DROP COLUMN IF EXISTS fixed_level;

-- Add a new constraint for activity_type in locations that includes all possible phases
ALTER TABLE locations ADD CONSTRAINT locations_activity_type_check CHECK (
  activity_type = ANY (ARRAY['dressage', 'show_jumping', 'cross_country'])
);

-- Add comment to explain the activity_type field in locations
COMMENT ON COLUMN locations.activity_type IS 'The phase or activity type this location is used for (dressage, show_jumping, cross_country)';

-- Now fix the activities table
-- First, drop the existing constraint on activity_type
ALTER TABLE activities DROP CONSTRAINT IF EXISTS activities_activity_type_check;

-- Add a new constraint for activity_type that matches the locations table
ALTER TABLE activities ADD CONSTRAINT activities_activity_type_check CHECK (
  activity_type = ANY (ARRAY['dressage', 'show_jumping', 'cross_country'])
);

-- Add comment to explain the activity_type field in activities
COMMENT ON COLUMN activities.activity_type IS 'The phase or activity type for this activity (dressage, show_jumping, cross_country)';

-- Update any existing show_jumping values to ensure consistent casing
UPDATE locations SET activity_type = 'show_jumping' WHERE activity_type = 'show_jumping';
UPDATE activities SET activity_type = 'show_jumping' WHERE activity_type = 'show_jumping';

-- Update any existing dressage values to ensure consistent casing
UPDATE locations SET activity_type = 'dressage' WHERE activity_type = 'dressage';
UPDATE activities SET activity_type = 'dressage' WHERE activity_type = 'dressage'; 