-- This table temporarily holds booking information while the user completes the Stripe checkout process.
CREATE TABLE public.pending_bookings (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    payload jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.pending_bookings ENABLE ROW LEVEL SECURITY;

-- The service_role (used by our edge functions) should have full access.
-- No other role needs access since this table is only managed by the backend.
CREATE POLICY "Allow service_role full access" ON public.pending_bookings
FOR ALL
TO service_role
USING (true)
WITH CHECK (true); 