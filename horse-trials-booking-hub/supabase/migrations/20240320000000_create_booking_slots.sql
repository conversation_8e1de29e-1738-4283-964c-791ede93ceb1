-- Create the booking_slots table
create table if not exists public.booking_slots (
  id uuid not null default gen_random_uuid(),
  booking_code uuid not null,
  time_slot_id uuid not null,
  participant_name text not null,
  horse_name text not null,
  event_dressage_test_id uuid null,
  activity_description text null,
  created_at timestamp without time zone not null default now(),
  constraint booking_slots_pkey primary key (id),
  constraint booking_slots_booking_code_fkey foreign key (booking_code) 
    references bookings (booking_code) on delete cascade,
  constraint booking_slots_time_slot_id_fkey foreign key (time_slot_id) 
    references time_slots (id) on delete cascade,
  constraint booking_slots_event_dressage_test_id_fkey foreign key (event_dressage_test_id) 
    references event_dressage_tests (id) on delete set null
);

-- Create indexes for faster lookups
create index if not exists idx_booking_slots_booking_code on public.booking_slots (booking_code);
create index if not exists idx_booking_slots_time_slot_id on public.booking_slots (time_slot_id);

-- Create a trigger to update time_slots.is_booked when a booking_slot is created or deleted
create or replace function update_time_slot_booking_status()
returns trigger as $$
begin
  if (tg_op = 'INSERT') then
    update time_slots 
    set is_booked = true 
    where id = NEW.time_slot_id;
  elsif (tg_op = 'DELETE') then
    update time_slots 
    set is_booked = false 
    where id = OLD.time_slot_id;
  end if;
  return null;
end;
$$ language plpgsql;

drop trigger if exists booking_slots_booking_status_trigger on booking_slots;
create trigger booking_slots_booking_status_trigger
after insert or delete on booking_slots
for each row
execute function update_time_slot_booking_status();

-- Update the view to use the new structure
create or replace view public.bookings_with_details_view as
select
  b.id as booking_id,
  b.booking_code,
  b.payer_name,
  b.payer_email,
  b.payment_status,
  b.user_id,
  b.created_at as booking_created_at,
  
  bs.id as booking_slot_id,
  bs.participant_name,
  bs.horse_name,
  bs.activity_description,
  bs.created_at as slot_created_at,
  
  ts.id as time_slot_id,
  ts.start_time as time_slot_start_time,
  ts.end_time as time_slot_end_time,
  ts.level as time_slot_level,
  ts.is_booked as time_slot_is_booked,
  ts.activity_id as time_slot_activity_fk,
  ts.location_id as time_slot_location_fk,
  
  l.id as location_id,
  l.name as location_name,
  l.event_id as location_event_id,
  l.activity_type as location_activity_type,
  
  act.id as activity_id,
  act.activity_type as activity_specific_type,
  act.level as activity_level,
  act.description as activity_specific_description,
  act.slot_duration_minutes as activity_slot_duration_minutes,
  
  edt.id as event_dressage_test_link_id,
  dtl.id as dressage_test_id,
  dtl.label as dressage_test_label,
  dtl.description as dressage_test_description,
  ll.id as dressage_level_id,
  ll.name as dressage_level_name,
  ll.discipline as dressage_level_discipline
from
  bookings b
  join booking_slots bs on b.booking_code = bs.booking_code
  left join time_slots ts on bs.time_slot_id = ts.id
  left join locations l on ts.location_id = l.id
  left join activities act on ts.activity_id = act.id
  left join event_dressage_tests edt on bs.event_dressage_test_id = edt.id
  left join dressage_test_library dtl on edt.test_id = dtl.id
  left join levels_library ll on dtl.level_id = ll.id; 