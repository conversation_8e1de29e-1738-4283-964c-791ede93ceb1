-- Fix SECURITY DEFINER views by recreating them without SECURITY DEFINER
-- This addresses the security linting issues

-- Drop existing views if they exist
DROP VIEW IF EXISTS public.bookings_with_details_view;
DROP VIEW IF EXISTS public.public_bookings_with_details_view;

-- Recreate bookings_with_details_view without SECURITY DEFINER
CREATE VIEW public.bookings_with_details_view AS
SELECT
  b.id as booking_id,
  b.booking_code,
  b.payer_name,
  b.payer_email,
  b.payment_status,
  b.user_id,
  b.created_at as booking_created_at,
  
  bs.id as booking_slot_id,
  bs.participant_name,
  bs.horse_name,
  bs.activity_description,
  bs.activity_id as booking_slot_activity_id,
  bs.created_at as slot_created_at,
  
  ts.id as time_slot_id,
  ts.start_time as time_slot_start_time,
  ts.end_time as time_slot_end_time,
  ts.level as time_slot_level,
  ts.is_booked as time_slot_is_booked,
  ts.activity_id as time_slot_activity_fk,
  ts.location_id as time_slot_location_fk,
  
  l.id as location_id,
  l.name as location_name,
  l.event_id as location_event_id,
  l.activity_type as location_activity_type,
  
  act.id as activity_id,
  act.activity_type as activity_specific_type,
  act.level as activity_level,
  act.description as activity_specific_description,
  act.slot_duration_minutes as activity_slot_duration_minutes,
  
  edt.id as event_dressage_test_link_id,
  dtl.id as dressage_test_id,
  dtl.label as dressage_test_label,
  dtl.description as dressage_test_description,
  ll.id as dressage_level_id,
  ll.name as dressage_level_name,
  ll.discipline as dressage_level_discipline
FROM
  bookings b
  JOIN booking_slots bs ON b.booking_code = bs.booking_code
  LEFT JOIN time_slots ts ON bs.time_slot_id = ts.id
  LEFT JOIN locations l ON ts.location_id = l.id
  LEFT JOIN activities act ON COALESCE(bs.activity_id, ts.activity_id) = act.id
  LEFT JOIN event_dressage_tests edt ON bs.event_dressage_test_id = edt.id
  LEFT JOIN dressage_test_library dtl ON edt.test_id = dtl.id
  LEFT JOIN levels_library ll ON dtl.level_id = ll.id;

-- Create public_bookings_with_details_view (if it doesn't exist, this will create it)
-- This view is used by the useBookings hook for public booking display
CREATE VIEW public.public_bookings_with_details_view AS
SELECT
  b.id as booking_id,
  b.booking_code,
  b.payer_name,
  b.payer_email,
  b.payment_status,
  b.user_id,
  b.created_at as booking_created_at,
  
  bs.id as booking_slot_id,
  bs.participant_name,
  bs.horse_name,
  bs.activity_description,
  bs.activity_id as booking_slot_activity_id,
  bs.created_at as slot_created_at,
  
  ts.id as time_slot_id,
  ts.start_time as time_slot_start_time,
  ts.end_time as time_slot_end_time,
  ts.level as time_slot_level,
  ts.is_booked as time_slot_is_booked,
  ts.activity_id as time_slot_activity_fk,
  ts.location_id as time_slot_location_fk,
  
  l.id as location_id,
  l.name as location_name,
  l.event_id as location_event_id,
  l.activity_type as location_activity_type,
  
  act.id as activity_id,
  act.activity_type as activity_specific_type,
  act.level as activity_level,
  act.description as activity_specific_description,
  act.slot_duration_minutes as activity_slot_duration_minutes,
  
  edt.id as event_dressage_test_link_id,
  dtl.id as dressage_test_id,
  dtl.label as dressage_test_label,
  dtl.description as dressage_test_description,
  ll.id as dressage_level_id,
  ll.name as dressage_level_name,
  ll.discipline as dressage_level_discipline
FROM
  bookings b
  JOIN booking_slots bs ON b.booking_code = bs.booking_code
  LEFT JOIN time_slots ts ON bs.time_slot_id = ts.id
  LEFT JOIN locations l ON ts.location_id = l.id
  LEFT JOIN activities act ON COALESCE(bs.activity_id, ts.activity_id) = act.id
  LEFT JOIN event_dressage_tests edt ON bs.event_dressage_test_id = edt.id
  LEFT JOIN dressage_test_library dtl ON edt.test_id = dtl.id
  LEFT JOIN levels_library ll ON dtl.level_id = ll.id;

-- Grant appropriate permissions on the views
GRANT SELECT ON public.bookings_with_details_view TO authenticated;
GRANT SELECT ON public.public_bookings_with_details_view TO authenticated;
GRANT SELECT ON public.bookings_with_details_view TO anon;
GRANT SELECT ON public.public_bookings_with_details_view TO anon; 