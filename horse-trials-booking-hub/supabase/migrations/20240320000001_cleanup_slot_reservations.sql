-- Delete expired reservations
delete from slot_reservations
where expires_at < now();

-- Delete reservations with invalid time_slot_id
delete from slot_reservations
where time_slot_id not in (select id from time_slots);

-- Delete duplicate reservations (keep the most recent one for each time_slot_id and user_session_id)
delete from slot_reservations a
using (
  select time_slot_id, user_session_id, max(created_at) as max_created_at
  from slot_reservations
  group by time_slot_id, user_session_id
  having count(*) > 1
) b
where a.time_slot_id = b.time_slot_id
  and a.user_session_id = b.user_session_id
  and a.created_at < b.max_created_at;

-- Add a unique constraint to prevent future duplicates
alter table slot_reservations
add constraint unique_time_slot_user_session unique (time_slot_id, user_session_id);

-- Add a trigger to automatically delete expired reservations
create or replace function cleanup_expired_reservations()
returns trigger as $$
begin
  delete from slot_reservations
  where expires_at < now();
  return null;
end;
$$ language plpgsql;

drop trigger if exists cleanup_expired_reservations_trigger on slot_reservations;
create trigger cleanup_expired_reservations_trigger
after insert on slot_reservations
for each statement
execute function cleanup_expired_reservations(); 