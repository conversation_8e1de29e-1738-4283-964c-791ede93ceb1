-- Drop the existing view if it exists
drop view if exists public.bookings_with_details_view;

-- Create the view for booking details
create view public.bookings_with_details_view as
select
  b.id as booking_id,
  b.booking_code,
  b.payer_name,
  b.payer_email,
  b.payment_status,
  b.user_id,
  b.created_at as booking_created_at,
  
  bs.id as booking_slot_id,
  bs.participant_name,
  bs.horse_name,
  bs.activity_description,
  bs.activity_id as booking_slot_activity_id,
  bs.created_at as slot_created_at,
  
  ts.id as time_slot_id,
  ts.start_time as time_slot_start_time,
  ts.end_time as time_slot_end_time,
  ts.level as time_slot_level,
  ts.is_booked as time_slot_is_booked,
  ts.activity_id as time_slot_activity_fk,
  ts.location_id as time_slot_location_fk,
  
  l.id as location_id,
  l.name as location_name,
  l.event_id as location_event_id,
  l.activity_type as location_activity_type,
  
  act.id as activity_id,
  act.activity_type as activity_specific_type,
  act.level as activity_level,
  act.description as activity_specific_description,
  act.slot_duration_minutes as activity_slot_duration_minutes,
  
  edt.id as event_dressage_test_link_id,
  dtl.id as dressage_test_id,
  dtl.label as dressage_test_label,
  dtl.description as dressage_test_description,
  ll.id as dressage_level_id,
  ll.name as dressage_level_name,
  ll.discipline as dressage_level_discipline
from
  bookings b
  join booking_slots bs on b.booking_code = bs.booking_code
  left join time_slots ts on bs.time_slot_id = ts.id
  left join locations l on ts.location_id = l.id
  left join activities act on coalesce(bs.activity_id, ts.activity_id) = act.id
  left join event_dressage_tests edt on bs.event_dressage_test_id = edt.id
  left join dressage_test_library dtl on edt.test_id = dtl.id
  left join levels_library ll on dtl.level_id = ll.id; 