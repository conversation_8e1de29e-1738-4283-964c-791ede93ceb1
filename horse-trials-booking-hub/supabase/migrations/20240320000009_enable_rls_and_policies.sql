-- Enable Row Level Security on all tables
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.booking_slots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.slot_reservations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_slots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.event_dressage_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dressage_test_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.levels_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- BOOKINGS TABLE POLICIES
-- ============================================================================

-- Users can view their own bookings
CREATE POLICY "Users can view their own bookings" ON public.bookings
FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own bookings
CREATE POLICY "Users can insert their own bookings" ON public.bookings
FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own bookings
CREATE POLICY "Users can update their own bookings" ON public.bookings
FOR UPDATE USING (auth.uid() = user_id);

-- Admins can view all bookings
CREATE POLICY "Admins can view all bookings" ON public.bookings
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- Admins can insert/update/delete all bookings
CREATE POLICY "Admins can manage all bookings" ON public.bookings
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- BOOKING_SLOTS TABLE POLICIES
-- ============================================================================

-- Users can view booking slots for their own bookings
CREATE POLICY "Users can view their booking slots" ON public.booking_slots
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM bookings 
    WHERE booking_code = booking_slots.booking_code 
    AND user_id = auth.uid()
  )
);

-- Users can insert booking slots for their own bookings
CREATE POLICY "Users can insert their booking slots" ON public.booking_slots
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM bookings 
    WHERE booking_code = booking_slots.booking_code 
    AND user_id = auth.uid()
  )
);

-- Admins can view all booking slots
CREATE POLICY "Admins can view all booking slots" ON public.booking_slots
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- Admins can manage all booking slots
CREATE POLICY "Admins can manage all booking slots" ON public.booking_slots
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- PROFILES TABLE POLICIES
-- ============================================================================

-- Users can view their own profile
CREATE POLICY "Users can view their own profile" ON public.profiles
FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update their own profile" ON public.profiles
FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile
CREATE POLICY "Users can insert their own profile" ON public.profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- ============================================================================
-- SLOT_RESERVATIONS TABLE POLICIES
-- ============================================================================

-- Users can view their own slot reservations
CREATE POLICY "Users can view their own reservations" ON public.slot_reservations
FOR SELECT USING (user_session_id LIKE auth.uid()::text || '%');

-- Users can insert their own slot reservations
CREATE POLICY "Users can insert their own reservations" ON public.slot_reservations
FOR INSERT WITH CHECK (user_session_id LIKE auth.uid()::text || '%');

-- Users can delete their own slot reservations
CREATE POLICY "Users can delete their own reservations" ON public.slot_reservations
FOR DELETE USING (user_session_id LIKE auth.uid()::text || '%');

-- Admins can view all slot reservations
CREATE POLICY "Admins can view all reservations" ON public.slot_reservations
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- Admins can manage all slot reservations
CREATE POLICY "Admins can manage all reservations" ON public.slot_reservations
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- EVENTS TABLE POLICIES
-- ============================================================================

-- Public can view active events
CREATE POLICY "Public can view active events" ON public.events
FOR SELECT USING (is_active = true);

-- Admins can view all events
CREATE POLICY "Admins can view all events" ON public.events
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- Admins can manage all events
CREATE POLICY "Admins can manage all events" ON public.events
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- LOCATIONS TABLE POLICIES
-- ============================================================================

-- Public can view active locations
CREATE POLICY "Public can view active locations" ON public.locations
FOR SELECT USING (is_active = true);

-- Admins can view all locations
CREATE POLICY "Admins can view all locations" ON public.locations
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- Admins can manage all locations
CREATE POLICY "Admins can manage all locations" ON public.locations
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- TIME_SLOTS TABLE POLICIES
-- ============================================================================

-- Public can view time slots for active locations
CREATE POLICY "Public can view time slots" ON public.time_slots
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM locations 
    WHERE locations.id = time_slots.location_id 
    AND locations.is_active = true
  )
);

-- Admins can view all time slots
CREATE POLICY "Admins can view all time slots" ON public.time_slots
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- Admins can manage all time slots
CREATE POLICY "Admins can manage all time slots" ON public.time_slots
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- ACTIVITIES TABLE POLICIES
-- ============================================================================

-- Public can view activities
CREATE POLICY "Public can view activities" ON public.activities
FOR SELECT USING (true);

-- Admins can manage all activities
CREATE POLICY "Admins can manage all activities" ON public.activities
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- EVENT_DRESSAGE_TESTS TABLE POLICIES
-- ============================================================================

-- Public can view enabled dressage tests
CREATE POLICY "Public can view enabled dressage tests" ON public.event_dressage_tests
FOR SELECT USING (is_enabled = true);

-- Admins can view all dressage tests
CREATE POLICY "Admins can view all dressage tests" ON public.event_dressage_tests
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- Admins can manage all dressage tests
CREATE POLICY "Admins can manage all dressage tests" ON public.event_dressage_tests
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- DRESSAGE_TEST_LIBRARY TABLE POLICIES
-- ============================================================================

-- Public can view active dressage tests
CREATE POLICY "Public can view active dressage tests" ON public.dressage_test_library
FOR SELECT USING (is_active = true);

-- Admins can manage all dressage tests
CREATE POLICY "Admins can manage all dressage tests" ON public.dressage_test_library
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- LEVELS_LIBRARY TABLE POLICIES
-- ============================================================================

-- Public can view active levels
CREATE POLICY "Public can view active levels" ON public.levels_library
FOR SELECT USING (is_active = true);

-- Admins can manage all levels
CREATE POLICY "Admins can manage all levels" ON public.levels_library
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- USER_ROLES TABLE POLICIES
-- ============================================================================

-- Users can view their own roles
CREATE POLICY "Users can view their own roles" ON public.user_roles
FOR SELECT USING (auth.uid() = user_id);

-- Admins can view all user roles
CREATE POLICY "Admins can view all user roles" ON public.user_roles
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- Admins can manage all user roles
CREATE POLICY "Admins can manage all user roles" ON public.user_roles
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- ============================================================================
-- SPECIAL POLICIES FOR WEBHOOKS AND EDGE FUNCTIONS
-- ============================================================================

-- Allow service role to bypass RLS for webhook processing
-- This is needed for the Stripe webhook to create bookings
CREATE POLICY "Service role bypass" ON public.bookings
FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role bypass" ON public.booking_slots
FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role bypass" ON public.slot_reservations
FOR ALL USING (auth.role() = 'service_role'); 