-- Add Stripe-related columns to bookings table
ALTER TABLE public.bookings 
ADD COLUMN stripe_payment_id text,
ADD COLUMN total_price numeric(10,2);

-- Add index for stripe_payment_id for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_stripe_payment_id ON public.bookings USING btree (stripe_payment_id);

-- Add unique constraint to prevent duplicate payments
ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_stripe_payment_id_unique UNIQUE (stripe_payment_id); 