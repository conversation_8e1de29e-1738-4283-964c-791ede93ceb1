-- Update locations table to use phase instead of type
-- Add phase column
ALTER TABLE locations ADD COLUMN phase TEXT CHECK (phase IN ('dressage', 'show_jumping', 'cross_country'));

-- Add description column if it doesn't exist
ALTER TABLE locations ADD COLUMN IF NOT EXISTS description TEXT;

-- Drop the old type column if it exists
ALTER TABLE locations DROP COLUMN IF EXISTS type;

-- Add comment to explain the phase field
COMMENT ON COLUMN locations.phase IS 'Phase of the event this location is used for (dressage, show_jumping, cross_country)'; 