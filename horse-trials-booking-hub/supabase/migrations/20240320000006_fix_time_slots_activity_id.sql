-- Fix existing time slots that don't have activity_id set
-- This migration updates time slots to link them to the correct activity based on time range and location

UPDATE time_slots 
SET activity_id = activities.id
FROM activities 
WHERE time_slots.location_id = activities.location_id
  AND time_slots.start_time >= activities.start_time
  AND time_slots.end_time <= activities.end_time
  AND time_slots.activity_id IS NULL;

-- Add an index to improve performance for activity_id lookups
CREATE INDEX IF NOT EXISTS idx_time_slots_activity_id ON public.time_slots (activity_id); 