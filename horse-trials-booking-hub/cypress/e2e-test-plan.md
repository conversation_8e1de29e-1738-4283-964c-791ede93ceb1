# E2E Testing Plan

## User Flows to Test

### Authentication Flow
- [ ] User can sign up
- [ ] User can log in
- [ ] User can log out
- [ ] User can reset password

### Booking Flow
- [ ] User can view available events
- [ ] User can select an event
- [ ] User can add slots to cart
- [ ] User can view cart
- [ ] User can checkout
- [ ] User can complete payment
- [ ] User receives confirmation

### Admin Flow
- [ ] Admin can create events
- [ ] Admin can edit events
- [ ] Admin can view bookings
- [ ] Admin can manage users

## Example E2E Tests