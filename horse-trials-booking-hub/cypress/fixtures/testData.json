{"users": {"regular": {"email": "<EMAIL>", "password": "userpassword", "name": "Test User", "role": "user"}, "admin": {"email": "<EMAIL>", "password": "adminpassword", "name": "Admin User", "role": "admin"}, "superAdmin": {"email": "<EMAIL>", "password": "superadminpassword", "name": "Super Admin", "role": "super_admin"}, "organizer": {"email": "<EMAIL>", "password": "organizerpassword", "name": "Event Organizer", "role": "organizer"}}, "events": {"dressage": {"name": "Test Dressage Event", "description": "A test dressage competition", "activity": "dressage", "price": 45}, "jumping": {"name": "Test Jumping Event", "description": "A test show jumping competition", "activity": "show_jumping", "price": 60}, "crossCountry": {"name": "Test Cross Country Event", "description": "A test cross country competition", "activity": "cross_country", "price": 75}}, "locations": {"mainArena": {"name": "Main Arena", "description": "Primary competition arena"}, "warmupArena": {"name": "Warm-up Arena", "description": "Warm-up and practice area"}, "crossCountryCourse": {"name": "Cross Country Course", "description": "Cross country competition course"}}, "timeSlots": {"morning": {"start": "9:00 AM", "end": "10:00 AM", "duration": 60}, "afternoon": {"start": "2:00 PM", "end": "3:00 PM", "duration": 60}, "evening": {"start": "6:00 PM", "end": "7:00 PM", "duration": 60}}, "bookings": {"valid": {"payer_name": "Test User", "payer_email": "<EMAIL>", "participant_name": "Test Rider", "horse_name": "Test Horse", "total_price": 45}, "multipleSlots": {"payer_name": "<PERSON>", "payer_email": "<EMAIL>", "participant_name": "<PERSON>", "horse_name": "Thunder", "total_price": 105}}, "dressageTests": [{"id": "test1", "label": "Intro Test A", "level": "Intro"}, {"id": "test2", "label": "Training Level Test 1", "level": "Training"}, {"id": "test3", "label": "First Level Test 1", "level": "First"}], "pricing": {"dressage": 45, "show_jumping": 60, "cross_country": 75, "currency": "USD", "currency_symbol": "$"}, "testDates": {"today": "2024-01-15", "tomorrow": "2024-01-16", "nextWeek": "2024-01-22", "nextMonth": "2024-02-15"}}