describe('Organizer creates a new event', () => {
  beforeEach(() => {
    cy.visit('/');
    // Login as organizer
    cy.get('button').contains('Login').click();
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('Fritz0615@@');
    cy.get('[data-testid="signin-submit-button"]').click();
    cy.contains('Upcoming Events').should('be.visible');
  });

  it('can create a new event from the organizer dashboard', () => {
    // Open user dropdown and go to Organizer Dashboard
    cy.get('button').contains('Sally Organizer').click();
    cy.contains('Organizer Dashboard').click();
    cy.url().should('include', '/admin/organizer-dashboard');
    cy.contains('Organizer Dashboard').should('be.visible');

    // Click Create New Event
    cy.get('button').contains('Create New Event').click();
    cy.url().should('include', '/admin/events/create');
    cy.contains('Create New Event').should('be.visible');

    // Fill out the event creation form using data-testid selectors
    cy.get('[data-testid="event-name-input"]').type('Cypress Test Event');
    cy.get('[data-testid="event-start-date-input"]').type('2025-12-01');
    cy.get('[data-testid="event-end-date-input"]').type('2025-12-02');

    // Submit the form
    cy.get('[data-testid="create-event-button"]').click();

    // Verify the event was created (success message or event appears in list)
    cy.contains('Event created successfully').should('be.visible');
    // Or, if redirected to event list, check for the new event
    // cy.contains('Cypress Test Event').should('be.visible');
  });
}); 