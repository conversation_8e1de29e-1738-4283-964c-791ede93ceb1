describe('Complete Booking Journey', () => {
  let testEventId: string;
  let testLocationId: string;
  let testTimeSlotId: string;
  let testBookingId: string;

  before(() => {
    // Set up test data before running tests
    cy.log('Setting up test data...');
    
    // Create test event
    cy.request({
      method: 'POST',
      url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/events`,
      headers: {
        'apikey': Cypress.env('SUPABASE_ANON_KEY'),
        'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      },
      body: {
        name: 'Cypress Test Event',
        description: 'Test event for e2e testing',
        start_date: '2024-02-15',
        end_date: '2024-02-16',
        is_active: true,
        created_by: 'test-admin'
      }
    }).then((response) => {
      testEventId = response.body[0].id;
      cy.log(`Created test event: ${testEventId}`);
      
      // Create test location
      cy.request({
        method: 'POST',
        url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/locations`,
        headers: {
          'apikey': Cypress.env('SUPABASE_ANON_KEY'),
          'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        },
        body: {
          name: 'Test Arena',
          description: 'Test arena for e2e testing',
          event_id: testEventId,
          created_by: 'test-admin'
        }
      }).then((locationResponse) => {
        testLocationId = locationResponse.body[0].id;
        cy.log(`Created test location: ${testLocationId}`);
        
        // Create test activity
        cy.request({
          method: 'POST',
          url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/activities`,
          headers: {
            'apikey': Cypress.env('SUPABASE_ANON_KEY'),
            'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
          },
          body: {
            name: 'Test Dressage',
            activity_type: 'dressage',
            location_id: testLocationId,
            event_id: testEventId,
            created_by: 'test-admin'
          }
        }).then((activityResponse) => {
          const testActivityId = activityResponse.body[0].id;
          cy.log(`Created test activity: ${testActivityId}`);
          
          // Create test time slot
          cy.request({
            method: 'POST',
            url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/time_slots`,
            headers: {
              'apikey': Cypress.env('SUPABASE_ANON_KEY'),
              'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`,
              'Content-Type': 'application/json',
              'Prefer': 'return=representation'
            },
            body: {
              start_time: '2024-02-15T09:00:00',
              end_time: '2024-02-15T10:00:00',
              activity_id: testActivityId,
              location_id: testLocationId,
              event_id: testEventId,
              is_booked: false,
              created_by: 'test-admin'
            }
          }).then((timeSlotResponse) => {
            testTimeSlotId = timeSlotResponse.body[0].id;
            cy.log(`Created test time slot: ${testTimeSlotId}`);
          });
        });
      });
    });
  });

  after(() => {
    // Clean up test data after tests
    cy.log('Cleaning up test data...');
    
    if (testTimeSlotId) {
      cy.request({
        method: 'DELETE',
        url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/time_slots?id=eq.${testTimeSlotId}`,
        headers: {
          'apikey': Cypress.env('SUPABASE_ANON_KEY'),
          'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`
        }
      });
    }
    
    if (testLocationId) {
      cy.request({
        method: 'DELETE',
        url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/locations?id=eq.${testLocationId}`,
        headers: {
          'apikey': Cypress.env('SUPABASE_ANON_KEY'),
          'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`
        }
      });
    }
    
    if (testEventId) {
      cy.request({
        method: 'DELETE',
        url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/events?id=eq.${testEventId}`,
        headers: {
          'apikey': Cypress.env('SUPABASE_ANON_KEY'),
          'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`
        }
      });
    }
  });

  it('completes the full booking journey from event creation to confirmation', () => {
    // Mock Stripe checkout to avoid actual payment
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 200,
      body: {
        url: 'https://checkout.stripe.com/test',
        sessionId: 'test_session_id'
      }
    }).as('stripeCheckout');

    // Mock booking confirmation
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 200,
      body: {
        success: true,
        booking: {
          id: 'test-booking-id',
          booking_code: 'TEST123',
          total_price: 45,
          payer_name: 'Test User',
          payer_email: '<EMAIL>'
        },
        message: 'Booking confirmed successfully'
      }
    }).as('bookingConfirmation');

    // Step 1: Visit the application
    cy.visit('/');
    cy.get('[data-testid="events-section"]').should('be.visible');
    cy.get('[data-testid="events-title"]').should('contain', 'Upcoming Events');

    // Step 2: Find and select our test event
    cy.contains('Cypress Test Event').click();
    cy.get('[data-testid="details-view"]').should('be.visible');
    cy.get('[data-testid="back-button"]').should('be.visible');

    // Step 3: Select a time slot
    cy.get('button').contains('Reserve').first().click();
    
    // Step 4: Verify cart icon shows the item
    cy.get('[data-testid="floating-cart-icon"]').should('be.visible');
    cy.get('[data-testid="cart-badge"]').should('contain', '1');

    // Step 5: Open cart and proceed to booking
    cy.get('[data-testid="floating-cart-icon"]').click();
    cy.get('[data-testid="cart-widget"]').should('be.visible');
    cy.get('[data-testid="proceed-to-booking"]').click();

    // Step 6: Fill out booking form
    cy.get('[data-testid="booking-view"]').should('be.visible');
    cy.contains('Complete Your Booking').should('be.visible');
    
    // Fill participant information
    cy.get('input[id="payer_name"]').type('Test User');
    cy.get('input[id="payer_email"]').type('<EMAIL>');
    
    // Fill slot-specific information
    cy.get('input[placeholder*="rider"]').first().type('Test Rider');
    cy.get('input[placeholder*="horse"]').first().type('Test Horse');
    
    // Select dressage test if available
    cy.get('body').then(($body) => {
      if ($body.find('select').length > 0) {
        cy.get('select').first().select('1');
      }
    });

    // Step 7: Verify pricing and booking details
    cy.contains('Total:').should('be.visible');
    cy.contains('Cypress Test Event').should('be.visible');
    cy.contains('Number of slots:').should('be.visible');
    cy.contains('1').should('be.visible');

    // Step 8: Complete booking
    cy.get('button').contains('Complete Booking').click();

    // Step 9: Verify Stripe checkout was called
    cy.wait('@stripeCheckout');
    cy.get('@stripeCheckout.all').should('have.length', 1);

    // Step 10: Simulate successful payment redirect
    cy.visit('/booking-success?session_id=test_session_id');

    // Step 11: Verify booking confirmation
    cy.wait('@bookingConfirmation');
    cy.contains('Booking Confirmed').should('be.visible');
    cy.contains('TEST123').should('be.visible'); // Booking code
    cy.contains('Test User').should('be.visible'); // Payer name
    cy.contains('<EMAIL>').should('be.visible'); // Payer email
    cy.contains('$45').should('be.visible'); // Total price

    // Step 12: Verify booking was created in database
    cy.request({
      method: 'GET',
      url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/bookings?booking_code=eq.TEST123`,
      headers: {
        'apikey': Cypress.env('SUPABASE_ANON_KEY'),
        'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`
      }
    }).then((response) => {
      expect(response.body).to.have.length(1);
      const booking = response.body[0];
      expect(booking.payer_name).to.equal('Test User');
      expect(booking.payer_email).to.equal('<EMAIL>');
      expect(booking.total_price).to.equal(45);
      testBookingId = booking.id;
    });

    // Step 13: Verify time slot was marked as booked
    cy.request({
      method: 'GET',
      url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/time_slots?id=eq.${testTimeSlotId}`,
      headers: {
        'apikey': Cypress.env('SUPABASE_ANON_KEY'),
        'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`
      }
    }).then((response) => {
      expect(response.body).to.have.length(1);
      expect(response.body[0].is_booked).to.be.true;
    });

    // Step 14: Verify booking slot was created
    cy.request({
      method: 'GET',
      url: `${Cypress.env('apiUrl') || 'http://localhost:54321'}/rest/v1/booking_slots?booking_code=eq.TEST123`,
      headers: {
        'apikey': Cypress.env('SUPABASE_ANON_KEY'),
        'Authorization': `Bearer ${Cypress.env('SUPABASE_ANON_KEY')}`
      }
    }).then((response) => {
      expect(response.body).to.have.length(1);
      const bookingSlot = response.body[0];
      expect(bookingSlot.participant_name).to.equal('Test Rider');
      expect(bookingSlot.horse_name).to.equal('Test Horse');
      expect(bookingSlot.time_slot_id).to.equal(testTimeSlotId);
    });

    // Step 15: Test navigation from success page
    cy.get('button').contains('Back to Events').click();
    cy.url().should('eq', Cypress.config().baseUrl + '/');
    cy.get('[data-testid="events-section"]').should('be.visible');
  });

  it('handles booking conflicts gracefully', () => {
    // Try to book the same time slot that was just booked
    cy.visit('/');
    cy.contains('Cypress Test Event').click();
    
    // The time slot should now show as booked
    cy.get('button').contains('Reserve').should('not.exist');
    cy.contains('Booked').should('be.visible');
  });

  it('allows admin to view the booking', () => {
    // Login as admin
    cy.loginAsAdmin();
    
    // Navigate to admin bookings
    cy.contains('Bookings').click();
    cy.url().should('include', '/admin/bookings');
    
    // Should see the test booking
    cy.contains('TEST123').should('be.visible');
    cy.contains('Test User').should('be.visible');
    cy.contains('<EMAIL>').should('be.visible');
    cy.contains('$45').should('be.visible');
  });
}); 