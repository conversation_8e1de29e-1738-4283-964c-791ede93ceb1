describe('Admin Flow', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('allows admin to access admin login', () => {
    // Should show admin login option in header
    cy.get('button').contains('Login').click();
    
    // Look for admin login option (this might be in a dropdown or separate button)
    cy.get('body').then(($body) => {
      if ($body.find('button').contains('Admin Login').length > 0) {
        cy.get('button').contains('Admin Login').click();
      } else {
        // If no direct admin login button, try to find it in the auth modal
        cy.get('button').contains('Login').click();
        cy.get('[data-testid="admin-login-link"]').click();
      }
    });
    
    // Should show admin login form
    cy.contains('Organizer Login').should('be.visible');
  });

  it('allows admin to log in and access dashboard', () => {
    // Navigate to admin login
    cy.get('button').contains('Login').click();
    cy.get('body').then(($body) => {
      if ($body.find('button').contains('Admin Login').length > 0) {
        cy.get('button').contains('Admin Login').click();
      } else {
        cy.get('[data-testid="admin-login-link"]').click();
      }
    });
    
    // Fill admin login form
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('adminpassword');
    cy.get('button').contains('Sign In').click();
    
    // Should redirect to admin dashboard
    cy.url().should('include', '/admin');
    cy.contains('Admin Dashboard').should('be.visible');
  });

  it('shows appropriate admin dashboard based on role', () => {
    // Login as super admin
    cy.get('button').contains('Login').click();
    cy.get('body').then(($body) => {
      if ($body.find('button').contains('Admin Login').length > 0) {
        cy.get('button').contains('Admin Login').click();
      } else {
        cy.get('[data-testid="admin-login-link"]').click();
      }
    });
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('superadminpassword');
    cy.get('button').contains('Sign In').click();
    
    // Should show super admin dashboard
    cy.url().should('include', '/admin/super-admin-dashboard');
    cy.contains('Super Admin Dashboard').should('be.visible');
    
    // Should show super admin specific options
    cy.contains('Users Management').should('be.visible');
    cy.contains('Database Management').should('be.visible');
  });

  it('allows organizer to access organizer dashboard', () => {
    // Login as organizer
    cy.get('button').contains('Login').click();
    cy.get('body').then(($body) => {
      if ($body.find('button').contains('Admin Login').length > 0) {
        cy.get('button').contains('Admin Login').click();
      } else {
        cy.get('[data-testid="admin-login-link"]').click();
      }
    });
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('Fritz0615@@');
    cy.get('button').contains('Sign In').click();
    
    // Should show organizer dashboard
    cy.url().should('include', '/admin/organizer-dashboard');
    cy.contains('Organizer Dashboard').should('be.visible');
    
    // Should show organizer specific options
    cy.contains('Event Management').should('be.visible');
    cy.contains('Location Management').should('be.visible');
  });

  it('prevents unauthorized access to admin areas', () => {
    // Try to access admin dashboard without login
    cy.visit('/admin/dashboard');
    
    // Should redirect to login
    cy.url().should('include', '/admin/login');
    cy.contains('Authentication Required').should('be.visible');
  });

  it('allows admin to view bookings', () => {
    // Login as admin
    cy.get('button').contains('Login').click();
    cy.get('body').then(($body) => {
      if ($body.find('button').contains('Admin Login').length > 0) {
        cy.get('button').contains('Admin Login').click();
      } else {
        cy.get('[data-testid="admin-login-link"]').click();
      }
    });
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('adminpassword');
    cy.get('button').contains('Sign In').click();
    
    // Navigate to bookings
    cy.contains('Bookings').click();
    
    // Should show bookings page
    cy.url().should('include', '/admin/bookings');
    cy.contains('All Bookings').should('be.visible');
  });

  it('allows admin to manage events', () => {
    // Login as admin
    cy.get('button').contains('Login').click();
    cy.get('body').then(($body) => {
      if ($body.find('button').contains('Admin Login').length > 0) {
        cy.get('button').contains('Admin Login').click();
      } else {
        cy.get('[data-testid="admin-login-link"]').click();
      }
    });
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('adminpassword');
    cy.get('button').contains('Sign In').click();
    
    // Navigate to event management
    cy.contains('Event Management').click();
    
    // Should show event management page
    cy.url().should('include', '/admin/events');
    cy.contains('Event Management').should('be.visible');
  });

  it('allows admin to create new events', () => {
    // Login as admin
    cy.get('button').contains('Login').click();
    cy.get('body').then(($body) => {
      if ($body.find('button').contains('Admin Login').length > 0) {
        cy.get('button').contains('Admin Login').click();
      } else {
        cy.get('[data-testid="admin-login-link"]').click();
      }
    });
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('adminpassword');
    cy.get('button').contains('Sign In').click();
    
    // Navigate to event management
    cy.contains('Event Management').click();
    
    // Click create new event
    cy.get('button').contains('Create Event').click();
    
    // Should show create event form
    cy.url().should('include', '/admin/events/create');
    cy.contains('Create New Event').should('be.visible');
  });

  it('allows admin to manage users (super admin only)', () => {
    // Login as super admin
    cy.get('button').contains('Login').click();
    cy.get('body').then(($body) => {
      if ($body.find('button').contains('Admin Login').length > 0) {
        cy.get('button').contains('Admin Login').click();
      } else {
        cy.get('[data-testid="admin-login-link"]').click();
      }
    });
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('superadminpassword');
    cy.get('button').contains('Sign In').click();
    
    // Navigate to user management
    cy.contains('Users Management').click();
    
    // Should show user management page
    cy.url().should('include', '/admin/users');
    cy.contains('User Management').should('be.visible');
  });

  it('prevents regular users from accessing admin areas', () => {
    // Login as regular user
    cy.get('button').contains('Login').click();
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('userpassword');
    cy.get('[data-testid="signin-submit-button"]').click();
    
    // Try to access admin dashboard
    cy.visit('/admin/dashboard');
    
    // Should be denied access
    cy.contains('Access Denied').should('be.visible');
  });

  it('allows admin to sign out', () => {
    // Login as admin
    cy.get('button').contains('Login').click();
    cy.get('body').then(($body) => {
      if ($body.find('button').contains('Admin Login').length > 0) {
        cy.get('button').contains('Admin Login').click();
      } else {
        cy.get('[data-testid="admin-login-link"]').click();
      }
    });
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('adminpassword');
    cy.get('button').contains('Sign In').click();
    
    // Click user dropdown
    cy.get('button').contains('<EMAIL>').click();
    
    // Click sign out
    cy.get('button').contains('Sign Out').click();
    
    // Should return to main page
    cy.url().should('eq', Cypress.config().baseUrl + '/');
    cy.get('button').contains('Login').should('be.visible');
  });
}); 