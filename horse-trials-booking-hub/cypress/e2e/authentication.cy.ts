describe('Authentication', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('allows a user to sign up through the modal', () => {
    // Click the login button to open the auth modal
    cy.get('button').contains('Login').click();
    
    // Switch to sign up tab
    cy.get('[data-testid="tab-signup"]').click();
    
    // Fill out sign up form with random email to avoid duplicates
    const randomEmail = `test${Math.floor(Math.random() * 10000)}@example.com`;
    cy.get('input[id="fullName"]').type('Test User');
    cy.get('input[id="email"]').type(randomEmail);
    cy.get('input[id="password"]').type('Password123!');
    
    // Submit form
    cy.get('[data-testid="signup-submit-button"]').click();
    
    // Verify successful sign up (should show toast)
    cy.contains('Account created successfully').should('be.visible');
  });

  it('allows a user to log in through the modal', () => {
    // Click the login button to open the auth modal
    cy.get('button').contains('Login').click();
    
    // Fill out login form (sign in tab should be active by default)
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('Password123!');
    
    // Submit form
    cy.get('[data-testid="signin-submit-button"]').click();
    
    // Verify successful login (should show toast)
    cy.contains('Signed in successfully').should('be.visible');
  });

  it('shows validation errors for invalid input', () => {
    // Click the login button to open the auth modal
    cy.get('button').contains('Login').click();
    
    // Try to submit without filling required fields
    cy.get('[data-testid="signin-submit-button"]').click();
    
    // Should show validation errors (browser native validation)
    cy.get('input[id="email"]').should('have.attr', 'required');
    cy.get('input[id="password"]').should('have.attr', 'required');
  });

  it('allows switching between sign in and sign up tabs', () => {
    // Click the login button to open the auth modal
    cy.get('button').contains('Login').click();
    
    // Should start on sign in tab
    cy.get('input[id="email"]').should('be.visible');
    cy.get('input[id="password"]').should('be.visible');
    
    // Switch to sign up tab
    cy.get('[data-testid="tab-signup"]').click();
    
    // Should show sign up fields
    cy.get('input[id="fullName"]').should('be.visible');
    cy.get('input[id="email"]').should('be.visible');
    cy.get('input[id="password"]').should('be.visible');
  });

  it('closes the modal when cancel is clicked', () => {
    // Click the login button to open the auth modal
    cy.get('button').contains('Login').click();
    
    // Verify modal is open
    cy.contains('Welcome to Next Rider Up').should('be.visible');
    
    // Click cancel
    cy.get('button').contains('Cancel').click();
    
    // Verify modal is closed
    cy.contains('Welcome to Next Rider Up').should('not.exist');
  });

  it('shows user dropdown when authenticated', () => {
    // First sign in
    cy.get('button').contains('Login').click();
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('Password123!');
    cy.get('[data-testid="signin-submit-button"]').click();
    
    // Wait for authentication to complete
    cy.contains('Signed in successfully').should('be.visible');
    
    // Should show user dropdown instead of login button
    cy.get('button').contains('Login').should('not.exist');
    cy.get('button').contains('<EMAIL>').should('be.visible');
  });

  it('allows user to sign out', () => {
    // First sign in
    cy.get('button').contains('Login').click();
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('Password123!');
    cy.get('[data-testid="signin-submit-button"]').click();
    
    // Wait for authentication to complete
    cy.contains('Signed in successfully').should('be.visible');
    
    // Click user dropdown
    cy.get('button').contains('<EMAIL>').click();
    
    // Click sign out
    cy.get('button').contains('Sign Out').click();
    
    // Should show login button again
    cy.get('button').contains('Login').should('be.visible');
  });
});