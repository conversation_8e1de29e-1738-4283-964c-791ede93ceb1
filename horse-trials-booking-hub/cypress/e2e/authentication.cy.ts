describe('Authentication', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('allows a user to sign up', () => {
    // Navigate to sign up page
    cy.get('nav').contains('Sign Up').click();
    
    // Fill out sign up form with random email to avoid duplicates
    const randomEmail = `test${Math.floor(Math.random() * 10000)}@example.com`;
    cy.get('input[name="name"]').type('Test User');
    cy.get('input[name="email"]').type(randomEmail);
    cy.get('input[name="password"]').type('Password123!');
    cy.get('input[name="confirmPassword"]').type('Password123!');
    
    // Submit form
    cy.get('button').contains('Sign Up').click();
    
    // Verify successful sign up
    cy.contains('Account created successfully').should('be.visible');
  });

  it('allows a user to log in', () => {
    // Navigate to login page
    cy.get('nav').contains('Log In').click();
    
    // Fill out login form
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('Password123!');
    
    // Submit form
    cy.get('button').contains('Log In').click();
    
    // Verify successful login
    cy.contains('Welcome back').should('be.visible');
  });
});