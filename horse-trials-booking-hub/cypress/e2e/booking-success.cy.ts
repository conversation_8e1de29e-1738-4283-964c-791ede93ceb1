describe('Booking Success Flow', () => {
  beforeEach(() => {
    // Mock the booking confirmation API
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 200,
      body: {
        url: 'https://checkout.stripe.com/test',
        sessionId: 'test_session_id'
      }
    }).as('stripeCheckout');
    
    // Mock the booking confirmation endpoint
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 200,
      body: {
        success: true,
        booking: {
          id: 'test-booking-id',
          booking_code: 'TEST123',
          total_price: 45,
          payer_name: 'Test User',
          payer_email: '<EMAIL>'
        },
        message: 'Booking confirmed successfully'
      }
    }).as('bookingConfirmation');
  });

  it('shows booking success page after successful payment', () => {
    // Mock successful Stripe redirect back to success page
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Should show success page
    cy.contains('Booking Confirmed').should('be.visible');
    cy.contains('Thank you for your booking').should('be.visible');
    
    // Should show booking details
    cy.contains('TEST123').should('be.visible'); // Booking code
    cy.contains('Test User').should('be.visible'); // Payer name
    cy.contains('<EMAIL>').should('be.visible'); // Payer email
    cy.contains('$45').should('be.visible'); // Total price
  });

  it('handles booking confirmation API call', () => {
    // Visit success page with session ID
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Should call the booking confirmation API
    cy.wait('@bookingConfirmation');
    
    // Should show success message
    cy.contains('Booking confirmed successfully').should('be.visible');
  });

  it('shows booking details correctly', () => {
    // Mock booking data
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 200,
      body: {
        success: true,
        booking: {
          id: 'test-booking-id',
          booking_code: 'TEST123',
          total_price: 90,
          payer_name: 'John Doe',
          payer_email: '<EMAIL>',
          created_at: '2024-01-15T10:30:00Z'
        },
        message: 'Booking confirmed successfully'
      }
    }).as('bookingConfirmation');
    
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Should display all booking information
    cy.contains('TEST123').should('be.visible');
    cy.contains('John Doe').should('be.visible');
    cy.contains('<EMAIL>').should('be.visible');
    cy.contains('$90').should('be.visible');
  });

  it('shows slot details for the booking', () => {
    // Mock booking with slot details
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 200,
      body: {
        success: true,
        booking: {
          id: 'test-booking-id',
          booking_code: 'TEST123',
          total_price: 45,
          payer_name: 'Test User',
          payer_email: '<EMAIL>',
          booking_slots: [
            {
              id: 'slot1',
              time_slot: '9:00 AM',
              location_name: 'Main Arena',
              activity_type: 'dressage',
              participant_name: 'Test Rider',
              horse_name: 'Test Horse'
            }
          ]
        },
        message: 'Booking confirmed successfully'
      }
    }).as('bookingConfirmation');
    
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Should show slot details
    cy.contains('Main Arena').should('be.visible');
    cy.contains('9:00 AM').should('be.visible');
    cy.contains('dressage').should('be.visible');
    cy.contains('Test Rider').should('be.visible');
    cy.contains('Test Horse').should('be.visible');
  });

  it('provides navigation options after booking', () => {
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Should show navigation buttons
    cy.get('button').contains('Back to Events').should('be.visible');
    cy.get('button').contains('View My Bookings').should('be.visible');
  });

  it('allows user to return to events page', () => {
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Click back to events
    cy.get('button').contains('Back to Events').click();
    
    // Should return to main events page
    cy.url().should('eq', Cypress.config().baseUrl + '/');
    cy.get('[data-testid="events-section"]').should('be.visible');
  });

  it('allows user to view their bookings', () => {
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Click view my bookings
    cy.get('button').contains('View My Bookings').click();
    
    // Should navigate to bookings page
    cy.url().should('include', '/bookings');
    cy.contains('My Bookings').should('be.visible');
  });

  it('handles missing session ID gracefully', () => {
    // Visit success page without session ID
    cy.visit('/booking-success');
    
    // Should show error or redirect
    cy.contains('Invalid booking session').should('be.visible');
  });

  it('handles booking confirmation errors', () => {
    // Mock error response
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 400,
      body: {
        success: false,
        error: 'Booking not found'
      }
    }).as('bookingError');
    
    cy.visit('/booking-success?session_id=invalid_session');
    
    // Should show error message
    cy.contains('Booking not found').should('be.visible');
  });

  it('shows loading state while confirming booking', () => {
    // Mock delayed response
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 200,
      body: {
        success: true,
        booking: {
          id: 'test-booking-id',
          booking_code: 'TEST123',
          total_price: 45,
          payer_name: 'Test User',
          payer_email: '<EMAIL>'
        },
        message: 'Booking confirmed successfully'
      },
      delay: 1000
    }).as('delayedConfirmation');
    
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Should show loading state
    cy.contains('Confirming your booking').should('be.visible');
    
    // Wait for confirmation
    cy.wait('@delayedConfirmation');
    
    // Should show success
    cy.contains('Booking Confirmed').should('be.visible');
  });

  it('displays email confirmation message', () => {
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Should mention email confirmation
    cy.contains('confirmation email').should('be.visible');
    cy.contains('<EMAIL>').should('be.visible');
  });

  it('shows booking management link', () => {
    cy.visit('/booking-success?session_id=test_session_id');
    
    // Should show unique booking management link
    cy.contains('manage your booking').should('be.visible');
    cy.get('a[href*="booking"]').should('be.visible');
  });
}); 