describe('Organizer Login', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('allows organizer to log in successfully and see organizer dashboard option', () => {
    // Click the login button to open the auth modal
    cy.get('button').contains('Login').click();
    
    // Should show the auth modal with tabs
    cy.contains('Welcome to Next Rider Up').should('be.visible');
    cy.contains('Sign In').should('be.visible');
    
    // Fill the regular login form with organizer credentials
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('Fritz0615@@');
    cy.get('[data-testid="signin-submit-button"]').click();
    
    // Should stay on main page (not redirect to dashboard)
    cy.url().should('not.include', '/admin/organizer-dashboard');
    
    // Should see the main page content
    cy.contains('Upcoming Events').should('be.visible');
    
    // Wait a moment for the user state to load
    cy.wait(2000);
    
    // Click on the user dropdown in the header (should show full name)
    cy.get('button').contains('Sally Organizer').click();
    
    // Should show "Organizer Dashboard" in the dropdown menu
    cy.contains('Organizer Dashboard').should('be.visible');
    
    // Should show the user role as "Organizer" in the dropdown
    cy.contains('Organizer').should('be.visible');
  });
}); 