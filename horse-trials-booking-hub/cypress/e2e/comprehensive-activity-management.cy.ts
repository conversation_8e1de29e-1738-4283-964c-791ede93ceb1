describe('Comprehensive Activity and Time Slot Management', () => {
  const eventName = `Comprehensive Test Event ${Date.now()}`;
  const locationName = `Test Arena ${Date.now()}`;
  let eventId: string;
  let locationId: string;

  beforeEach(() => {
    cy.visit('/');
    // Login as organizer
    cy.get('button').contains('Login').click();
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('Fritz0615@@');
    cy.get('[data-testid="signin-submit-button"]').click();
    cy.contains('Upcoming Events').should('be.visible');
  });

  afterEach(() => {
    if (eventName) {
      // Navigate to events list page to find the event
      cy.visit('/admin/events');
      
      // Wait for the event to appear and click the delete button (trash icon)
      cy.get('h3').contains(eventName, { timeout: 20000 }).should('be.visible');
      
      // Find the event card and click the delete button (trash icon)
      cy.get('h3').contains(eventName).closest('.border-green-200').find('button').last().click();
      
      // Confirm deletion in the dialog
      cy.get('[data-testid="delete-event-confirm-button"]')
        .should('be.visible')
        .and('not.be.disabled');
      cy.wait(300);
      cy.get('[data-testid="delete-event-confirm-button"]').click({ force: true });
      
      // Assert the success toast appears
      cy.contains('Event deleted successfully', { timeout: 10000 }).should('be.visible');
      cy.get('[role="dialog"]').should('not.exist');
      cy.get('h3').contains(eventName, { timeout: 20000 }).should('not.exist');
    }
  });

  it('should create event, add location, manage activities, view schedule, and generate time slots', () => {
    // Step 1: Navigate to Organizer Dashboard and create event
    cy.get('button').contains('Sally Organizer').click();
    cy.contains('Organizer Dashboard').click();
    cy.url().should('include', '/admin/organizer-dashboard');
    cy.get('button').contains('Create New Event').click();
    cy.url().should('include', '/admin/events/create');

    // Create event
    cy.get('[data-testid="event-name-input"]').type(eventName);
    cy.get('[data-testid="event-start-date-input"]').type('2025-12-01');
    cy.get('[data-testid="event-end-date-input"]').type('2025-12-02');
    cy.get('[data-testid="create-event-button"]').click();
    cy.contains('Event created successfully').should('be.visible');
    cy.contains(eventName).should('be.visible');
    cy.contains(eventName).click();
    cy.url().should('include', '/admin/events/');
    cy.contains('Location Management').should('be.visible');

    // Step 2: Add location
    cy.get('[data-testid="add-location-button"]').click();
    cy.get('[data-testid="location-name-input"]').type(locationName);
    cy.contains('Select activity').click();
    cy.contains('Dressage').click();
    cy.get('[data-testid="description-input"]').type('Primary dressage competition ring');
    cy.get('[data-testid="create-location-button"]').click();
    cy.contains('Location created successfully').should('be.visible');
    cy.contains(locationName).should('be.visible');
    
    // Get location ID
    cy.contains(locationName).closest('[data-testid^="location-card-"]').invoke('attr', 'data-testid').then((testId) => {
      if (testId) {
        locationId = testId.replace('location-card-', '');
      }
    });

    // Step 3: Expand location and manage activities
    cy.get(`[data-testid="location-card-${locationId}"]`).within(() => {
      cy.get(`[data-testid="expand-location-button-${locationId}"]`).click();
      cy.get('[data-testid="activities-tab"]').click();
    });

    // Step 4: Add first activity
    cy.get('[data-testid="add-activity-button"]').click();
    cy.get('[data-testid="activity-date-input"]').type('2025-12-01');
    cy.get('[data-testid="start-time-input"]').type('09:00');
    cy.get('[data-testid="end-time-input"]').type('10:00');
    cy.get('[data-testid="slot-duration-input"]').type('30');
    cy.get('[data-testid="description-input"]').type('Morning dressage session');
    cy.get('[data-testid="create-activity-button"]').click();
    cy.contains('Morning dressage session').should('be.visible');

    // Step 5: Add second activity
    cy.get('[data-testid="add-activity-button"]').click();
    cy.get('[data-testid="activity-date-input"]').type('2025-12-01');
    cy.get('[data-testid="start-time-input"]').type('10:30');
    cy.get('[data-testid="end-time-input"]').type('11:30');
    cy.get('[data-testid="slot-duration-input"]').type('30');
    cy.get('[data-testid="description-input"]').type('Afternoon dressage session');
    cy.get('[data-testid="create-activity-button"]').click();
    cy.contains('Afternoon dressage session').should('be.visible');

    // Step 6: View schedule
    cy.get('[data-testid="schedule-tab"]').click();
    cy.contains('Morning dressage session').should('be.visible');
    cy.contains('Afternoon dressage session').should('be.visible');

    // Step 7: Generate time slots
    cy.get('[data-testid="timeslots-tab"]').click();
    cy.get('[data-testid="generate-time-slots-btn"]').click();
    cy.get('[data-testid="confirm-action-btn"]').click();
    cy.get('[data-testid^="time-slot-"]').should('have.length.at.least', 2);
    cy.get('[data-testid^="slot-status-"]').each(($status) => {
      cy.wrap($status).should('contain', 'Available');
    });

    cy.log('✅ Complete flow verified: Event created, location added, activities created, schedule viewed, and time slots generated');
  });
}); 