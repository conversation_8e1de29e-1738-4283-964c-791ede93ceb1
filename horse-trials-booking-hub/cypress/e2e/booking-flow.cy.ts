describe('Booking Flow', () => {
  beforeEach(() => {
    // Set up any test data or authentication
    cy.visit('/');
  });

  it('allows a user to browse events and add to cart', () => {
    // Navigate to events page
    cy.get('nav').contains('Events').click();
    
    // Find an event and click on it
    cy.get('[data-testid="event-card"]').first().click();
    
    // Select a slot
    cy.get('[data-testid="slot-item"]').first().within(() => {
      cy.get('button').contains('Add to Cart').click();
    });
    
    // Verify cart icon shows 1 item
    cy.get('[aria-label="Open shopping cart"]').within(() => {
      cy.get('.badge').should('contain', '1');
    });
  });

  it('allows a user to complete checkout process', () => {
    // Set up cart with an item
    cy.visit('/events');
    cy.get('[data-testid="event-card"]').first().click();
    cy.get('[data-testid="slot-item"]').first().within(() => {
      cy.get('button').contains('Add to Cart').click();
    });
    
    // Open cart
    cy.get('[aria-label="Open shopping cart"]').click();
    
    // Proceed to checkout
    cy.get('button').contains('Checkout').click();
    
    // Fill out checkout form
    cy.get('input[name="name"]').type('Test User');
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="phone"]').type('1234567890');
    
    // Complete checkout
    cy.get('button').contains('Complete Booking').click();
    
    // Verify success message
    cy.contains('Booking Confirmed').should('be.visible');
  });
});