describe('Booking Flow', () => {
  beforeEach(() => {
    // Set up any test data or authentication
    cy.visit('/');
  });

  it('allows a user to browse events and view event details', () => {
    // Should be on events page by default
    cy.get('[data-testid="events-section"]').should('be.visible');
    cy.get('[data-testid="events-title"]').should('contain', 'Upcoming Events');
    
    // Find an event and click on it
    cy.get('[data-testid="event-card"]').first().click();
    
    // Should navigate to event details
    cy.get('[data-testid="details-view"]').should('be.visible');
    cy.get('[data-testid="back-button"]').should('be.visible');
  });

  it('allows a user to select time slots and add to cart', () => {
    // Navigate to event details
    cy.get('[data-testid="event-card"]').first().click();
    
    // Wait for event details to load
    cy.get('[data-testid="details-view"]').should('be.visible');
    
    // Select a date if date picker is present
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="date-picker"]').length > 0) {
        cy.get('[data-testid="date-picker"]').first().click();
      }
    });
    
    // Find and click on an available time slot
    cy.get('button').contains('Reserve').first().click();
    
    // Should show floating cart icon with count
    cy.get('[data-testid="floating-cart-icon"]').should('be.visible');
    cy.get('[data-testid="cart-badge"]').should('contain', '1');
  });

  it('allows a user to view cart and proceed to booking', () => {
    // Navigate to event details and add a slot
    cy.get('[data-testid="event-card"]').first().click();
    cy.get('button').contains('Reserve').first().click();
    
    // Click on floating cart icon to open cart
    cy.get('[data-testid="floating-cart-icon"]').click();
    
    // Should show cart with slot details
    cy.get('[data-testid="cart-widget"]').should('be.visible');
    cy.get('[data-testid="proceed-to-booking"]').should('be.visible');
    
    // Proceed to booking
    cy.get('[data-testid="proceed-to-booking"]').click();
    
    // Should navigate to booking flow
    cy.get('[data-testid="booking-view"]').should('be.visible');
    cy.contains('Complete Your Booking').should('be.visible');
  });

  it('allows a user to complete the booking form', () => {
    // Navigate through the booking flow
    cy.get('[data-testid="event-card"]').first().click();
    cy.get('button').contains('Reserve').first().click();
    cy.get('[data-testid="floating-cart-icon"]').click();
    cy.get('[data-testid="proceed-to-booking"]').click();
    
    // Should be on booking form
    cy.contains('Complete Your Booking').should('be.visible');
    cy.contains('Participant Information').should('be.visible');
    cy.contains('Booking Details').should('be.visible');
    
    // Fill out required fields
    cy.get('input[id="payer_name"]').type('Test User');
    cy.get('input[id="payer_email"]').type('<EMAIL>');
    
    // Fill out slot-specific information
    cy.get('input[placeholder*="rider"]').first().type('Test Rider');
    cy.get('input[placeholder*="horse"]').first().type('Test Horse');
    
    // Select dressage test if available
    cy.get('body').then(($body) => {
      if ($body.find('select').length > 0) {
        cy.get('select').first().select('1');
      }
    });
    
    // Verify total price is displayed
    cy.contains('Total:').should('be.visible');
    cy.get('[data-testid="total-price"]').should('be.visible');
    
    // Complete booking button should be enabled
    cy.get('button').contains('Complete Booking').should('not.be.disabled');
  });

  it('handles booking form validation', () => {
    // Navigate to booking form
    cy.get('[data-testid="event-card"]').first().click();
    cy.get('button').contains('Reserve').first().click();
    cy.get('[data-testid="floating-cart-icon"]').click();
    cy.get('[data-testid="proceed-to-booking"]').click();
    
    // Try to submit without filling required fields
    cy.get('button').contains('Complete Booking').click();
    
    // Should show validation errors
    cy.get('input[id="payer_name"]').should('have.attr', 'required');
    cy.get('input[id="payer_email"]').should('have.attr', 'required');
  });

  it('allows user to go back from booking to event details', () => {
    // Navigate to booking form
    cy.get('[data-testid="event-card"]').first().click();
    cy.get('button').contains('Reserve').first().click();
    cy.get('[data-testid="floating-cart-icon"]').click();
    cy.get('[data-testid="proceed-to-booking"]').click();
    
    // Click back button
    cy.get('button').contains('Back to Schedule').click();
    
    // Should return to event details
    cy.get('[data-testid="details-view"]').should('be.visible');
  });

  it('shows slot expiration countdown', () => {
    // Navigate to booking form
    cy.get('[data-testid="event-card"]').first().click();
    cy.get('button').contains('Reserve').first().click();
    cy.get('[data-testid="floating-cart-icon"]').click();
    cy.get('[data-testid="proceed-to-booking"]').click();
    
    // Should show expiration countdown
    cy.contains('Your reservation will expire in:').should('be.visible');
  });

  it('displays correct pricing information', () => {
    // Navigate to booking form
    cy.get('[data-testid="event-card"]').first().click();
    cy.get('button').contains('Reserve').first().click();
    cy.get('[data-testid="floating-cart-icon"]').click();
    cy.get('[data-testid="proceed-to-booking"]').click();
    
    // Should show individual slot prices
    cy.get('[data-testid="slot-price"]').should('be.visible');
    
    // Should show total price
    cy.get('[data-testid="total-price"]').should('be.visible');
    
    // Should show event name and slot count
    cy.contains('Event:').should('be.visible');
    cy.contains('Number of slots:').should('be.visible');
  });

  it('handles Stripe checkout redirect', () => {
    // Mock Stripe checkout response
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 200,
      body: {
        url: 'https://checkout.stripe.com/test',
        sessionId: 'test_session_id'
      }
    }).as('stripeCheckout');
    
    // Navigate to booking form and fill it out
    cy.get('[data-testid="event-card"]').first().click();
    cy.get('button').contains('Reserve').first().click();
    cy.get('[data-testid="floating-cart-icon"]').click();
    cy.get('[data-testid="proceed-to-booking"]').click();
    
    // Fill out form
    cy.get('input[id="payer_name"]').type('Test User');
    cy.get('input[id="payer_email"]').type('<EMAIL>');
    cy.get('input[placeholder*="rider"]').first().type('Test Rider');
    cy.get('input[placeholder*="horse"]').first().type('Test Horse');
    
    // Submit booking
    cy.get('button').contains('Complete Booking').click();
    
    // Should call Stripe checkout
    cy.wait('@stripeCheckout');
    
    // Should redirect to Stripe (in real scenario)
    // For testing, we just verify the API call was made
    cy.get('@stripeCheckout.all').should('have.length', 1);
  });

  it('handles booking errors gracefully', () => {
    // Mock error response
    cy.intercept('POST', '**/create-stripe-checkout-public', {
      statusCode: 400,
      body: {
        error: 'Payment processing failed'
      }
    }).as('stripeError');
    
    // Navigate to booking form and fill it out
    cy.get('[data-testid="event-card"]').first().click();
    cy.get('button').contains('Reserve').first().click();
    cy.get('[data-testid="floating-cart-icon"]').click();
    cy.get('[data-testid="proceed-to-booking"]').click();
    
    // Fill out form
    cy.get('input[id="payer_name"]').type('Test User');
    cy.get('input[id="payer_email"]').type('<EMAIL>');
    cy.get('input[placeholder*="rider"]').first().type('Test Rider');
    cy.get('input[placeholder*="horse"]').first().type('Test Horse');
    
    // Submit booking
    cy.get('button').contains('Complete Booking').click();
    
    // Should show error message
    cy.contains('Payment Error').should('be.visible');
    cy.contains('Payment processing failed').should('be.visible');
  });
});