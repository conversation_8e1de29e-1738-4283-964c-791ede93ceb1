let eventName;

describe('Complete Event Setup: Location → Activity → Time Slots', () => {
  beforeEach(() => {
    cy.visit('/');
    // Login as organizer
    cy.get('button').contains('Login').click();
    cy.get('input[id="email"]').type('<EMAIL>');
    cy.get('input[id="password"]').type('Fritz0615@@');
    cy.get('[data-testid="signin-submit-button"]').click();
    cy.contains('Upcoming Events').should('be.visible');
  });

  afterEach(() => {
    if (eventName) {
      // Navigate to events list page to find the event
      cy.visit('/admin/events');
      
      // Wait for the event to appear and click the delete button (trash icon)
      cy.get('h3').contains(eventName, { timeout: 20000 }).should('be.visible');
      cy.screenshot('before-delete');
      
      // Find the event card and click the delete button (trash icon)
      cy.get('h3').contains(eventName).closest('.border-green-200').find('button').last().click();
      cy.screenshot('after-clicking-delete-button');
      
      // Confirm deletion in the dialog
      cy.get('[data-testid="delete-event-confirm-button"]')
        .should('be.visible')
        .and('not.be.disabled');
      cy.wait(300);
      cy.log('About to click Delete Event');
      cy.get('[data-testid="delete-event-confirm-button"]').click({ force: true });
      cy.screenshot('after-forced-click');
      // Assert the success toast appears
      cy.contains('Event deleted successfully', { timeout: 10000 }).should('be.visible');
      cy.get('[role="dialog"]').should('not.exist');
      cy.screenshot('after-dialog-closed');
      cy.get('h3').contains(eventName, { timeout: 20000 }).should('not.exist');
    }
  });

  it('can create an event and add a location', () => {
    // Step 1: Navigate to Organizer Dashboard
    cy.get('button').contains('Sally Organizer').click();
    cy.contains('Organizer Dashboard').click();
    cy.url().should('include', '/admin/organizer-dashboard');

    // Step 2: Create a new event for testing
    cy.get('button').contains('Create New Event').click();
    cy.url().should('include', '/admin/events/create');

    // Fill out event creation form
    eventName = `Cypress Test Event ${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    cy.get('[data-testid="event-name-input"]').type(eventName);
    cy.get('[data-testid="event-start-date-input"]').type('2025-12-01');
    cy.get('[data-testid="event-end-date-input"]').type('2025-12-02');
    cy.get('[data-testid="create-event-button"]').click();

    // Wait for event creation and navigate to event details
    cy.contains('Event created successfully').should('be.visible');
    cy.contains(eventName).should('be.visible');
    
    // Click on the event to go to details
    cy.contains(eventName).click();
    cy.url().should('include', '/admin/events/');
    
    // Wait for the LocationManagement component to load
    cy.contains('Location Management').should('be.visible');

    // Step 3: Add a Location
    cy.get('[data-testid="add-location-button"]').click();
    cy.get('[data-testid="location-name-input"]').type('Main Competition Ring');
    
    // Handle the activity type dropdown - use placeholder text to find it
    cy.contains('Select activity').click();
    cy.contains('Dressage').click();
    
    cy.get('[data-testid="description-input"]').type('Primary dressage competition ring');
    cy.get('[data-testid="create-location-button"]').click();

    // Verify location was created
    cy.contains('Location created successfully').should('be.visible');
    cy.contains('Main Competition Ring').should('be.visible');
    
    // Click back arrow to return to events list
    cy.get('[data-testid="back-button"]').click();
    cy.url().should('include', '/admin/events');
  });
}); 