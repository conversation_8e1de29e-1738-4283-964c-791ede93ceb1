// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Add global configuration
Cypress.on('window:before:load', (win) => {
  // Mock any global objects that might cause issues in tests
  cy.stub(win.console, 'log').as('consoleLog');
  cy.stub(win.console, 'error').as('consoleError');
  cy.stub(win.console, 'warn').as('consoleWarn');
});

// Global before hook
beforeEach(() => {
  // Clear any existing mocks
  cy.clearAllMocks?.();
  
  // Set up any global test data
  cy.fixture('testData').then((data) => {
    Cypress.env('testData', data);
  });
});

// Global after hook
afterEach(() => {
  // Clean up any test data
  cy.clearTestData();
  
  // Log any console errors that occurred during the test
  cy.get('@consoleError').then((stub) => {
    if (stub.called) {
      cy.log('Console errors during test:', stub.getCalls());
    }
  });
}); 