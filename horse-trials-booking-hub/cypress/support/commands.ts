// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to login as a regular user
       * @example cy.loginAsUser()
       */
      loginAsUser(): Chainable<void>
      
      /**
       * Custom command to login as an admin
       * @example cy.loginAsAdmin()
       */
      loginAsAdmin(): Chainable<void>
      
      /**
       * Custom command to login as a super admin
       * @example cy.loginAsSuperAdmin()
       */
      loginAsSuperAdmin(): Chainable<void>
      
      /**
       * Custom command to login as an organizer
       * @example cy.loginAsOrganizer()
       */
      loginAsOrganizer(): Chainable<void>
      
      /**
       * Custom command to logout
       * @example cy.logout()
       */
      logout(): Chainable<void>
      
      /**
       * Custom command to navigate to event details
       * @example cy.navigateToEventDetails()
       */
      navigateToEventDetails(): Chainable<void>
      
      /**
       * Custom command to add a slot to cart
       * @example cy.addSlotToCart()
       */
      addSlotToCart(): Chainable<void>
      
      /**
       * Custom command to proceed to booking
       * @example cy.proceedToBooking()
       */
      proceedToBooking(): Chainable<void>
      
      /**
       * Custom command to fill booking form
       * @example cy.fillBookingForm({ name: 'Test User', email: '<EMAIL>' })
       */
      fillBookingForm(data: { name: string; email: string; rider?: string; horse?: string }): Chainable<void>
      
      /**
       * Custom command to mock Stripe checkout
       * @example cy.mockStripeCheckout()
       */
      mockStripeCheckout(): Chainable<void>
      
      /**
       * Custom command to mock booking confirmation
       * @example cy.mockBookingConfirmation()
       */
      mockBookingConfirmation(): Chainable<void>
      
      /**
       * Custom command to clear test data
       * @example cy.clearTestData()
       */
      clearTestData(): Chainable<void>
    }
  }
}

// Test user credentials
const testUsers = {
  user: { email: '<EMAIL>', password: 'userpassword' },
  admin: { email: '<EMAIL>', password: 'adminpassword' },
  superAdmin: { email: '<EMAIL>', password: 'superadminpassword' },
  organizer: { email: '<EMAIL>', password: 'Fritz0615@@' }
};

// Authentication commands
Cypress.Commands.add('loginAsUser', () => {
  cy.visit('/');
  cy.get('button').contains('Login').click();
  cy.get('input[id="email"]').type(testUsers.user.email);
  cy.get('input[id="password"]').type(testUsers.user.password);
  cy.get('[data-testid="signin-submit-button"]').click();
  cy.contains('Signed in successfully').should('be.visible');
});

Cypress.Commands.add('loginAsAdmin', () => {
  cy.visit('/');
  cy.get('button').contains('Login').click();
  cy.get('body').then(($body) => {
    if ($body.find('button').contains('Admin Login').length > 0) {
      cy.get('button').contains('Admin Login').click();
    } else {
      cy.get('[data-testid="admin-login-link"]').click();
    }
  });
  cy.get('input[name="email"]').type(testUsers.admin.email);
  cy.get('input[name="password"]').type(testUsers.admin.password);
  cy.get('button').contains('Sign In').click();
  cy.url().should('include', '/admin');
});

Cypress.Commands.add('loginAsSuperAdmin', () => {
  cy.visit('/');
  cy.get('button').contains('Login').click();
  cy.get('body').then(($body) => {
    if ($body.find('button').contains('Admin Login').length > 0) {
      cy.get('button').contains('Admin Login').click();
    } else {
      cy.get('[data-testid="admin-login-link"]').click();
    }
  });
  cy.get('input[name="email"]').type(testUsers.superAdmin.email);
  cy.get('input[name="password"]').type(testUsers.superAdmin.password);
  cy.get('button').contains('Sign In').click();
  cy.url().should('include', '/admin/super-admin-dashboard');
});

Cypress.Commands.add('loginAsOrganizer', () => {
  cy.visit('/');
  cy.get('button').contains('Login').click();
  cy.get('body').then(($body) => {
    if ($body.find('button').contains('Admin Login').length > 0) {
      cy.get('button').contains('Admin Login').click();
    } else {
      cy.get('[data-testid="admin-login-link"]').click();
    }
  });
  cy.get('input[name="email"]').type(testUsers.organizer.email);
  cy.get('input[name="password"]').type(testUsers.organizer.password);
  cy.get('button').contains('Sign In').click();
  cy.url().should('include', '/admin/organizer-dashboard');
});

Cypress.Commands.add('logout', () => {
  cy.get('button').contains(testUsers.user.email).click();
  cy.get('button').contains('Sign Out').click();
  cy.get('button').contains('Login').should('be.visible');
});

// Navigation commands
Cypress.Commands.add('navigateToEventDetails', () => {
  cy.get('[data-testid="event-card"]').first().click();
  cy.get('[data-testid="details-view"]').should('be.visible');
});

Cypress.Commands.add('addSlotToCart', () => {
  cy.get('button').contains('Reserve').first().click();
  cy.get('[data-testid="floating-cart-icon"]').should('be.visible');
  cy.get('[data-testid="cart-badge"]').should('contain', '1');
});

Cypress.Commands.add('proceedToBooking', () => {
  cy.get('[data-testid="floating-cart-icon"]').click();
  cy.get('[data-testid="proceed-to-booking"]').click();
  cy.get('[data-testid="booking-view"]').should('be.visible');
});

// Form filling commands
Cypress.Commands.add('fillBookingForm', (data) => {
  cy.get('input[id="payer_name"]').type(data.name);
  cy.get('input[id="payer_email"]').type(data.email);
  
  if (data.rider) {
    cy.get('input[placeholder*="rider"]').first().type(data.rider);
  }
  
  if (data.horse) {
    cy.get('input[placeholder*="horse"]').first().type(data.horse);
  }
  
  // Select dressage test if available
  cy.get('body').then(($body) => {
    if ($body.find('select').length > 0) {
      cy.get('select').first().select('1');
    }
  });
});

// Mock commands
Cypress.Commands.add('mockStripeCheckout', () => {
  cy.intercept('POST', '**/create-stripe-checkout-public', {
    statusCode: 200,
    body: {
      url: 'https://checkout.stripe.com/test',
      sessionId: 'test_session_id'
    }
  }).as('stripeCheckout');
});

Cypress.Commands.add('mockBookingConfirmation', () => {
  cy.intercept('POST', '**/create-stripe-checkout-public', {
    statusCode: 200,
    body: {
      success: true,
      booking: {
        id: 'test-booking-id',
        booking_code: 'TEST123',
        total_price: 45,
        payer_name: 'Test User',
        payer_email: '<EMAIL>'
      },
      message: 'Booking confirmed successfully'
    }
  }).as('bookingConfirmation');
});

// Data management commands
Cypress.Commands.add('clearTestData', () => {
  // This would typically call an API to clean up test data
  // For now, we'll just log that cleanup would happen
  cy.log('Clearing test data...');
});

// Global configuration
beforeEach(() => {
  // Set viewport for consistent testing
  cy.viewport(1280, 720);
});

// Global error handling
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents Cypress from failing the test
  // for uncaught exceptions that are expected in some scenarios
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false;
  }
  return true;
});

export {}; 