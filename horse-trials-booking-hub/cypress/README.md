# E2E Testing with Cypress

This directory contains the end-to-end testing setup for the Next Rider Up schooling scheduling application using Cypress.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- Application running on `http://localhost:5173`
- Supabase local development environment (optional)

### Installation
```bash
# Install Cypress (if not already installed)
npm install cypress --save-dev

# Open Cypress UI
npm run test:e2e:open

# Run tests in headless mode
npm run test:e2e
```

## 📁 Directory Structure

```
cypress/
├── e2e/                    # Test files
│   ├── authentication.cy.ts
│   ├── booking-flow.cy.ts
│   ├── admin-flow.cy.ts
│   └── booking-success.cy.ts
├── fixtures/               # Test data
│   └── testData.json
├── support/                # Custom commands and configuration
│   ├── commands.ts
│   └── index.ts
└── README.md              # This file
```

## 🧪 Test Files

### 1. Authentication (`authentication.cy.ts`)
Tests user authentication flows including:
- User sign up through modal
- User sign in through modal
- Form validation
- Tab switching
- User dropdown functionality
- Sign out

### 2. Booking Flow (`booking-flow.cy.ts`)
Tests the complete booking process:
- Event browsing and selection
- Time slot reservation
- Cart management
- Booking form completion
- Stripe checkout integration
- Error handling

### 3. Admin Flow (`admin-flow.cy.ts`)
Tests admin functionality:
- Admin login and authentication
- Role-based dashboard access
- Event management
- User management (Super Admin)
- Access control

### 4. Booking Success (`booking-success.cy.ts`)
Tests post-payment flows:
- Success page display
- Booking confirmation
- Booking details display
- Navigation options
- Error handling

## 🔧 Custom Commands

The following custom commands are available in all tests:

### Authentication
```javascript
cy.loginAsUser()           // Login as regular user
cy.loginAsAdmin()          // Login as admin
cy.loginAsSuperAdmin()     // Login as super admin
cy.loginAsOrganizer()      // Login as organizer
cy.logout()                // Logout current user
```

### Navigation
```javascript
cy.navigateToEventDetails()  // Navigate to event details page
cy.addSlotToCart()           // Add a time slot to cart
cy.proceedToBooking()        // Proceed to booking form
```

### Form Filling
```javascript
cy.fillBookingForm({
  name: 'Test User',
  email: '<EMAIL>',
  rider: 'Test Rider',
  horse: 'Test Horse'
})
```

### Mocking
```javascript
cy.mockStripeCheckout()      // Mock Stripe checkout response
cy.mockBookingConfirmation() // Mock booking confirmation
```

## 📊 Test Data

Test data is stored in `cypress/fixtures/testData.json` and includes:
- User credentials for different roles
- Sample events and locations
- Time slot configurations
- Booking examples
- Pricing information

## 🎯 Running Tests

### Run All Tests
```bash
npm run test:e2e
```

### Run Specific Test File
```bash
npx cypress run --spec "cypress/e2e/authentication.cy.ts"
```

### Run Tests in Different Browsers
```bash
npx cypress run --browser chrome
npx cypress run --browser firefox
npx cypress run --browser edge
```

### Open Cypress UI
```bash
npm run test:e2e:open
```

## 🔧 Configuration

### Cypress Configuration (`cypress.config.ts`)
- Base URL: `http://localhost:5173`
- Viewport: 1280x720
- Video recording: Disabled
- Screenshots: Enabled on failure
- Retries: 2 for run mode, 0 for open mode

### Environment Variables
```bash
CYPRESS_BASE_URL=http://localhost:5173
CYPRESS_API_URL=http://localhost:54321
CYPRESS_STRIPE_PUBLIC_KEY=pk_test_...
```

## 🚨 Troubleshooting

### Common Issues

1. **Tests failing due to missing elements**
   - Check if the application is running on the correct port
   - Verify that test data exists in the database
   - Check for UI changes that might affect selectors

2. **Authentication issues**
   - Ensure test user accounts exist in the database
   - Check if authentication endpoints are working
   - Verify environment variables are set correctly

3. **Stripe integration issues**
   - Mock Stripe responses in tests
   - Use test API keys
   - Check webhook configurations

4. **Slow test execution**
   - Reduce viewport size
   - Disable video recording
   - Use `cy.wait()` sparingly

### Debug Mode
```bash
# Run with debug logging
DEBUG=cypress:* npm run test:e2e

# Open browser dev tools during test
cy.debug()
```

## 📈 Best Practices

### Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Keep tests independent and isolated
- Clean up test data after each test

### Selectors
- Prefer `data-testid` attributes for stable selectors
- Use semantic selectors when possible
- Avoid CSS classes that might change
- Use `cy.get()` with specific selectors

### Performance
- Mock external API calls
- Use `cy.intercept()` for network requests
- Minimize page loads and navigation
- Use `cy.fixture()` for test data

### Reliability
- Add appropriate waits for async operations
- Handle loading states
- Test error scenarios
- Use retries for flaky tests

## 🔄 Continuous Integration

### GitHub Actions Example
```yaml
name: E2E Tests
on: [push, pull_request]
jobs:
  cypress:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: cypress-io/github-action@v6
        with:
          start: npm run dev
          wait-on: 'http://localhost:5173'
          spec: cypress/e2e/**/*.cy.ts
```

## 📝 Adding New Tests

1. **Create test file** in `cypress/e2e/`
2. **Add test data** to `cypress/fixtures/testData.json` if needed
3. **Create custom commands** in `cypress/support/commands.ts` for reusable actions
4. **Update test plan** in `cypress/e2e-test-plan.md`
5. **Run tests** to ensure they pass

### Example Test Structure
```javascript
describe('Feature Name', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should perform expected action', () => {
    // Arrange
    cy.loginAsUser();
    
    // Act
    cy.navigateToEventDetails();
    cy.addSlotToCart();
    
    // Assert
    cy.get('[data-testid="cart-badge"]').should('contain', '1');
  });
});
```

## 📚 Resources

- [Cypress Documentation](https://docs.cypress.io/)
- [Cypress Best Practices](https://docs.cypress.io/guides/references/best-practices)
- [Testing Library Guidelines](https://testing-library.com/docs/guiding-principles)
- [E2E Testing Strategy](https://kentcdodds.com/blog/write-tests)

## 🤝 Contributing

When adding new tests:
1. Follow the existing naming conventions
2. Add appropriate comments and documentation
3. Ensure tests are reliable and not flaky
4. Update the test plan document
5. Test on multiple browsers if needed 