-- Debug script to check time slots and their dates
SELECT 
  'Time Slots' as table_name,
  COUNT(*) as total_count,
  MIN(DATE(start_time)) as earliest_date,
  MAX(DATE(start_time)) as latest_date
FROM time_slots;

-- Show time slots by date
SELECT 
  DATE(start_time) as date,
  COUNT(*) as slot_count,
  MIN(start_time) as first_slot,
  MAX(end_time) as last_slot
FROM time_slots 
GROUP BY DATE(start_time)
ORDER BY date;

-- Show activities by date
SELECT 
  DATE(start_time) as date,
  COUNT(*) as activity_count,
  MIN(start_time) as first_activity,
  MAX(end_time) as last_activity
FROM activities 
GROUP BY DATE(start_time)
ORDER BY date;

-- Check the event dates
SELECT 
  name,
  schooling_start_date,
  schooling_end_date
FROM events 
WHERE id = '76e88ec6-1ff2-4703-94b8-35687e56ff90'; 