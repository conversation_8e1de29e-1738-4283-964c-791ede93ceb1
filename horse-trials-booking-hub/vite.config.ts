import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  define: {
    'process.env.REACT_APP_ALLOW_EVENT_DELETE_WITH_BOOKINGS': JSON.stringify(
      process.env.REACT_APP_ALLOW_EVENT_DELETE_WITH_BOOKINGS || 'false'
    ),
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});