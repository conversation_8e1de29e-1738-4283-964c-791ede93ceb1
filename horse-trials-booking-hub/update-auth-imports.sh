#!/bin/bash

# Update all remaining useAuth imports to useSession
echo "Updating remaining useAuth imports..."

# Files that need updating
files=(
  "src/pages/EventManagementPage.tsx"
  "src/pages/AdminSettingsPage.tsx"
  "src/pages/ArenaManagementPage.tsx"
  "src/pages/CreateEventPage.tsx"
  "src/pages/AdminDashboardPage.tsx"
  "src/pages/ProfilePage.tsx"
  "src/pages/SuperAdminDashboardPage.tsx"
)

for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "Updating $file..."
    
    # Replace useAuth import with useSession
    sed -i '' 's/import { useAuth } from '\''@\/hooks\/useAuth'\'';/import { useSession } from '\''@supabase\/auth-helpers-react'\'';/g' "$file"
    
    # Replace useAuth() call with useSession()
    sed -i '' 's/const { user, userRole } = useAuth();/const session = useSession();\n  const user = session?.user;\n  const { userRole } = useUserRole(!!user);/g' "$file"
    sed -i '' 's/const { user } = useAuth();/const session = useSession();\n  const user = session?.user;/g' "$file"
    sed -i '' 's/const { signIn, userRole, user, loading } = useAuth();/const session = useSession();\n  const user = session?.user;\n  const { userRole } = useUserRole(!!user);\n  const [loading, setLoading] = useState(false);/g' "$file"
    
    # Add useUserRole import if not present
    if ! grep -q "useUserRole" "$file"; then
      sed -i '' 's/import { useSession } from '\''@supabase\/auth-helpers-react'\'';/import { useSession } from '\''@supabase\/auth-helpers-react'\'';\nimport { useUserRole } from '\''@\/hooks\/useUserRoles'\'';/g' "$file"
    fi
    
    # Add useState import if not present
    if ! grep -q "useState" "$file"; then
      sed -i '' 's/import React from '\''react'\'';/import React, { useState } from '\''react'\'';/g' "$file"
    fi
  fi
done

echo "Done updating auth imports!" 